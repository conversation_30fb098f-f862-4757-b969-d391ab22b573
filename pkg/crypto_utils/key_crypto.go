package crypto_utils

import (
	"crypto/sha256"
)

func EncryptDB<PERSON>ey(plainKey, liteLLM<PERSON>ey string) (string, error) {
	// 通过 SHA256 将密钥扩充到 32 字节，满足 AES 对密钥长度的要求
	secret := sha256.Sum256([]byte(liteLLMKey))
	return Encrypt(secret[:], plainKey)
}

func DecryptDBKey(cipherKey, liteLLMKey string) (string, error) {
	secret := sha256.Sum256([]byte(liteLLMKey))
	return Decrypt(secret[:], cipherKey)
} 