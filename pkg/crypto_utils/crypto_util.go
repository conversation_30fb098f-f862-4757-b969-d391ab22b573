package crypto_utils

import (
	"bytes"
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"encoding/base64"
	"fmt"
	"io"
)

func Encrypt(key []byte, text string) (res string, err error) {
	defer func() {
		if r := recover(); r != nil {
			res = ""
			if e, ok := r.(error); ok {
				err = e
			} else {
				err = fmt.Errorf("panic: %v", r)
			}
		}
	}()

	// 生成随机 header (16 bytes)
	header := make([]byte, 16)
	if _, err = io.ReadFull(rand.Reader, header); err != nil {
		return
	}

	// 创建 AES cipher
	block, err := aes.NewCipher(key)
	if err != nil {
		return
	}

	// 创建 16 字节的 nonce
	nonce := make([]byte, 16)
	if _, err = io.ReadFull(rand.Reader, nonce); err != nil {
		return
	}

	// 使用 16 字节的 nonce 创建 GCM
	gcm, err := cipher.NewGCMWithNonceSize(block, 16)
	if err != nil {
		return
	}

	// 加密
	ciphertext := gcm.Seal(nil, nonce, []byte(text), header)

	// 提取 tag（在 Go 中，tag 是 ciphertext 的最后 16 字节）
	tag := ciphertext[len(ciphertext)-16:]
	ciphertext = ciphertext[:len(ciphertext)-16]

	// 编码结果
	result := make([]string, 4)
	result[0] = base64.StdEncoding.EncodeToString(header)
	result[1] = base64.StdEncoding.EncodeToString(nonce)
	result[2] = base64.StdEncoding.EncodeToString(tag)
	result[3] = base64.StdEncoding.EncodeToString(ciphertext)
	res = result[0] + result[1] + result[2] + result[3]
	return
}

func Decrypt(key []byte, text string) (res string, err error) {
	defer func() {
		if r := recover(); r != nil {
			res = ""
			if e, ok := r.(error); ok {
				err = e
			} else {
				err = fmt.Errorf("panic: %v", r)
			}
		}
	}()
	metadata := text[:72]
	header, err := base64.StdEncoding.DecodeString(metadata[:24])
	if err != nil {
		return
	}
	nonce, err := base64.StdEncoding.DecodeString(metadata[24:48])
	if err != nil {
		return
	}
	tag, err := base64.StdEncoding.DecodeString(metadata[48:])
	if err != nil {
		return
	}
	ciphertext, err := base64.StdEncoding.DecodeString(text[72:])
	if err != nil {
		return
	}
	block, err := aes.NewCipher(key)
	if err != nil {
		return
	}
	aesgcm, err := cipher.NewGCMWithNonceSize(block, len(nonce))
	if err != nil {
		return
	}
	plaintext, err := aesgcm.Open(nil, nonce, append(ciphertext, tag...), header)
	if err != nil {
		return
	}
	res = string(plaintext)
	return
}

func padKeyTo32Bytes(key []byte) []byte {
	paddingSize := 32 - len(key)%32                                 // 计算需要填充的字节数
	padding := bytes.Repeat([]byte{byte(paddingSize)}, paddingSize) // 创建填充字节切片
	return append(key, padding...)                                  // 将填充字节追加到原始密钥后
}
