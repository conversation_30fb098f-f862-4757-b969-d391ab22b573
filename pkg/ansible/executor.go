package ansible

import (
	"bytes"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"io"
	"io/ioutil"
	"os"
	"path/filepath"
	"strings"

	"github.com/apenella/go-ansible/v2/pkg/execute"
	"github.com/apenella/go-ansible/v2/pkg/execute/configuration"
	"github.com/apenella/go-ansible/v2/pkg/execute/measure"
	"github.com/apenella/go-ansible/v2/pkg/playbook"
)

func GetCurrentDir() string {
	exePath, _ := os.Executable()
	dir := filepath.Dir(exePath)
	return dir
}
func GetPrivateKeyPath(privateKeyContent string) string {
	currentDir := GetCurrentDir()
	keyDir := filepath.Join(currentDir, "ansible", ".keys")

	// 确保目录存在
	if err := os.MkdirAll(keyDir, 0700); err != nil {
		return ""
	}
	h := sha256.New()
	h.Write([]byte(privateKeyContent))
	keyHash := hex.EncodeToString(h.Sum(nil))

	keyPath := filepath.Join(keyDir, keyHash+".key")

	if _, err := os.Stat(keyPath); os.IsNotExist(err) {
		if err := ioutil.WriteFile(keyPath, []byte(privateKeyContent), 0600); err != nil {
			return ""
		}
	} else if err != nil {
		return ""
	} else {
		if err := os.Chmod(keyPath, 0600); err != nil {
			return ""
		}
	}

	return keyPath
}

func makeProxyCommand(gateway *Gateway) string {
	proxyCommandList := []string{
		"ssh", "-o", fmt.Sprintf("Port=%d", gateway.Port),
		"-o", "StrictHostKeyChecking=no",
		fmt.Sprintf("%s@%s", gateway.Username, gateway.Ip),
		"-W", "%h:%p", "-q",
	}
	if gateway.Password != "" {
		sshpass := fmt.Sprintf("sshpass -p %s", gateway.Password)
		proxyCommandList = append([]string{sshpass}, proxyCommandList...)
	}
	if gateway.PrivateKey != "" {
		privateKey := fmt.Sprintf("-i %s", GetPrivateKeyPath(gateway.PrivateKey))
		proxyCommandList = append(proxyCommandList, privateKey)
	}
	proxyCommand := fmt.Sprintf("-o ProxyCommand=\"%s\"", strings.Join(proxyCommandList, " "))

	return proxyCommand
}

func generatePlaybookOptions(gateway *Gateway, opts *ExecuteOption) *playbook.AnsiblePlaybookOptions {
	inventory := opts.Hosts + ","

	optsPrivateKeyPath := GetPrivateKeyPath(opts.PrivateKey)
	ansiblePlaybookOptions := &playbook.AnsiblePlaybookOptions{
		ExtraVars:    opts.Variables,
		User:         opts.User,
		PrivateKey:   optsPrivateKeyPath,
		Inventory:    inventory,
		Become:       true,
		BecomeMethod: "sudo",
		BecomeUser:   "root",
		Connection:   "smart",
		Forks:        "1",
		Timeout:      20,
		SSHExtraArgs: "-o StrictHostKeyChecking=no",
	}
	if gateway.Ip != "" {
		ansiblePlaybookOptions.SSHCommonArgs = makeProxyCommand(gateway)
	}
	return ansiblePlaybookOptions
}

func CreateExecutor(gateway *Gateway, playbooks string, opts *ExecuteOption, buff *bytes.Buffer) *measure.ExecutorTimeMeasurement {
	// 生成ansible参数
	ansiblePlaybookOptions := generatePlaybookOptions(gateway, opts)

	currentDir := GetCurrentDir()
	playbooksPath := filepath.Join(currentDir, "ansible", "templates", playbooks)

	playbookCmd := playbook.NewAnsiblePlaybookCmd(
		playbook.WithPlaybooks(playbooksPath),
		playbook.WithPlaybookOptions(ansiblePlaybookOptions),
	)

	exec := measure.NewExecutorTimeMeasurement(
		configuration.NewAnsibleWithConfigurationSettingsExecute(
			execute.NewDefaultExecute(
				execute.WithCmd(playbookCmd),
				execute.WithErrorEnrich(playbook.NewAnsiblePlaybookErrorEnrich()),
				execute.WithWrite(io.Writer(buff)),
				execute.WithWriteError(io.Writer(buff)),
			),
			configuration.WithAnsibleForceColor(),
		),
	)
	return exec
}
