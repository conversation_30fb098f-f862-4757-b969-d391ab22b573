- hosts: demo
  gather_facts: no
  tasks:
    - name: "Test privileged {{ username }} account"
      ansible.builtin.ping:

    - name: "Check if {{ username }} user exists"
      getent:
        database: passwd
        key: "{{ username }}"
      register: user_info
      ignore_errors: yes

    - name: "Add {{ username }} user"
      ansible.builtin.user:
        name: "{{ username }}"
        shell: "/bin/bash"
        home: "/home/<USER>"
        expires: -1
        state: present
      when: user_info.failed

    - name: "Add {{ username }} group"
      ansible.builtin.group:
        name: "{{ username }}"
        state: present
      when: user_info.failed

    - name: "Add {{ username }} user to group"
      ansible.builtin.user:
        name: "{{ username }}"
        groups: "{{ username }}"
      when:
        - user_info.failed

    - name: "Change {{ username }} SSH key"
      ansible.builtin.authorized_key:
        user: "{{ username }}"
        key: "{{ public_key }}"


    - name: "Set {{ username }} sudo setting"
      ansible.builtin.lineinfile:
        dest: /etc/sudoers
        state: present
        regexp: "^{{ username }} ALL="
        line: "{{ username + ' ALL=(ALL) NOPASSWD: ' + sudo_setting }}"
        validate: visudo -cf %s
