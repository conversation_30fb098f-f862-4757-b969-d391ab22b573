package ansible

import (
	"bufio"
	"fmt"
	"regexp"
	"strings"
)

type AnsibleResult struct {
	Host        string
	Ok          int
	Changed     int
	Unreachable int
	Failed      int
	Skipped     int
	Rescued     int
	Ignored     int
}

func removeANSIColors(text string) string {
	// 匹配ANSI颜色代码的正则表达式
	ansiRegex := regexp.MustCompile(`\x1b\[[0-9;]*[a-zA-Z]`)
	return ansiRegex.ReplaceAllString(text, "")
}
func GetAnsibleOutputResult(ansibleOutput string) bool {
	if ansibleOutput == "" {
		return false
	}
	result := parseAnsibleOutput(ansibleOutput)
	isSucceed := result.Unreachable == 0 && result.Failed == 0
	return isSucceed
}

func parseAnsibleOutput(output string) AnsibleResult {
	var results AnsibleResult
	cleanOutput := removeANSIColors(output)

	recapIndex := strings.Index(cleanOutput, "PLAY RECAP")
	if recapIndex == -1 {
		return results
	}
	// 获取 "PLAY RECAP" 之后的行
	recapSection := cleanOutput[recapIndex:]
	scanner := bufio.NewScanner(strings.NewReader(recapSection))
	scanner.Scan()

	recapRegex := regexp.MustCompile(`(\S+)\s+: ok=(\d+)\s+changed=(\d+)\s+unreachable=(\d+)\s+failed=(\d+)\s+skipped=(\d+)\s+rescued=(\d+)\s+ignored=(\d+)`)

	for scanner.Scan() {
		line := scanner.Text()
		if len(strings.TrimSpace(line)) == 0 {
			continue
		}

		matches := recapRegex.FindStringSubmatch(line)
		fmt.Println(222, matches, "scanner", scanner.Text())
		if len(matches) != 9 {
			continue
		}

		result := AnsibleResult{
			Host:        matches[1],
			Ok:          parseInt(matches[2]),
			Changed:     parseInt(matches[3]),
			Unreachable: parseInt(matches[4]),
			Failed:      parseInt(matches[5]),
			Skipped:     parseInt(matches[6]),
			Rescued:     parseInt(matches[7]),
			Ignored:     parseInt(matches[8]),
		}
		return result
	}
	return results
}

func parseInt(s string) int {
	var val int
	_, err := fmt.Sscanf(s, "%d", &val)
	if err != nil {
		return 0
	}
	return val
}
