package ssh_utils

import (
	"crypto/rand"
	"crypto/rsa"
	"crypto/x509"
	"encoding/pem"
	"golang.org/x/crypto/ssh"
)

func GenerateSSHKeyPair() (privateKey, publicKey string, err error) {
	// 生成 4096 位 RSA 密钥对
	key, err := rsa.GenerateKey(rand.Reader, 4096)
	if err != nil {
		return "", "", err
	}

	// 私钥编码为PEM格式
	privateKeyBytes := x509.MarshalPKCS1PrivateKey(key)
	privateKeyPEM := pem.EncodeToMemory(&pem.Block{
		Type:  "RSA PRIVATE KEY",
		Bytes: privateKeyBytes,
	})

	// 使用 "crypto/ssh" 来生成 OpenSSH 格式公钥
	pub, err := ssh.NewPublicKey(&key.PublicKey)
	if err != nil {
		return "", "", err
	}
	// 将公钥格式化为 OpenSSH 字符串
	publicKeySSH := string(ssh.MarshalAuthorized<PERSON>ey(pub))

	return string(privateKeyPEM), publicKeySSH, nil
}
