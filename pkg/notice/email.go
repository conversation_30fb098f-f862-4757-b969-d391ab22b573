package notice

import (
	"crypto/tls"
	"fmt"
	"gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/internal/conf"
	"log"
	"net"
	"net/smtp"
	"strings"
)

func SendEmail(email *conf.EmailConfig, address []string, subject, body string) error {
	header := make(map[string]string)
	header["From"] = email.Nickname + " <" + email.User + ">"
	header["To"] = strings.Join(address, ";")
	header["Subject"] = subject
	header["Content-Type"] = "text/plain; charset=UTF-8"
	message := ""
	for k, v := range header {
		message += fmt.Sprintf("%s: %s\r\n", k, v)
	}
	message += "\r\n" + body
	auth := smtp.PlainAuth(
		"",
		email.User,
		email.Pwd,
		email.Host,
	)
	err := SendMailWithTLS(
		fmt.Sprintf("%s:%s", email.Host, email.Port),
		auth,
		email.User,
		address,
		[]byte(message),
	)
	if err != nil {
		fmt.Println("Send email error:", err)
		return err
	} else {
		fmt.Println("Send mail success!")
		return nil
	}
}

func SendEmailHTML(email *conf.EmailConfig, address []string, subject, body string) error {
	header := make(map[string]string)
	header["From"] = email.Nickname + " <" + email.User + ">"
	header["To"] = strings.Join(address, ";")
	header["Subject"] = subject
	header["Content-Type"] = "text/html; charset=UTF-8"

	auth := smtp.PlainAuth(
		"",
		email.User,
		email.Pwd,
		email.Host,
	)
	message := ""
	for k, v := range header {
		message += fmt.Sprintf("%s: %s\r\n", k, v)
	}
	message += "\r\n" + body
	err := SendMailWithTLS(
		fmt.Sprintf("%s:%s", email.Host, email.Port),
		auth,
		email.User,
		address,
		[]byte(message),
	)
	if err != nil {
		fmt.Println("Send email error:", err)
		return err
	} else {
		fmt.Println("Send mail success!")
		return nil
	}
}

// Dial return a smtp client
func Dial(addr string) (*smtp.Client, error) {
	conn, err := tls.Dial("tcp", addr, nil)
	if err != nil {
		log.Println("tls.Dial Error:", err)
		return nil, err
	}

	host, _, _ := net.SplitHostPort(addr)
	return smtp.NewClient(conn, host)
}

// SendMailWithTLS send email with tls
func SendMailWithTLS(addr string, auth smtp.Auth, from string,
	to []string, msg []byte) (err error) {
	//create smtp client
	c, err := Dial(addr)
	if err != nil {
		log.Println("Create smtp client error:", err)
		return err
	}
	defer c.Close()
	if auth != nil {
		if ok, _ := c.Extension("AUTH"); ok {
			if err = c.Auth(auth); err != nil {
				log.Println("Error during AUTH", err)
				return err
			}
		}
	}
	if err = c.Mail(from); err != nil {
		return err
	}
	for _, addr := range to {
		if err = c.Rcpt(addr); err != nil {
			return err
		}
	}
	w, err := c.Data()
	if err != nil {
		return err
	}
	_, err = w.Write(msg)
	if err != nil {
		return err
	}
	err = w.Close()
	if err != nil {
		return err
	}
	return c.Quit()
}
