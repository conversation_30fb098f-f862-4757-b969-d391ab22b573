package notice

import (
	"fmt"
	"github.com/imroc/req/v3"
	"math/rand"
	"strings"
	"time"
)

type SmsConfig struct {
	UserId   string
	Password string
	Url      string
}

func GenValidateCode(width int) string {
	numeric := [10]byte{0, 1, 2, 3, 4, 5, 6, 7, 8, 9}
	r := len(numeric)
	rand.Seed(time.Now().UnixNano())

	var sb strings.Builder
	for i := 0; i < width; i++ {
		fmt.Fprintf(&sb, "%d", numeric[rand.Intn(r)])
	}
	return sb.String()
}

func SendPhoneSMS(smsConfig *SmsConfig, phone, content string) error {
	if phone == "" {
		return fmt.Errorf("phone is empty")
	}
	if content == "" {
		return fmt.Errorf("content is empty")
	}
	client := req.C().SetTimeout(2 * time.Second)
	_, err := client.R().SetQueryParams(map[string]string{
		"userId":     smsConfig.UserId,
		"password":   smsConfig.Password,
		"pszMobis":   phone,
		"iMobiCount": "1",
		"pszMsg":     content,
	}).Get(smsConfig.Url)
	if err != nil {
		return err
	}
	return nil
}
