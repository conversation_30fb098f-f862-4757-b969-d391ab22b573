package common

import (
	"context"
	"errors"
	"flag"
	"strings"

	"github.com/go-kratos/kratos/v2/config"
	"github.com/go-kratos/kratos/v2/config/file"
	"github.com/go-kratos/kratos/v2/transport"
	"github.com/golang-jwt/jwt"

	userBiz "gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/internal/biz/user"
)

// 分页
func ListPage(totalCount, pageSize int64) int64 {
	var totalPage int64
	if totalCount > 0 && pageSize != 0 && totalCount%pageSize == 0 {
		totalPage = totalCount / pageSize
	} else if pageSize != 0 {
		totalPage = totalCount/pageSize + 1
	}
	return totalPage
}

// 获取token值
func GetUserInfo(ctx context.Context) (*UserInfo, error) {
	tr, _ := transport.FromServerContext(ctx)
	authorizationHeader := tr.RequestHeader().Get("Authorization")
	var token string
	if authorizationHeader != "" {
		splitStrs := strings.Split(authorizationHeader, " ")
		if len(splitStrs) < 2 {
			return nil, errors.New("invalid authorization header")
		}
		token = splitStrs[1]
	} else {
		return nil, errors.New("missing authorization header")
	}
	return GetTokenToUserInfo(token)
}

func GetTokenToUserInfo(token string) (*UserInfo, error) {
	tokenParse, _ := jwt.Parse(token, func(token *jwt.Token) (interface{}, error) {
		return userBiz.JwtSecret, nil
	})
	ssoInfo := make(map[string]interface{}, 1)
	if claims, ok := tokenParse.Claims.(jwt.MapClaims); ok {
		ssoInfo, _ = claims["sso"].(map[string]interface{})
	}
	userInfo := &UserInfo{
		Uid:   int64(ssoInfo["uid"].(float64)),
		Email: ssoInfo["email"].(string),
		Name:  ssoInfo["name"].(string),
	}
	return userInfo, nil
}

// 配置初始化

func ConfigInit(configInfo interface{}) error {
	var flagconf string
	flag.StringVar(&flagconf, "conf", "../../configs/config_dev.yaml", "config path, eg: -conf config.yaml")
	c := config.New(
		config.WithSource(
			file.NewSource(flagconf),
		),
	)
	defer c.Close()
	if err := c.Load(); err != nil {
		panic(err)
	}
	if err := c.Scan(configInfo); err != nil {
		panic(err)
	}
	return nil
}
