package common

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"
)

type HTTPClient struct {
	Client  *http.Client
	Headers map[string]string
}

func NewHTTPClient(timeout time.Duration) *HTTPClient {
	return &HTTPClient{
		Client: &http.Client{
			Timeout: timeout,
		},
		Headers: make(map[string]string),
	}
}

func (c *HTTPClient) SetHeader(key, value string) {
	c.<PERSON>[key] = value
}

func Request[Response any](client *HTTPClient, method, url string, body interface{}) (*Response, error) {
	var reqBody io.Reader

	// 处理请求体
	if body != nil {
		jsonData, err := json.Marshal(body)
		if err != nil {
			return nil, fmt.Errorf("marshal request body error: %w", err)
		}
		reqBody = bytes.NewReader(jsonData)
	}

	req, err := http.NewRequest(method, url, reqBody)
	if err != nil {
		return nil, fmt.Errorf("create request error: %w", err)
	}

	if body != nil {
		req.Header.Set("Content-Type", "application/json")
	}

	for key, value := range client.Headers {
		req.Header.Set(key, value)
	}

	resp, err := client.Client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("send request error: %w", err)
	}
	defer resp.Body.Close()
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("read response body error: %w", err)
	}

	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		return nil, fmt.Errorf("HTTP error %d: %s", resp.StatusCode, string(respBody))
	}

	var result Response
	if err := json.Unmarshal(respBody, &result); err != nil {
		return nil, fmt.Errorf("unmarshal response error: %w", err)
	}

	return &result, nil
}

func GET[Response any](client *HTTPClient, url string) (*Response, error) {
	return Request[Response](client, "GET", url, nil)
}

func POST[Response any](client *HTTPClient, url string, body interface{}) (*Response, error) {
	return Request[Response](client, "POST", url, body)
}

func PUT[Response any](client *HTTPClient, url string, body interface{}) (*Response, error) {
	return Request[Response](client, "PUT", url, body)
}

func DELETE[Response any](client *HTTPClient, url string) (*Response, error) {
	return Request[Response](client, "DELETE", url, nil)
}
