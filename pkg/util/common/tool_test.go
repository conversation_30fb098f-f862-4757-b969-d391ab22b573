package common

import (
	"fmt"
	"testing"
)

func TestGetTokenToUserInfo(t *testing.T) {
	token := "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzc28iOnsidWlkIjoyODE3LCJlbWFpbCI6Inh1aG9uZ195YW9AaW50c2lnLm5ldCIsIm5hbWUiOiLlp5rml63nuqIiLCJhdmF0YXIiOiJodHRwczovL3dld29yay5xcGljLmNuL3d3cGljLzI5NjM1Nl9Ha0ZQU2pFcFNZZVIycGhfMTY2NzEwNzIyMy8wIiwicGxhdGZvcm1faWQiOiJjS1V2bWVWeHBSbEtUT3VwekRYMlV5VEJJcEVDYlV4WSIsIlJlZ2lzdHJhdGlvblR5cGUiOjB9LCJleHAiOjE3NDQxNzA1MjF9.9VpbQAJ6G0LLeLinFX8scADFoOsneHJ1S9WT2nJ1KNE"
	user, err := GetTokenToUserInfo(token)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(user)
}
