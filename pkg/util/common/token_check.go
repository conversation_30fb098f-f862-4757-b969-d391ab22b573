package common

import (
	"context"
	"errors"
	"strings"

	"github.com/go-kratos/kratos/v2/transport"
	"github.com/golang-jwt/jwt"

	userBiz "gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/internal/biz/user"
)

type UserInfo struct {
	Uid              int64
	Name             string
	Email            string
	RegistrationType int64
}

func GetUserInfoByToken(ctx context.Context) (*UserInfo, error) {
	var token string
	if tr, ok := transport.FromServerContext(ctx); ok {
		authorizationHeader := tr.RequestHeader().Get("Authorization")
		if authorizationHeader != "" {
			splitStrs := strings.Split(authorizationHeader, " ")
			if len(splitStrs) < 2 {
				return nil, errors.New("invalid authorization header")
			}
			token = splitStrs[1]
		} else {
			return nil, errors.New("missing authorization header")
		}
	}
	tokenParse, _ := jwt.Parse(token, func(token *jwt.Token) (interface{}, error) {
		return userBiz.JwtSecret, nil
	})

	ssoInfo := make(map[string]interface{}, 1)
	if claims, ok := tokenParse.Claims.(jwt.MapClaims); ok {
		ssoInfo, _ = claims["sso"].(map[string]interface{})
	}
	userInfo := &UserInfo{
		Uid:              int64(ssoInfo["uid"].(float64)),
		Email:            ssoInfo["email"].(string),
		Name:             ssoInfo["name"].(string),
		RegistrationType: int64(ssoInfo["registrationType"].(float64)),
	}
	return userInfo, nil
}

func GetUserInfoFromHeaderByToken(authorizationHeader string, secret []byte) (*UserInfo, error) {
	var token string
	if authorizationHeader != "" {
		splitStrs := strings.Split(authorizationHeader, " ")
		if len(splitStrs) < 2 {
			return nil, errors.New("invalid authorization header")
		}
		token = splitStrs[1]
	} else {
		return nil, errors.New("missing authorization header")
	}

	tokenParse, _ := jwt.Parse(token, func(token *jwt.Token) (interface{}, error) {
		return secret, nil
	})
	// if err != nil {
	// 	return nil, err
	// }
	ssoInfo := make(map[string]interface{}, 1)
	if claims, ok := tokenParse.Claims.(jwt.MapClaims); ok {
		ssoInfo, _ = claims["sso"].(map[string]interface{})
	}
	userInfo := &UserInfo{
		Uid:   int64(ssoInfo["uid"].(float64)),
		Email: ssoInfo["email"].(string),
	}
	return userInfo, nil
}

func GetUserToken(ctx context.Context) (string, error) {
	var token string
	if tr, ok := transport.FromServerContext(ctx); ok {
		authorizationHeader := tr.RequestHeader().Get("Authorization")
		if authorizationHeader != "" {
			splitStrs := strings.Split(authorizationHeader, " ")
			if len(splitStrs) < 2 {
				return "", errors.New("invalid authorization header")
			}
			token = splitStrs[1]
		} else {
			return "", errors.New("missing authorization header")
		}
	}

	return token, nil
}
