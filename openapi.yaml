# Generated with protoc-gen-openapi
# https://github.com/google/gnostic/tree/master/cmd/protoc-gen-openapi

openapi: 3.0.3
info:
    title: Asset API
    version: 0.0.1
paths:
    /v1/asset/admin-users:
        get:
            tags:
                - Asset
            operationId: Asset_GetAssetAdminUserList
            parameters:
                - name: pageSize
                  in: query
                  schema:
                    type: integer
                    format: int64
                - name: pageNo
                  in: query
                  schema:
                    type: integer
                    format: int64
                - name: searchText
                  in: query
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/GetAssetAdminUserListReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
        put:
            tags:
                - Asset
            operationId: Asset_UpdateAssetAdminUser
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/UpdateAssetAdminUserRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Reply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
        post:
            tags:
                - Asset
            operationId: Asset_CreateAssetAdminUser
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/CreateAssetAdminUserRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Reply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/asset/admin-users/{id}:
        delete:
            tags:
                - Asset
            operationId: Asset_DeleteAssetAdminUser
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int32
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Reply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/asset/alias:
        post:
            tags:
                - Asset
            operationId: Asset_UserAssetAlias
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/UserAssetAliasRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Reply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/asset/assets:
        get:
            tags:
                - Asset
            operationId: Asset_GetAssetList
            parameters:
                - name: pageSize
                  in: query
                  schema:
                    type: integer
                    format: int64
                - name: pageNo
                  in: query
                  schema:
                    type: integer
                    format: int64
                - name: searchText
                  in: query
                  schema:
                    type: string
                - name: idc
                  in: query
                  schema:
                    type: string
                - name: organizationId
                  in: query
                  schema:
                    type: string
                - name: proxyGatewayId
                  in: query
                  schema:
                    type: integer
                    format: int64
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/GetAssetListReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
        put:
            tags:
                - Asset
            operationId: Asset_UpdateAsset
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/UpdateAseetRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Reply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
        post:
            tags:
                - Asset
            operationId: Asset_CreateAsset
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/CreateAssetRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Reply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/asset/assets/{uuid}:
        delete:
            tags:
                - Asset
            operationId: Asset_DeleteAsset
            parameters:
                - name: uuid
                  in: path
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Reply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/asset/favorite:
        post:
            tags:
                - Asset
            operationId: Asset_UserAssetFavorite
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/UserAssetFavoriteRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Reply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/asset/ips:
        get:
            tags:
                - Asset
            operationId: Asset_GetAssetListByIp
            parameters:
                - name: pageSize
                  in: query
                  schema:
                    type: integer
                    format: int64
                - name: pageNo
                  in: query
                  schema:
                    type: integer
                    format: int64
                - name: ip
                  in: query
                  schema:
                    type: string
                - name: organizationId
                  in: query
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/GetAssetListByIpReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/asset/proxy-gateways:
        get:
            tags:
                - Asset
            operationId: Asset_GetAssetProxyGatewayList
            parameters:
                - name: pageSize
                  in: query
                  schema:
                    type: integer
                    format: int64
                - name: pageNo
                  in: query
                  schema:
                    type: integer
                    format: int64
                - name: searchText
                  in: query
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/GetAssetProxyGatewayListReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
        put:
            tags:
                - Asset
            operationId: Asset_UpdateAssetProxyGateway
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/UpdateAssetProxyGatewayRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Reply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
        post:
            tags:
                - Asset
            operationId: Asset_CreateAssetProxyGateway
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/CreateAssetProxyGatewayRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Reply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/asset/proxy-gateways/select:
        get:
            tags:
                - Asset
            operationId: Asset_GetAssetProxyGatewaySelect
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/GetAssetProxyGatewaySelectReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/asset/proxy-gateways/{id}:
        delete:
            tags:
                - Asset
            operationId: Asset_DeleteAssetProxyGateway
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int32
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Reply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/asset/routes:
        get:
            tags:
                - Asset
            operationId: Asset_ListAssetRoute
            parameters:
                - name: organizationId
                  in: query
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ListAssetRouteReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/asset/user-work-space:
        get:
            tags:
                - Asset
            operationId: Asset_ListUserWorkSpace
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ListUserWorkSpaceReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
components:
    schemas:
        CreateAssetAdminUserRequest:
            type: object
            properties:
                organizationId:
                    type: string
                username:
                    type: string
                password:
                    type: string
                privateKey:
                    type: string
                comment:
                    type: string
        CreateAssetProxyGatewayRequest:
            type: object
            properties:
                name:
                    type: string
                organizationId:
                    type: string
                ip:
                    type: string
                port:
                    type: integer
                    format: int64
                username:
                    type: string
                password:
                    type: string
                privateKey:
                    type: string
                comment:
                    type: string
                idc:
                    type: string
        CreateAssetRequest:
            type: object
            properties:
                name:
                    type: string
                idc:
                    type: string
                assetIp:
                    type: string
                active:
                    type: string
                platform:
                    type: string
                proxyGatewayId:
                    type: integer
                    format: int64
                protocol:
                    type: string
                assetSource:
                    type: string
                os:
                    type: string
                organizationId:
                    type: string
                comment:
                    type: string
        GetAssetAdminUserListReply:
            type: object
            properties:
                data:
                    type: array
                    items:
                        $ref: '#/components/schemas/GetAssetAdminUserListReply_AssetAdminUser'
                pageNo:
                    type: integer
                    format: int64
                pageSize:
                    type: integer
                    format: int64
                totalCount:
                    type: integer
                    format: int64
                totalPage:
                    type: integer
                    format: int64
        GetAssetAdminUserListReply_AssetAdminUser:
            type: object
            properties:
                id:
                    type: integer
                    format: int32
                organizationId:
                    type: string
                username:
                    type: string
        GetAssetListByIpReply:
            type: object
            properties:
                data:
                    type: array
                    items:
                        $ref: '#/components/schemas/GetAssetListByIpReply_Asset'
                pageNo:
                    type: integer
                    format: int64
                pageSize:
                    type: integer
                    format: int64
                totalCount:
                    type: integer
                    format: int64
                totalPage:
                    type: integer
                    format: int64
        GetAssetListByIpReply_Asset:
            type: object
            properties:
                id:
                    type: integer
                    format: int32
                name:
                    type: string
                assetIp:
                    type: string
                organizationId:
                    type: string
                uuid:
                    type: string
        GetAssetListReply:
            type: object
            properties:
                data:
                    type: array
                    items:
                        $ref: '#/components/schemas/GetAssetListReply_Asset'
                pageNo:
                    type: integer
                    format: int64
                pageSize:
                    type: integer
                    format: int64
                totalCount:
                    type: integer
                    format: int64
                totalPage:
                    type: integer
                    format: int64
        GetAssetListReply_Asset:
            type: object
            properties:
                uuid:
                    type: string
                name:
                    type: string
                assetIp:
                    type: string
                active:
                    type: string
                platform:
                    type: string
                proxyGatewayId:
                    type: integer
                    format: int64
                protocol:
                    type: string
                os:
                    type: string
                organizationId:
                    type: string
                comment:
                    type: string
                createUser:
                    type: string
                idc:
                    type: string
                createdAt:
                    type: string
                updatedAt:
                    type: string
        GetAssetProxyGatewayListReply:
            type: object
            properties:
                data:
                    type: array
                    items:
                        $ref: '#/components/schemas/GetAssetProxyGatewayListReply_AssetProxyGateway'
                pageNo:
                    type: integer
                    format: int64
                pageSize:
                    type: integer
                    format: int64
                totalCount:
                    type: integer
                    format: int64
                totalPage:
                    type: integer
                    format: int64
        GetAssetProxyGatewayListReply_AssetProxyGateway:
            type: object
            properties:
                id:
                    type: integer
                    format: int32
                organizationId:
                    type: string
                name:
                    type: string
                ip:
                    type: string
                port:
                    type: integer
                    format: int64
                username:
                    type: string
                comment:
                    type: string
                idc:
                    type: string
        GetAssetProxyGatewaySelectReply:
            type: object
            properties:
                data:
                    type: array
                    items:
                        $ref: '#/components/schemas/GetAssetProxyGatewaySelectReply_AssetProxyGateway'
        GetAssetProxyGatewaySelectReply_AssetProxyGateway:
            type: object
            properties:
                id:
                    type: integer
                    format: int32
                organizationId:
                    type: string
                name:
                    type: string
                ip:
                    type: string
                port:
                    type: integer
                    format: int64
                username:
                    type: string
                comment:
                    type: string
                idc:
                    type: string
        GoogleProtobufAny:
            type: object
            properties:
                '@type':
                    type: string
                    description: The type of the serialized message.
            additionalProperties: true
            description: Contains an arbitrary serialized message along with a @type that describes the type of the serialized message.
        ListAssetRouteReply:
            type: object
            properties:
                routers:
                    type: array
                    items:
                        $ref: '#/components/schemas/ListAssetRouteReply_Route'
        ListAssetRouteReply_Children:
            type: object
            properties:
                key:
                    type: string
                title:
                    type: string
                favorite:
                    type: boolean
                ip:
                    type: string
                organizationId:
                    type: string
        ListAssetRouteReply_Route:
            type: object
            properties:
                key:
                    type: string
                title:
                    type: string
                children:
                    type: array
                    items:
                        $ref: '#/components/schemas/ListAssetRouteReply_Children'
        ListUserWorkSpaceReply:
            type: object
            properties:
                data:
                    type: array
                    items:
                        $ref: '#/components/schemas/ListUserWorkSpaceReply_WorkSpace'
        ListUserWorkSpaceReply_WorkSpace:
            type: object
            properties:
                key:
                    type: string
                label:
                    type: string
        Reply:
            type: object
            properties:
                message:
                    type: string
        Status:
            type: object
            properties:
                code:
                    type: integer
                    description: The status code, which should be an enum value of [google.rpc.Code][google.rpc.Code].
                    format: int32
                message:
                    type: string
                    description: A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the [google.rpc.Status.details][google.rpc.Status.details] field, or localized by the client.
                details:
                    type: array
                    items:
                        $ref: '#/components/schemas/GoogleProtobufAny'
                    description: A list of messages that carry the error details.  There is a common set of message types for APIs to use.
            description: 'The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).'
        UpdateAseetRequest:
            type: object
            properties:
                uuid:
                    type: string
                name:
                    type: string
                assetIp:
                    type: string
                active:
                    type: string
                platform:
                    type: string
                proxyGatewayId:
                    type: integer
                    format: int64
                protocol:
                    type: string
                os:
                    type: string
                organizationId:
                    type: string
                comment:
                    type: string
                idc:
                    type: string
        UpdateAssetAdminUserRequest:
            type: object
            properties:
                id:
                    type: integer
                    format: int32
                organizationId:
                    type: string
                username:
                    type: string
                password:
                    type: string
                privateKey:
                    type: string
                comment:
                    type: string
        UpdateAssetProxyGatewayRequest:
            type: object
            properties:
                id:
                    type: integer
                    format: int32
                name:
                    type: string
                organizationId:
                    type: string
                ip:
                    type: string
                port:
                    type: integer
                    format: int64
                username:
                    type: string
                password:
                    type: string
                privateKey:
                    type: string
                comment:
                    type: string
                idc:
                    type: string
        UserAssetAliasRequest:
            type: object
            properties:
                key:
                    type: string
                alias:
                    type: string
        UserAssetFavoriteRequest:
            type: object
            properties:
                assetIp:
                    type: string
                action:
                    type: string
tags:
    - name: Asset
