# KMS服务端 - 数据密钥管理服务

## 🛡️ 安全架构

### 零信任原则
- **数据不离开客户端**：服务端永远不接触用户敏感数据
- **密钥分离**：主密钥在AWS KMS，数据密钥临时下发
- **立即清理**：明文密钥在服务端内存中立即清除
- **权限隔离**：用户只能访问自己的数据密钥

### 安全特性
- 🔐 身份验证和授权
- 🚦 API速率限制
- 🛡️ 安全HTTP头
- 🌐 CORS保护
- 📊 请求日志记录

## 🚀 快速开始

### 1. 安装依赖
```bash
cd server
npm install
```

### 2. 配置环境变量
```bash
cp .env.example .env
# 编辑 .env 文件，填入您的AWS配置
```

### 3. 启动服务
```bash
npm start
```

服务将在 http://localhost:3000 启动

## 📋 API接口

### 健康检查
```http
GET /api/health
```

### 生成数据密钥
```http
POST /api/generate-data-key
Authorization: Bearer your-jwt-token
Content-Type: application/json

{
  "userId": "user123",
  "sessionId": "session-uuid"
}
```

### 解密数据密钥
```http
POST /api/decrypt-data-key
Authorization: Bearer your-jwt-token
Content-Type: application/json

{
  "encryptedDataKey": "base64-encrypted-key",
  "userId": "user123", 
  "sessionId": "session-uuid"
}
```

### 轮换数据密钥
```http
POST /api/rotate-data-key
Authorization: Bearer your-jwt-token
Content-Type: application/json

{
  "userId": "user123",
  "oldSessionId": "old-session-uuid",
  "newSessionId": "new-session-uuid"
}
```

## 🔧 配置说明

### 环境变量
- `AWS_REGION`: AWS区域
- `AWS_ACCESS_KEY_ID`: AWS访问密钥ID
- `AWS_SECRET_ACCESS_KEY`: AWS秘密访问密钥
- `KMS_KEY_ARN`: KMS密钥ARN
- `PORT`: 服务端口（默认3000）
- `JWT_SECRET`: JWT签名密钥
- `RATE_LIMIT_WINDOW_MS`: 速率限制时间窗口
- `RATE_LIMIT_MAX_REQUESTS`: 速率限制最大请求数

## 🏗️ 项目结构

```
server/
├── src/
│   ├── config.js              # 配置管理
│   ├── server.js              # 主服务器
│   ├── services/
│   │   └── dataKeyService.js  # 数据密钥服务
│   ├── middleware/
│   │   └── auth.js            # 身份验证中间件
│   └── routes/
│       └── dataKeyRoutes.js   # 数据密钥路由
├── package.json
├── .env.example
└── README.md
```

## 🔒 安全最佳实践

1. **密钥管理**
   - 使用AWS KMS管理主密钥
   - 数据密钥在服务端立即清除
   - 支持密钥轮换

2. **身份验证**
   - JWT令牌验证
   - 用户权限隔离
   - 会话管理

3. **网络安全**
   - HTTPS传输
   - CORS配置
   - 速率限制

4. **监控和日志**
   - 请求日志记录
   - 错误监控
   - 性能指标

## 🧪 测试

```bash
npm test
```

## 📝 开发模式

```bash
npm run dev
```

启用调试模式，支持热重载和调试器。
