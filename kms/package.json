{"name": "kms-server", "version": "1.0.0", "description": "KMS服务端 - 数据密钥管理服务", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "node --inspect src/server.js", "test": "jest"}, "keywords": ["kms", "encryption", "data-key", "security", "server"], "author": "xuhong_yao", "license": "MIT", "dependencies": {"@aws-sdk/client-kms": "^3.456.0", "express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "dotenv": "^16.3.1", "express-rate-limit": "^7.1.5", "jsonwebtoken": "^9.0.2"}, "devDependencies": {"jest": "^29.7.0", "supertest": "^6.3.3"}, "engines": {"node": ">=14.0.0", "npm": ">=6.0.0"}}