#!/usr/bin/env node

const AuthMiddleware = require('../middleware/auth');

/**
 * 🔑 JWT令牌生成工具
 * 
 * 用于生成测试用的JWT令牌
 */

function generateTestToken() {
  console.log('\n🔑 JWT令牌生成工具');
  console.log('=====================================');
  
  // 获取命令行参数
  const args = process.argv.slice(2);
  const userId = args[0] || 'user123';
  const role = args[1] || 'user';
  
  try {
    // 生成JWT令牌
    const token = AuthMiddleware.generateToken(userId, role);
    
    console.log('✅ JWT令牌生成成功');
    console.log(`   用户ID: ${userId}`);
    console.log(`   角色: ${role}`);
    console.log(`   令牌: ${token}`);
    console.log('');
    console.log('📋 使用方法:');
    console.log(`   Authorization: Bearer ${token}`);
    console.log('');
    console.log('🧪 测试命令:');
    console.log(`   curl -H "Authorization: Bearer ${token}" \\`);
    console.log('        http://localhost:3000/api/health');
    console.log('=====================================\n');
    
    return token;
    
  } catch (error) {
    console.error('❌ 令牌生成失败:', error.message);
    process.exit(1);
  }
}

// 如果直接运行此文件
if (require.main === module) {
  console.log('🚀 启动JWT令牌生成器...');
  console.log('💡 用法: node generateToken.js [userId] [role]');
  console.log('💡 示例: node generateToken.js user123 admin\n');
  
  generateTestToken();
}

module.exports = { generateTestToken };
