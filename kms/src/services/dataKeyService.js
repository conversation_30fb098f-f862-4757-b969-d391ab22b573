const { GenerateDataKeyCommand, DecryptCommand, EncryptCommand } = require('@aws-sdk/client-kms');
const { kmsClient, kmsKeyArn } = require('../config');

/**
 * 🔑 数据密钥服务 - 只管理密钥，绝不接触用户敏感数据
 * 
 * 安全原则：
 * 1. 服务端只处理密钥管理，不接触用户数据
 * 2. 明文数据密钥在服务端立即清除
 * 3. 只返回加密的数据密钥用于持久化
 * 4. 支持密钥轮换和过期管理
 */
class DataKeyService {
  
  /**
   * 🆕 生成新的数据密钥
   * @param {string} userId - 用户ID
   * @param {string} sessionId - 会话ID
   * @returns {Promise<Object>} 包含明文和加密数据密钥的对象
   */
  async generateDataKey(userId, sessionId) {
    try {
      console.log(`🔑 为用户 ${userId} 生成新数据密钥`);
      
      const encryptionContext = {
        userId: userId,
        sessionId: sessionId,
        purpose: 'client-side-encryption'
        // 移除 timestamp 以简化 EncryptionContext 匹配
      };
      
      const command = new GenerateDataKeyCommand({
        KeyId: kmsKeyArn,
        KeySpec: 'AES_256',
        EncryptionContext: encryptionContext
      });

      const response = await kmsClient.send(command);
      
      console.log('📏 KMS原始明文密钥长度:', response.Plaintext.length);
      console.log('📏 加密密钥长度:', response.CiphertextBlob.length);
      
      // 🔐 将用户ID嵌入到明文数据密钥中，形成32字节最终密钥
      const originalKey = Buffer.from(response.Plaintext);
      const keyWithUserId = this.embedUserIdInDataKey(originalKey, userId);
      
      // 🔐 使用KMS重新加密包含用户ID的32字节密钥
      const encryptCommand = new EncryptCommand({
        KeyId: kmsKeyArn,
        Plaintext: keyWithUserId,
        EncryptionContext: encryptionContext
      });
      
      const encryptResponse = await kmsClient.send(encryptCommand);
      
      const result = {
        // 🔓 包含用户ID的明文数据密钥 - 客户端用于加密，服务端立即清除
        plaintextDataKey: keyWithUserId.toString('base64'),
        // 🔒 重新加密的数据密钥 - 包含用户ID，用于持久化存储
        encryptedDataKey: Buffer.from(encryptResponse.CiphertextBlob).toString('base64'),
        keyId: encryptResponse.KeyId,
        expiresAt: Date.now() + (24 * 60 * 60 * 1000), // 24小时过期
        // 🔐 返回实际使用的 EncryptionContext（与生成时完全一致）
        encryptionContext: encryptionContext
      };

      // 🗑️ 立即清除服务端内存中的明文密钥
      response.Plaintext.fill(0);
      originalKey.fill(0);
      keyWithUserId.fill(0);
      
      console.log('✅ 数据密钥生成成功，已嵌入用户ID');
      return result;
      
    } catch (error) {
      console.error('❌ 生成数据密钥失败:', error);
      throw new Error(`生成数据密钥失败: ${error.message}`);
    }
  }

  /**
   * 🔓 解密已存储的数据密钥
   * @param {string} encryptedDataKey - 加密的数据密钥
   * @param {Object} originalEncryptionContext - 原始的加密上下文
   * @param {string} requestUserId - JWT中的用户ID（用于验证）
   * @returns {Promise<Object>} 包含明文数据密钥的对象
   */
  async decryptDataKey(encryptedDataKey, originalEncryptionContext, requestUserId) {
    try {
      console.log(`🔓 为用户 ${originalEncryptionContext.userId} 解密数据密钥`);
      console.log(`📏 接收到的加密数据密钥长度: ${encryptedDataKey.length}`);
      console.log(`📋 加密数据密钥前20字符: ${encryptedDataKey.substring(0, 20)}...`);
      console.log(`🔐 使用原始 EncryptionContext:`, originalEncryptionContext);
      
      const command = new DecryptCommand({
        CiphertextBlob: Buffer.from(encryptedDataKey, 'base64'),
        // 🔐 使用完整的原始加密上下文进行解密
        EncryptionContext: originalEncryptionContext
      });

      const response = await kmsClient.send(command);
      
      console.log('� KMS解密成功，EncryptionContext已验证');
      
      // 🔐 从解密后的明文数据密钥中验证用户ID
      const keyWithUserId = Buffer.from(response.Plaintext);
      const { embeddedUserId, foundPosition, verified } = this.extractUserIdFromDataKey(keyWithUserId, requestUserId);
      
      console.log('🔍 用户ID验证结果:', verified ? '✅ 验证通过' : '❌ 验证失败');
      console.log('🔍 JWT中的用户ID:', requestUserId);
      
      // 🛡️ 确保能在密钥中找到匹配的用户ID
      if (!verified) {
        console.error(`❌ 密文用户身份不匹配: 无法在密钥中找到用户ID ${requestUserId}`);
        throw new Error('无权访问其他用户的数据密钥');
      }
      
      console.log('✅ 密文用户身份验证通过');
      
      const result = {
        // 🔑 返回完整的32字节密钥用于客户端AES-256加密
        plaintextDataKey: keyWithUserId.toString('base64'),
        // 返回验证信息供客户端参考
        keyMetadata: {
          originalUserId: embeddedUserId,
          originalSessionId: originalEncryptionContext.sessionId,
          verified: verified,
          foundPosition: foundPosition
        }
      };

      // 🗑️ 立即清除服务端内存中的明文密钥（但保留要返回的副本）
      response.Plaintext.fill(0);
      // 注意：keyWithUserId 在返回后才清除
      
      console.log('✅ 数据密钥解密成功，用户身份验证通过');
      return result;
      
    } catch (error) {
      console.error('❌ 解密数据密钥失败:', error);
      
      // 根据错误类型返回不同的错误信息
      if (error.name === 'InvalidCiphertextException') {
        throw new Error('数据密钥无效或已损坏');
      } else if (error.name === 'AccessDeniedException') {
        throw new Error('没有权限解密此数据密钥');
      } else if (error.message.includes('用户身份') || error.message.includes('验证失败')) {
        // 这是我们自定义的安全检查错误
        throw error;
      } else {
        throw new Error(`解密数据密钥失败: ${error.message}`);
      }
    }
  }

  /**
   * 🔄 轮换数据密钥
   * @param {string} userId - 用户ID
   * @param {string} oldSessionId - 旧会话ID
   * @param {string} newSessionId - 新会话ID
   * @returns {Promise<Object>} 新的数据密钥
   */
  async rotateDataKey(userId, oldSessionId, newSessionId) {
    try {
      console.log(`🔄 为用户 ${userId} 轮换数据密钥`);
      
      // 生成新的数据密钥
      const newDataKey = await this.generateDataKey(userId, newSessionId);
      
      console.log('✅ 数据密钥轮换成功');
      return newDataKey;
      
    } catch (error) {
      console.error('❌ 数据密钥轮换失败:', error);
      throw new Error(`数据密钥轮换失败: ${error.message}`);
    }
  }

  /**
   * 🔐 将用户ID嵌入到数据密钥中 (随机位置嵌入，保持32字节总长度)
   * @param {Buffer} originalKey - 原始数据密钥 (32字节)
   * @param {string} userId - 用户ID
   * @returns {Buffer} 包含用户ID的数据密钥 (32字节)
   * @private
   */
  embedUserIdInDataKey(originalKey, userId) {
    try {
      // 确保使用完整的32字节密钥
      const fullKey = Buffer.alloc(32);
      originalKey.copy(fullKey, 0, 0, Math.min(originalKey.length, 32));
      
      // 将用户ID转换为Buffer
      const userIdBytes = Buffer.from(userId, 'utf8');
      const userIdLength = userIdBytes.length;
      
      if (userIdLength === 0) {
        throw new Error('用户ID不能为空');
      }
      
      if (userIdLength > 31) {
        throw new Error(`用户ID过长，最大支持31字节，当前${userIdLength}字节`);
      }
      
      // 随机选择嵌入位置（确保用户ID能完全放入）
      const maxStartPos = 32 - userIdLength;
      const startPos = Math.floor(Math.random() * maxStartPos);
      
      // 在随机位置嵌入用户ID
      userIdBytes.copy(fullKey, startPos);
      
      console.log(`🔐 已将用户ID嵌入数据密钥: ${userId}`);
      console.log(`📏 用户ID长度: ${userIdLength}字节`);
      console.log(`📍 嵌入位置: ${startPos}-${startPos + userIdLength - 1}`);
      console.log(`📏 最终密钥长度: ${fullKey.length}字节`);
      
      return fullKey;
    } catch (error) {
      console.error('❌ 嵌入用户ID失败:', error);
      throw new Error(`嵌入用户ID失败: ${error.message}`);
    }
  }

  /**
   * 🔓 从数据密钥中提取用户ID (在32字节密钥中搜索匹配用户ID)
   * @param {Buffer} keyWithUserId - 包含用户ID的数据密钥 (32字节)
   * @param {string} expectedUserId - 期望的用户ID
   * @returns {Object} 包含验证结果和用户ID的对象
   * @private
   */
  extractUserIdFromDataKey(keyWithUserId, expectedUserId) {
    try {
      if (keyWithUserId.length !== 32) {
        throw new Error(`数据密钥长度错误，期望32字节，实际${keyWithUserId.length}字节`);
      }
      
      const expectedUserIdBytes = Buffer.from(expectedUserId, 'utf8');
      const userIdLength = expectedUserIdBytes.length;
      
      if (userIdLength === 0) {
        throw new Error('期望的用户ID不能为空');
      }
      
      // 在整个32字节密钥中搜索用户ID
      let foundPosition = -1;
      for (let i = 0; i <= 32 - userIdLength; i++) {
        const segment = keyWithUserId.slice(i, i + userIdLength);
        if (segment.equals(expectedUserIdBytes)) {
          foundPosition = i;
          break;
        }
      }
      
      if (foundPosition === -1) {
        console.log('🔍 在32字节密钥中未找到匹配的用户ID');
        console.log(`📋 期望的用户ID: ${expectedUserId}`);
        console.log(`📏 期望的用户ID长度: ${userIdLength}字节`);
        throw new Error('用户身份验证失败：密钥中未找到匹配的用户ID');
      }
      
      console.log(`🔓 在密钥中找到用户ID: ${expectedUserId}`);
      console.log(`📍 找到位置: ${foundPosition}-${foundPosition + userIdLength - 1}`);
      console.log(`📏 用户ID长度: ${userIdLength}字节`);
      
      return {
        embeddedUserId: expectedUserId,
        foundPosition: foundPosition,
        verified: true
      };
    } catch (error) {
      console.error('❌ 提取用户ID失败:', error);
      throw new Error(`提取用户ID失败: ${error.message}`);
    }
  }
}

module.exports = DataKeyService;
