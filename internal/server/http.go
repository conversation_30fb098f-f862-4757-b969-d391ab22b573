package server

import (
	"context"
	"encoding/json"
	"errors"

	"github.com/go-kratos/kratos/v2/log"
	kratosMiddleware "github.com/go-kratos/kratos/v2/middleware"
	"github.com/go-kratos/kratos/v2/middleware/auth/jwt"
	"github.com/go-kratos/kratos/v2/middleware/logging"
	"github.com/go-kratos/kratos/v2/middleware/recovery"
	"github.com/go-kratos/kratos/v2/middleware/selector"
	"github.com/go-kratos/kratos/v2/middleware/tracing"
	"github.com/go-kratos/kratos/v2/middleware/validate"
	"github.com/go-kratos/kratos/v2/transport/http"
	jwt5 "github.com/golang-jwt/jwt/v5"
	"github.com/gorilla/handlers"
	aiV1 "gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/api/ai/v1"
	assetV1 "gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/api/asset/v1"
	permV1 "gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/api/perm/v1"
	userV1 "gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/api/user/v1"
	userBiz "gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/internal/biz/user"
	"gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/internal/conf"
	aiService "gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/internal/service/ai"
	assetService "gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/internal/service/asset"
	permService "gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/internal/service/perm"
	userService "gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/internal/service/user"
	"go.opentelemetry.io/otel/sdk/trace"
	httpNet "net/http"
	"time"
)

func NewWhiteListMatcher() selector.MatchFunc {
	whiteList := make(map[string]struct{})
	empty := struct{}{}
	whiteList["/user.v1.User/UserLoginPwd"] = empty
	whiteList["/user.v1.User/UserLoginEmailSendCode"] = empty
	whiteList["/user.v1.User/UserLoginEmailVerification"] = empty
	whiteList["/user.v1.User/UserLoginSSO"] = empty

	// 临时白名单
	//whiteList["/user.v1.User/GetUserInfoByEmail"] = empty
	//whiteList["/perm.v1.Perm/CreateUserAssetPermission"] = empty
	//whiteList["/perm.v1.Perm/GetUserAssetPermissionList"] = empty
	//whiteList["/asset.v1.Asset/GetAssetProxyGatewayList"] = empty
	//whiteList["/asset.v1.Asset/CreateAssetProxyGateway"] = empty
	//whiteList["/asset.v1.Asset/UpdateAssetProxyGateway"] = empty
	//whiteList["/asset.v1.Asset/DeleteAssetProxyGateway"] = empty
	//whiteList["/asset.v1.Asset/GetAssetAdminUserList"] = empty
	//whiteList["/asset.v1.Asset/CreateAssetAdminUser"] = empty
	//whiteList["/asset.v1.Asset/UpdateAssetAdminUser"] = empty
	//whiteList["/asset.v1.Asset/DeleteAssetAdminUser"] = empty
	//whiteList["/asset.v1.Asset/GetAssetListByIp"] = empty
	//whiteList["/asset.v1.Asset/GetAssetList"] = empty
	//whiteList["/asset.v1.Asset/CreateAsset"] = empty
	//whiteList["/asset.v1.Asset/UpdateAsset"] = empty
	//whiteList["/asset.v1.Asset/DeleteAsset"] = empty
	//whiteList["/asset.v1.Asset/GetAssetProxyGatewaySelect"] = empty

	return func(ctx context.Context, operation string) bool {
		if _, ok := whiteList[operation]; ok {
			if httpTransport, ok := http.RequestFromServerContext(ctx); ok {
				httpTransport.Header.Set("WhiteList", "true")
			}
			return false
		}
		return true
	}
}

// NewHTTPServer new an HTTP server.
func NewHTTPServer(c *conf.Server, user *userService.UserService, ai *aiService.AiService, asset *assetService.AssetService, perm *permService.PermService, logger log.Logger, sso *conf.Sso, bs *conf.OtherInfo) *http.Server {
	var opts = []http.ServerOption{
		http.Middleware(
			validate.Validator(),
			recovery.Recovery(),
			logging.Server(logger),
			tracing.Server(tracing.WithTracerProvider(trace.NewTracerProvider())),
			selector.Server(
				jwt.Server(func(token *jwt5.Token) (interface{}, error) {
					return userBiz.JwtSecret, nil
				}, jwt.WithSigningMethod(jwt5.SigningMethodHS256)),
			).Match(NewWhiteListMatcher()).Match(NewWhiteListMatcher()).Build(),
			JwtTokenBlackListMiddleware(user),
		), http.Filter(handlers.CORS(
			handlers.AllowedHeaders([]string{"X-Requested-With", "Content-Type", "Authorization"}),
			handlers.AllowedMethods([]string{"GET", "POST", "PUT", "HEAD", "OPTIONS"}),
			handlers.AllowedOrigins([]string{"*"}),
		)),
	}
	if c.Http.Network != "" {
		opts = append(opts, http.Network(c.Http.Network))
	}
	if c.Http.Addr != "" {
		opts = append(opts, http.Address(c.Http.Addr))
	}
	if c.Http.Timeout != "" {
		t, _ := time.ParseDuration(c.Http.Timeout)
		opts = append(opts, http.Timeout(t))
	}
	opts = append(opts, http.ResponseEncoder(func(w httpNet.ResponseWriter, r *httpNet.Request, i interface{}) error {
		if r.Header.Get("Upgrade") == "websocket" {
			return nil
		}
		type response struct {
			Code int         `json:"code"`
			Data interface{} `json:"data"`
			Ts   string      `json:"ts"`
		}
		reply := &response{
			Code: 200,
			Data: i,
			Ts:   time.Now().String(),
		}
		w.Header().Set("Content-Type", "application/json")
		encoder := json.NewEncoder(w)
		err := encoder.Encode(reply)
		if err != nil {
			w.WriteHeader(httpNet.StatusInternalServerError)
			return err
		}
		return nil
	}))

	srv := http.NewServer(opts...)
	userV1.RegisterUserHTTPServer(srv, user)

	wsServer := aiService.NewServer(bs.LlmGatewayAddr)
	srv.HandleFunc("/v1/ai/chat/ws", wsServer.HandleWebSocket(ai))
	aiV1.RegisterAiHTTPServer(srv, ai)

	assetV1.RegisterAssetHTTPServer(srv, asset)
	permV1.RegisterPermHTTPServer(srv, perm)
	return srv
}

func JwtTokenBlackListMiddleware(user *userService.UserService) kratosMiddleware.Middleware {
	return func(handler kratosMiddleware.Handler) kratosMiddleware.Handler {
		return func(ctx context.Context, req interface{}) (reply interface{}, err error) {
			exist, err := user.IsExistInJwtBlackList(ctx)
			if exist {
				return nil, errors.New("invalid token")
			}
			// 继续执行原始处理器
			return handler(ctx, req)
		}
	}
}
