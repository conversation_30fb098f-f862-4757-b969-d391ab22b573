package data

import (
	"errors"
	"fmt"
	"time"

	reqC "github.com/imroc/req/v3"
	"github.com/spf13/cast"
	biz "gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/internal/biz/user"
	cryptoUtil "gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/pkg/crypto_utils"
	"gorm.io/gorm"
)

// 获取可用模型列表

type ModelResponse struct {
	Data []struct {
		ModelName     string `json:"model_name"`
		LitellmParams struct {
			ApiBase string `json:"api_base"`
			Model   string `json:"model"`
		} `json:"litellm_params"`
		ModelInfo struct {
			Id       string `json:"id"`
			DbModel  bool   `json:"db_model"`
			Provider string `json:"provider"`
		} `json:"model_info"`
	} `json:"data"`
}

func (r *UserRepo) GetModelList() ([]string, error) {
	client := reqC.C()
	var resContent ModelResponse
	resp, err := client.R().
		SetHeader("Authorization", "Bearer "+r.Data.Conf.LlmGatewayKey).
		SetHeader("Content-Type", "application/json").
		SetSuccessResult(&resContent).
		Get(r.Data.Conf.LlmGatewayAddr + "/model/info")
	if err != nil {
		r.Log.Errorf("get model list error, err: %v", err.Error())
		return nil, err
	}
	if !resp.IsSuccessState() {
		r.Log.Errorf("get model list error, err: %v", err.Error())
		return nil, err
	}

	var models []string
	for _, v := range resContent.Data {
		models = append(models, v.ModelName)
	}
	return models, nil
}

// 生成用户litellm密钥

type generateBody struct {
	Models         []string          `json:"models"`
	Metadata       map[string]string `json:"metadata"`
	Budget         float64           `json:"max_budget"`
	BudgetDuration string            `json:"budget_duration"`
	Duration       string            `json:"duration"`
	KeyAlias       string            `json:"key_alias"`
}

var max_budget = map[string]float64{
	"lite":  10.0,
	"pro":   30.0,
	"ultra": 100.0,
}

type generateResponse struct {
	Key string `json:"key"`
}

func (r *UserRepo) GenerateLiteLLMKey(user biz.UserAccountInfo) (UserKey, error) {
	if r == nil || r.Data == nil || r.Data.Conf == nil || r.Data.Db == nil {
		return UserKey{}, fmt.Errorf("UserRepo or its Data/Conf/Db is nil")
	}
	models, err := r.GetModelList()
	if err != nil || len(models) == 0 {
		return UserKey{}, err
	}

	keyAlias := cast.ToString(user.Uid)
	var expireDuration string
	switch user.Subscription {
	case "free":
		expireDuration = "3650d"
		keyAlias = "free-" + keyAlias
		models = []string{"Qwen-Turbo"}
	case "lite":
		expireDuration = "700d"
	case "pro":
		expireDuration = "30d"
	case "ultra":
		expireDuration = "30d"
	default:
		expireDuration = "30d"
	}

	client := reqC.C().SetTimeout(10 * time.Second)
	var resContent generateResponse
	resp, err := client.R().
		SetHeader("Authorization", "Bearer "+r.Data.Conf.LlmGatewayKey).
		SetHeader("Content-Type", "application/json").
		SetBody(&generateBody{Models: models,
			Budget:         max_budget[user.Subscription],
			Duration:       expireDuration,
			BudgetDuration: "30d",
			KeyAlias:       keyAlias,
			Metadata:       map[string]string{"user": user.Email, "uid": cast.ToString(user.Uid), "subscription": user.Subscription}}).
		SetSuccessResult(&resContent).
		Post(r.Data.Conf.LlmGatewayAddr + "/key/generate")
	if err != nil {
		r.Log.Errorf("user %v generate key error, err: %v", user, err.Error())
		return UserKey{}, err
	}

	if !resp.IsSuccessState() {
		r.Log.Errorf("user %v generate key error, response: %v", user, resp)
		return UserKey{}, err
	}
	if resContent.Key == "" {
		return UserKey{}, fmt.Errorf("generate key empty")
	}

	encKey, encErr := cryptoUtil.EncryptDBKey(resContent.Key, r.Data.Conf.LlmGatewayKey)
	if encErr != nil {
		r.Log.Errorf("user %v encrypt key error, err: %v", user, encErr)
		return UserKey{}, encErr
	}

	data := &UserKey{
		Uid:          user.Uid,
		Email:        user.Email,
		Key:          encKey,
		Subscription: user.Subscription,
	}

	if err := r.Data.Db.Table("t_user_keys").Create(&data).Error; err != nil {
		r.Log.Errorf("user %v create key error, err: %v", user, err.Error())
		return UserKey{}, err
	}
	return *data, nil
}

// 获取用户的模型密钥和模型列表
type UserModelKey struct {
	Uid           int64
	Email         string
	Key           string
	Subscription  string
	Model         []string
	Expires       string
	BudgetResetAt string
	Ratio         float64
}
type UserModelKeyInfo struct {
	Key  string `json:"key"`
	Info struct {
		Models        []string `json:"models"`
		Expires       string   `json:"expires"` // ISO 8601 format
		BudgetResetAt string   `json:"budget_reset_at"`
		Spend         float64  `json:"spend"`
		MaxBudget     float64  `json:"max_budget"`
	} `json:"info"`
}

func (r *UserRepo) GetUserLlmModelKey(user biz.UserAccountInfo) (UserModelKey, error) {
	var budgetResetAt string
	// 1. 尝试获取用户当前订阅类型的密钥
	if key, ok, err := r.getAndVerifyKey(user, user.Subscription); err != nil {
		// 发生不可恢复的错误
		return UserModelKey{}, err
	} else if ok {
		// 密钥有效，直接返回
		return key, nil
	} else {
		budgetResetAt = key.BudgetResetAt
	}
	fmt.Println(budgetResetAt, "budgetResetAt")

	// 2. 如果当前订阅密钥无效（额度用尽或不存在），则尝试获取免费密钥
	if key, ok, err := r.getAndVerifyKey(user, "free"); err == nil && ok {
		key.Ratio = 1
		key.BudgetResetAt = budgetResetAt
		return key, nil
	}

	// 3. 如果免费密钥也不存在或无效，则为用户生成一个新的免费密钥
	r.Log.Infof("user %v has no valid key, generating a new free key", user)
	freeUserAccount := biz.UserAccountInfo{
		Email:        user.Email,
		Uid:          user.Uid,
		Subscription: "free",
	}
	if _, err := r.GenerateLiteLLMKey(freeUserAccount); err != nil {
		r.Log.Errorf("user %v generate free key error, err: %v", user, err.Error())
		return UserModelKey{}, err
	}
	// 4. 再次获取刚生成的免费密钥信息并返回
	if key, ok, err := r.getAndVerifyKey(freeUserAccount, "free"); err != nil {
		return UserModelKey{}, err
	} else if ok {
		return key, nil
	}
	// 如果到这里仍然失败，说明发生了意外情况
	return UserModelKey{}, fmt.Errorf("failed to get or generate a valid key for user %v", user)
}

// getAndVerifyKey 是一个辅助函数，用于获取、解密并验证指定订阅类型的密钥
func (r *UserRepo) getAndVerifyKey(user biz.UserAccountInfo, subscription string) (UserModelKey, bool, error) {
	var out UserModelKey
	var findUserKey UserKey

	// 1. 从数据库查找密钥
	if err := r.Data.Db.Table("t_user_keys").Where("uid = ? and subscription=? ", user.Uid, subscription).First(&findUserKey).Error; err != nil {
		// 如果是没找到记录的错误，则认为验证失败，但不返回错误，以便上层逻辑可以继续尝试
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return out, false, nil
		}
		r.Log.Errorf("user %v get key for subscription '%s' error, err: %v", user, subscription, err)
		return out, false, err
	}
	// 2. 解密密钥
	decKey, decErr := cryptoUtil.DecryptDBKey(findUserKey.Key, r.Data.Conf.LlmGatewayKey)
	if decErr != nil {
		r.Log.Errorf("user %v decrypt key for subscription '%s' error, err: %v", user, subscription, decErr)
		return out, false, decErr
	}

	// 3. 调用API验证密钥信息
	client := reqC.C().SetTimeout(10 * time.Second)
	var resContent UserModelKeyInfo
	resp, err := client.R().
		SetRetryCount(2).
		SetRetryBackoffInterval(1*time.Second, 2*time.Second).
		SetHeader("Authorization", "Bearer "+r.Data.Conf.LlmGatewayKey).
		SetHeader("Content-Type", "application/json").
		SetSuccessResult(&resContent).
		Get(r.Data.Conf.LlmGatewayAddr + "/key/info?key=" + decKey)

	if err != nil || !resp.IsSuccessState() {
		r.Log.Errorf("user %v get key info for subscription '%s' error, err: %v, resp: %s", user, subscription, err, resp.String())
		// API调用失败，也认为验证失败
		return out, false, fmt.Errorf("failed to get key info from gateway: %v", err)
	}

	// 4. 检查额度
	// 避免除以零
	if resContent.Info.MaxBudget <= 0 {
		// 认为额度有效，或者根据业务逻辑处理
		return UserModelKey{
			Uid:           user.Uid,
			Email:         user.Email,
			Key:           decKey,
			Subscription:  subscription,
			Model:         resContent.Info.Models,
			Expires:       resContent.Info.Expires,
			BudgetResetAt: resContent.Info.BudgetResetAt,
			Ratio:         0,
		}, true, nil
	}

	ratio := resContent.Info.Spend / resContent.Info.MaxBudget
	if ratio < 1 {
		// 额度充足，验证成功
		return UserModelKey{
			Uid:           user.Uid,
			Email:         user.Email,
			Key:           decKey,
			Subscription:  subscription,
			Model:         resContent.Info.Models,
			Expires:       resContent.Info.Expires,
			BudgetResetAt: resContent.Info.BudgetResetAt,
			Ratio:         ratio,
		}, true, nil
	}
	out.BudgetResetAt = resContent.Info.BudgetResetAt
	out.Expires = resContent.Info.Expires
	return out, false, nil
}

// 刷新用户key 模型信息

func (r *UserRepo) FlushUserLlmBaseInfo(user biz.UserAccountInfo) error {
	var users []biz.UserAccountInfo
	if user.Uid != 0 {
		users = append(users, user)
	} else {
		// 全部用户刷新数据
		r.Data.Db.Table("t_users u").Select("u.*").
			Joins("LEFT JOIN t_user_keys k ON u.uid = k.uid").
			Where("u.deleted_at IS NULL AND k.uid IS NULL").
			Find(&users)
	}
	for _, u := range users {
		u.Subscription = "lite" // 默认订阅类型为 lite
		if _, err := r.GenerateLiteLLMKey(u); err != nil {
			r.Log.Errorf("user %v flush base info error, err: %v", u, err.Error())
			return err
		}
	}
	return nil
}
