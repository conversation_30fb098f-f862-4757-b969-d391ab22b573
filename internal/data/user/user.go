package data

import (
	"context"
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"

	cryptoUtil "gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/pkg/crypto_utils"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-redis/redis/v8"
	"github.com/jinzhu/copier"
	"gorm.io/gorm"

	biz "gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/internal/biz/user"
	userBiz "gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/internal/biz/user"
	"gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/internal/client"
	"gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/internal/conf"
	"gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/pkg/util/common"
)

type Data struct {
	// TODO wrapped database client
	Db          *gorm.DB
	Rdb         *redis.Client
	Sso         *conf.Sso
	Client      *client.Client
	Log         *log.Helper
	Conf        *conf.OtherInfo
	EmailConfig *conf.EmailConfig
	WecomConfig *conf.WecomConfig
}

type UserRepo struct {
	Data *Data
	Log  *log.Helper
}

func (r *UserRepo) GetUserByCtx(ctx context.Context) (*userBiz.User, error) {
	userInfo, err := common.GetUserInfoByToken(ctx)
	if err != nil {
		return nil, err
	}
	newUser := &userBiz.User{}
	if err := r.Data.Db.WithContext(ctx).Model(&User{}).Where("uid = ?", userInfo.Uid).First(newUser).Error; err != nil {
		r.Data.Log.WithContext(ctx).Errorf("find User data, err: %v", err.Error())
		return nil, err
	}
	userModelKey, err := r.GetUserLlmModelKey(biz.UserAccountInfo{
		Email:        userInfo.Email,
		Uid:          userInfo.Uid,
		Subscription: "lite",
	})
	if err != nil {
		r.Data.Log.WithContext(ctx).Errorf("generate liteLLM key error, err: %s", err.Error())
		return nil, fmt.Errorf("get liteLLM key error, err: %v", err)
	}
	newUser.Key = userModelKey.Key
	newUser.Subscription = userModelKey.Subscription
	newUser.Models = userModelKey.Model
	newUser.LlmGatewayAddr = r.Data.Conf.LlmGatewayAddr
	newUser.Ratio = userModelKey.Ratio
	loc, _ := time.LoadLocation("Asia/Shanghai")
	t, err := time.Parse(time.RFC3339Nano, userModelKey.Expires)
	if err == nil {
		newUser.Expires = t.In(loc).Format("2006-01-02 15:04:05")
	} else {
		newUser.Expires = userModelKey.Expires
	}
	t2, err := time.Parse(time.RFC3339Nano, userModelKey.BudgetResetAt)
	if err == nil {
		newUser.BudgetResetAt = t2.In(loc).Format("2006-01-02 15:04:05")
	} else {
		newUser.BudgetResetAt = userModelKey.BudgetResetAt
	}

	return newUser, nil
}

func (r *UserRepo) GetUserTermConfig(ctx context.Context) (*userBiz.UserTermConfig, error) {
	userInfo, err := common.GetUserInfoByToken(ctx)
	if err != nil {
		return nil, err
	}
	var existingItems []*UserTermConfig
	rsp := &userBiz.UserTermConfig{}
	if err = r.Data.Db.Model(&UserTermConfig{}).Where("uid = ?", userInfo.Uid).Find(&existingItems).Error; err != nil {
		r.Data.Log.WithContext(ctx).Errorf("query user error, err: %s", err.Error())
		return nil, err
	}
	if len(existingItems) == 0 {
		return rsp, nil
	}
	_ = copier.Copy(&rsp, existingItems[0])
	rsp.Id = existingItems[0].ID
	rsp.CreatedAt = existingItems[0].CreatedAt.Format(time.DateTime)
	rsp.UpdatedAt = existingItems[0].UpdatedAt.Format(time.DateTime)
	return rsp, nil
}

func (r *UserRepo) UpdateUserTermConfig(ctx context.Context, req *userBiz.UserTermConfig) (err error) {
	userInfo, err := common.GetUserInfoByToken(ctx)
	if err != nil {
		return err
	}
	if userInfo.Uid != req.Uid {
		return fmt.Errorf("no permission")
	}
	userTermConfig := &UserTermConfig{}
	_ = copier.Copy(&userTermConfig, req)
	if err = r.Data.Db.Model(&UserTermConfig{}).Where("id = ? ", req.Id).
		Select("font_size", "cursor_style", "scroll_back", "quick_vim_status", "common_vim_status", "alias_status", "auto_complete_status", "highlight_status", "language").
		Updates(&userTermConfig).Error; err != nil {
		r.Data.Log.WithContext(ctx).Errorf("update user term config error, err: %s", err.Error())
		return err
	}
	return nil
}

func (r *UserRepo) ListUserQuickCommand(ctx context.Context, req *userBiz.ListUserQuickCommandReq) ([]*userBiz.UserQuickCommand, int64, error) {
	db := r.Data.Db.WithContext(ctx).Unscoped().Model(&UserQuickCommand{})
	if req.Uid != 0 {
		db = db.Where("uid = ?", req.Uid)
	}
	if len(req.SearchText) != 0 {
		searchText := strings.ReplaceAll(req.SearchText, "%", "\\%")
		searchText = "%" + searchText + "%"
		db = db.Where("command like ? or alias like ?", searchText, searchText)
	}
	var count int64
	rv := make([]*userBiz.UserQuickCommand, 0)
	if err := db.Count(&count).Error; err != nil {
		return rv, 0, err
	}

	field := "id"
	desc := " desc"
	db = db.Order(field + desc)

	var UserQuickCommands []*UserQuickCommand
	if req.PageNo != 0 && req.PageSize != 0 {
		db = db.Offset(int(req.PageNo-1) * int(req.PageSize)).Limit(int(req.PageSize))
	}

	if err := db.Find(&UserQuickCommands).Error; err != nil {
		return nil, count, errors.New("list term quick command error: " + db.Error.Error())
	}

	for _, o := range UserQuickCommands {
		b := new(userBiz.UserQuickCommand)
		_ = copier.Copy(b, o)
		b.Id = int64(o.ID)
		b.CreatedAt = o.CreatedAt.Format("2006-01-02 15:04:05")
		b.UpdatedAt = o.UpdatedAt.Format("2006-01-02 15:04:05")
		rv = append(rv, b)
	}

	return rv, count, nil
}

func (r *UserRepo) CreateUserQuickCommand(ctx context.Context, req *userBiz.UserQuickCommand) error {
	if req.Uid == 0 || len(req.Alias) == 0 {
		return fmt.Errorf("uid or alias is empty")
	}
	existItems := make([]UserQuickCommand, 0)
	if err := r.Data.Db.WithContext(ctx).Where("uid = ? and alias = ?", req.Uid, req.Alias).Find(&existItems).Error; err != nil {
		r.Data.Db.Rollback()
		r.Data.Log.WithContext(ctx).Errorf("query term quick command data, err: %v", err.Error())
		return err
	}
	if len(existItems) != 0 {
		return fmt.Errorf("the alias already exists")
	}
	newTermCommand := &UserQuickCommand{
		Uid:     req.Uid,
		Command: req.Command,
		Comment: req.Comment,
		Alias:   req.Alias,
	}
	if err := r.Data.Db.WithContext(ctx).Create(&newTermCommand).Error; err != nil {
		r.Data.Db.Rollback()
		r.Data.Log.WithContext(ctx).Errorf("create term quick command data, err: %v", err.Error())
		return err
	}

	resp := new(userBiz.UserQuickCommand)
	if err := copier.Copy(resp, newTermCommand); err != nil {
		return err
	}
	resp.Id = int64(newTermCommand.ID)
	resp.CreatedAt = newTermCommand.CreatedAt.Format("2006-01-02 15:04:05")
	resp.UpdatedAt = newTermCommand.UpdatedAt.Format("2006-01-02 15:04:05")
	return nil
}

func (r *UserRepo) GetUserQuickCommand(ctx context.Context, id, uid int64) (*userBiz.UserQuickCommand, error) {
	var termCommand UserQuickCommand
	err := r.Data.Db.WithContext(ctx).Where("id = ? and uid = ?", id, uid).First(&termCommand).Error
	if err != nil {
		return nil, err
	}
	resp := new(userBiz.UserQuickCommand)
	if err := copier.Copy(resp, termCommand); err != nil {
		return nil, err
	}
	resp.Id = int64(termCommand.ID)
	resp.CreatedAt = termCommand.CreatedAt.Format("2006-01-02 15:04:05")
	resp.UpdatedAt = termCommand.UpdatedAt.Format("2006-01-02 15:04:05")

	return resp, nil
}

func (r *UserRepo) UpdateUserQuickCommand(ctx context.Context, req *userBiz.UserQuickCommand) error {
	if req.Id == 0 {
		return nil
	}
	var termCommand UserQuickCommand
	if req.Uid == 0 || len(req.Alias) == 0 {
		return fmt.Errorf("uid or alias is empty")
	}
	existItems := make([]UserQuickCommand, 0)
	if err := r.Data.Db.WithContext(ctx).Where("uid = ? and alias = ?", req.Uid, req.Alias).Find(&existItems).Error; err != nil {
		r.Data.Db.Rollback()
		r.Data.Log.WithContext(ctx).Errorf("query term quick command data, err: %v", err.Error())
		return err
	}
	if len(existItems) != 0 && existItems[0].ID != uint(req.Id) {
		return fmt.Errorf("the alias already exists")
	}
	if err := r.Data.Db.Model(&termCommand).Where("id = ? and uid = ?", req.Id, req.Uid).Select("*").Omit("id", "created_at", "uid").Updates(UserQuickCommand{Alias: req.Alias, Command: req.Command, Comment: req.Comment}).Error; err != nil {
		r.Data.Db.Rollback()
		r.Data.Log.WithContext(ctx).Errorf("update term quick command data, err: %v", err.Error())
		return err
	}

	return nil
}

func (r *UserRepo) DeleteUserQuickCommand(ctx context.Context, id, uid int64) error {
	return r.Data.Db.WithContext(ctx).Unscoped().Model(&UserQuickCommand{}).Where("id = ? and uid = ?", id, uid).Delete(&UserQuickCommand{}).Error
}

func (r *UserRepo) CheckUserDevice(ctx context.Context, req *userBiz.CheckUserDeviceReq) (bool, error) {
	ipMacAddressMap := make(map[string]string, 0)
	url := r.Data.Conf.OfficeDeviceApi
	timestamp := time.Now().Unix()
	t := time.Unix(timestamp, 0)
	date := t.Format("20060102")                 // Go 的时间格式化字符串
	key := fmt.Sprintf("%d_%s", timestamp, date) // 拼接时间戳和年月日
	hash := md5.Sum([]byte(key))                 // 计算 MD5 哈希
	sign := hex.EncodeToString(hash[:])          // 转换为十六进制字符串
	officeUrl := url + "?sign=" + sign + "&timestamp=" + fmt.Sprintf("%d", timestamp)
	officeData := map[string]interface{}{
		"user_email": req.Email,
		"offset":     0,
		"limit":      100,
	}
	sourceResp, err := r.Data.Client.HttpPostJson(officeUrl, officeData, nil)
	if err != nil {
		r.Data.Log.Infof("get office api info err: %v", err)
		return false, err
	}
	result := sourceResp.(map[string]interface{})["data"].(interface{})
	resultData := result.(map[string]interface{})["list"].([]interface{})
	for index := range resultData {
		ip := resultData[index].(map[string]interface{})["fixed_address"].(string)
		macAddress := strings.ToLower(resultData[index].(map[string]interface{})["hardware_ethernet"].(string))
		ipMacAddressMap[ip] = macAddress
		if ip == req.Ip && macAddress == strings.ToLower(req.MacAddress) {
			return true, nil
		}
	}
	if len(ipMacAddressMap) == 0 {
		return false, nil
	}
	usersStr, _ := json.Marshal(ipMacAddressMap)
	fmt.Println(string(usersStr))
	return false, nil

}

func (r *UserRepo) GetUserTermPublicKey(ctx context.Context, uid int64, organization, role string) string {
	var publicKey string
	pubColumn := "user_public_key"
	if role == "root" {
		pubColumn = "public_key"
	}
	err := r.Data.Db.Model(&UserTermConfig{}).Select(pubColumn).Where("uid = ? ", uid).Scan(&publicKey).Error
	if err != nil || publicKey == "" {
		return ""
	}
	secretKey := []byte(r.Data.Conf.OptSecretKey)
	decryptPublicKey, err := cryptoUtil.Decrypt(secretKey, publicKey)
	if err != nil {
		r.Log.Errorf("Decrypt User Term Public Key, err: %v", err.Error())
		return ""
	}
	return decryptPublicKey
}

func (r *UserRepo) CreateOrUpdateUserPublicKey(ctx context.Context, uid int64, organizationId, email, role, publicKey, privateKey string) string {
	var userTermConfig *UserTermConfig
	r.Data.Db.Model(&UserTermConfig{}).Where("uid = ? ", uid).First(&userTermConfig)
	userData := &UserTermConfig{}
	// 兼容堡垒机

	secretKey := []byte(r.Data.Conf.OptSecretKey)
	decryptPublicKey, err := cryptoUtil.Encrypt(secretKey, publicKey)
	decryptPrivateKey, err := cryptoUtil.Encrypt(secretKey, privateKey)
	if err != nil {
		r.Log.Errorf("Decrypt User Term Public Key, err: %v", err.Error())
		return ""
	}
	if role == "root" {
		userData.PublicKey = decryptPublicKey
		userData.PrivateKey = decryptPrivateKey
	} else {
		userData.UserPublicKey = decryptPublicKey
		userData.UserPrivateKey = decryptPrivateKey
	}

	if userTermConfig.ID == 0 {
		//userData.OrganizationId = organizationId
		userData.User = strings.Split(email, "@")[0]
		userData.Email = email
		userData.Uid = uid
		err := r.Data.Db.Model(&UserTermConfig{}).Create(userData).Error
		if err != nil {
			r.Log.WithContext(ctx).Errorf("create user term config error, err: %s", err.Error())
			return err.Error()
		}
	} else {
		err := r.Data.Db.Model(&UserTermConfig{}).Where("id = ?", userTermConfig.ID).Updates(userData).Error
		if err != nil {
			r.Log.WithContext(ctx).Errorf("update user term config error, err: %s", err.Error())
			return err.Error()
		}
	}
	return ""
}

func (r *UserRepo) GetUserInfoByEmail(ctx context.Context, email, organizationId string) ([]userBiz.User, error) {
	db := r.Data.Db.Table("t_users")
	db = db.Debug().Where("email like ?", "%"+email+"%")

	if organizationId != "" {
		db = db.Debug().Where("organization_id", organizationId)

	}

	var finds []userBiz.User
	db.Find(&finds)
	return finds, nil
}

func (r *UserRepo) UpdateUser(ctx context.Context, req *userBiz.UpdateUserReq) (rsp *userBiz.User, err error) {
	r.Data.Log.WithContext(ctx).Infof("UpdateUser: %v", req.Uid)
	var existingItem *User
	user := &User{}
	_ = copier.Copy(&user, req)
	err = r.Data.Db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		if err = tx.Model(&User{}).Where("uid = ? and registration_type = 2", req.Uid).First(&existingItem).Error; err != nil {
			r.Data.Log.WithContext(ctx).Errorf("query user error, err: %s", err.Error())
			return fmt.Errorf("query user error, err: %s", err.Error())
		}
		checkUsernames := make([]*User, 0)
		if err = tx.Model(&User{}).Where("username = ?", req.Username).Find(&checkUsernames).Error; err != nil {
			r.Data.Log.WithContext(ctx).Errorf("check username error, err: %s", err.Error())
			return fmt.Errorf("check username error, err: %s", err.Error())
		}
		_ = copier.Copy(&user, req)
		if len(checkUsernames) != 0 && checkUsernames[0].Uid != req.Uid {
			return fmt.Errorf("username exist")
		}
		if err = tx.Model(&User{}).Where("uid = ? ", req.Uid).
			Updates(&user).Error; err != nil {
			r.Data.Log.WithContext(ctx).Errorf("update user config error, err: %s", err.Error())
			return err
		}
		return nil
	})
	if err != nil {
		return nil, err
	}

	rsp = &userBiz.User{}
	_ = copier.Copy(&rsp, user)
	rsp.Id = user.ID
	rsp.CreatedAt = user.CreatedAt.Format(time.DateTime)
	rsp.UpdatedAt = user.UpdatedAt.Format(time.DateTime)
	return rsp, nil
}
