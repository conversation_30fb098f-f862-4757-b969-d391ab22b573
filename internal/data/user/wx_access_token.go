package data

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/go-kratos/kratos/contrib/config/consul/v2"
	"github.com/go-redis/redis/v8"
	"github.com/hashicorp/consul/api"
	"gopkg.in/resty.v1"
)

type RespCommon struct {
	ErrCode int    `json:"errcode"`
	ErrMsg  string `json:"errmsg"`
}

type RespAccessToken struct {
	RespCommon
	AccessToken   string `json:"access_token"`
	ExpiresInSecs int    `json:"expires_in"`
}

type WechatInfo struct {
	Agentid    string `json:"agentid"`
	Corpid     string `json:"corpid"`
	Corpsecret string `json:"corpsecret"`
}

func (rp *Data) parseInstanceRequest(path string, client *api.Client, req interface{}) error {
	cs, err := consul.New(client, consul.WithPath(path))
	if err != nil {
		return err
	}
	kvs, err := cs.Load()
	if err != nil {
		return err
	}
	if len(kvs) == 0 {
		return fmt.Errorf("can not find path: %s in consul", path)
	}
	if err = json.Unmarshal(kvs[0].Value, req); err != nil {
		return err
	}
	return nil
}

func (rp *Data) GetAccessToken(ctx context.Context) (string, error) {
	//wxInfo := new(WechatInfo)
	//if err := rp.parseInstanceRequest("cloud/workflow/config/wechat", rp.configClient, wxInfo); err != nil {
	//	rp.log.Infof("get consul info err: %v", err)
	//	return err.Error()
	//}
	accessTokenCacheKey := fmt.Sprintf("access_token_%s", rp.WecomConfig.AgentId)
	val, err := rp.Rdb.Get(ctx, accessTokenCacheKey).Result()
	if err == redis.Nil {
		// 从微信服务器获取
		var result RespAccessToken
		result, err := rp.GetAccessTokenFromServer(ctx, rp.WecomConfig.AgentId, rp.WecomConfig.CorpId, rp.WecomConfig.CorpSecret)
		if err != nil {
			rp.Log.Errorf("get server access token error, %v", err)
			return "", err
		}
		return result.AccessToken, nil
	} else if err != nil {
		rp.Log.Errorf("get redis access token error, %v", err)
		return "", err
	} else {
		return val, nil
	}
}

func (rp *Data) GetAccessTokenFromServer(ctx context.Context, agentid, corpid, corpsecret string) (RespAccessToken, error) {
	apiPath := "/cgi-bin/gettoken"
	resty.SetHostURL("https://qyapi.weixin.qq.com")
	resty.SetQueryParam("corpid", corpid)
	resty.SetQueryParam("corpsecret", corpsecret)
	//resty.SetDebug(os.Getenv("DEBUG") == "true")

	var result RespAccessToken
	resp, err := resty.R().SetResult(&result).Get(apiPath)
	if err != nil {
		return RespAccessToken{}, err
	}

	// 40001 不合法的secret参数
	// 40013 不合法的CorpID
	// 40056 不合法的agentid
	// access_token是分应用的，获取access_token 无需AgentID
	// 但是操作某些api 需要传AgentID
	if result.ErrCode == 40013 {
		rp.Log.Error("错误码 40013,请检查CorpID 参数")
	}

	if result.ErrCode == 40001 {
		rp.Log.Error("错误码 40001,请检查下CorpSecret 参数是否设置或者正确, 提示: 如果你在管理工具>通讯录同步中重置过Secret, 请注意更换CONTACT_APP_SECRET env")
	}

	if len(resp.Body()) == 0 {
		rp.Log.Error("在获取token的时候, 企业微信服务器的接口返回了空的Body。 检查CorpId和CorpSecret是否正确")
	}

	// 全局错误码 https://work.weixin.qq.com/api/doc#90000/90139/90313
	// -1 表示系统繁忙
	if result.ErrCode == -1 { //nolint
		rp.Log.Error("在获取token的时候, 系统繁忙")
	}

	accessTokenCacheKey := fmt.Sprintf("access_token_%s", agentid)
	expires := result.ExpiresInSecs - 900

	err = rp.Rdb.Set(ctx, accessTokenCacheKey, result.AccessToken, time.Duration(expires)*time.Second).Err()
	if err != nil {
		rp.Log.Errorf("Redis保存 access_token 失败：%v", err)
	}

	return result, nil
}
