package data

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io/ioutil"
	"net/http"
	"strconv"
	"time"

	"github.com/go-kratos/kratos/v2/transport"
	"github.com/go-redis/redis/v8"
	jwt5 "github.com/golang-jwt/jwt/v5"
	"github.com/jinzhu/copier"
	"github.com/tidwall/gjson"

	biz "gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/internal/biz/user"
	userBiz "gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/internal/biz/user"
	"gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/pkg/notice"
	"gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/pkg/util/common"
	"gorm.io/gorm"
)

func (r *UserRepo) UserLoginPwdCheck(ctx context.Context, req *userBiz.UserLoginPwdReq) (rsp *userBiz.User, err error) {
	// 1. 查看是否被锁定
	waitSecond, _ := r.SecretKeyErrorWait(ctx, req.Username)
	if waitSecond > 0 {
		return nil, fmt.Errorf("请 %d 秒后再进行尝试", waitSecond)
	}
	// 2. 校验密码是否正确
	users := make([]*User, 0)
	if err := r.Data.Db.WithContext(ctx).Where("username = ?", req.Username).Find(&users).Error; err != nil {
		r.Data.Log.WithContext(ctx).Errorf("find User data, err: %v", err.Error())
		return nil, err
	}
	if len(users) == 0 {
		return nil, errors.New("用户不存在")
	}
	if req.Password != users[0].Password {
		// 3. 密码错误则记录错误次数
		remainingTimes, _ := r.SecretKeyErrorCount(ctx, users[0].Username, false)
		if remainingTimes == 0 {
			return nil, errors.New("密码连续5次错误输入，已被锁定1分钟")
		} else {
			return nil, fmt.Errorf("密码错误, 再输错 %d 次后，将被锁定1分钟", remainingTimes)
		}
	} else {
		// 4. 密码正确则清除错误次数
		_, _ = r.SecretKeyErrorCount(ctx, users[0].Username, true)
	}
	rsp = &userBiz.User{}
	_ = copier.Copy(&rsp, users[0])
	rsp.Id = users[0].ID
	rsp.CreatedAt = users[0].CreatedAt.Format(time.DateTime)
	rsp.UpdatedAt = users[0].UpdatedAt.Format(time.DateTime)

	return rsp, nil
}

func (r *UserRepo) UserLoginEmailCheck(ctx context.Context, req *userBiz.UserLoginEmailVerificationReq) (err error) {
	key := fmt.Sprintf("UserLoginEmailSendCode-%s", req.Email)
	value, err := r.Data.Rdb.Get(ctx, key).Result()
	if err != nil && err != redis.Nil {
		return err
	}
	if len(value) == 0 {
		r.Data.Log.WithContext(ctx).Errorf("[UserLoginEmailCheck] 校验失败，验证码无效， Email: %s, 用户验证码: %s", req.Email, req.Code)
		return fmt.Errorf("校验失败，验证码无效")
	}
	if value != req.Code {
		r.Data.Log.WithContext(ctx).Errorf("[UserLoginEmailCheck] 校验失败，验证码错误， 手机号: %s, 用户验证码: %d", req.Email, req.Code)
		return fmt.Errorf("校验失败，验证码错误")
	}

	return nil
}

func (r *UserRepo) SecretKeyErrorCount(ctx context.Context, username string, correct bool) (int, error) {
	redKey := fmt.Sprintf("password-manager-%s", username)
	exists, err := r.Data.Rdb.Exists(ctx, redKey).Result()
	if err != nil {
		r.Data.Log.WithContext(ctx).Infof("get redis info: %v", err)
		return 0, fmt.Errorf("failed to get redis info, error: %v", err)
	}
	// 1.如果密码输入正确
	if correct {
		if exists != 0 {
			r.Data.Rdb.Del(ctx, redKey)
		}
		return 5, nil
	}
	// 2.如果密码输入错误
	ttl := 60 * time.Second
	if exists == 0 {
		r.Data.Rdb.Set(ctx, redKey, "1", ttl)
	} else {
		_, _ = r.Data.Rdb.Incr(ctx, redKey).Result()
		_ = r.Data.Rdb.Expire(ctx, redKey, ttl).Err()
	}
	val, _ := r.Data.Rdb.Get(ctx, redKey).Result()
	valNum, _ := strconv.Atoi(val)
	if len(val) != 0 && valNum >= 5 {
		redKeyWait := fmt.Sprintf("password-manager-wait-%s", username)
		r.Data.Rdb.Set(ctx, redKeyWait, "", ttl)
		r.Data.Rdb.Del(ctx, redKey)
		return 0, nil
	}

	return 5 - valNum, nil
}

func (r *UserRepo) SecretKeyErrorWait(ctx context.Context, username string) (int, error) {
	// 获取 Key 的过期时间
	redKeyWait := fmt.Sprintf("password-manager-wait-%s", username)
	ttl := r.Data.Rdb.TTL(ctx, redKeyWait).Val()
	if ttl == time.Duration(-2) {
		return 0, nil
	} else if ttl == time.Duration(-1) {
		return 0, nil
	} else {
		r.Log.Warnf("Key will expire in %s\n", ttl)
		return int(ttl.Seconds()), nil
	}
}

func (r *UserRepo) UserLoginEmailSendCode(ctx context.Context, req *userBiz.UserLoginEmailSendCodeReq) error {
	key := fmt.Sprintf("UserLoginEmailSendCode-%s", req.Email)
	codeNum := notice.GenValidateCode(6)
	var err error
	value, err := r.Data.Rdb.Get(ctx, key).Result()
	if err != nil && err != redis.Nil {
		return fmt.Errorf("failed to get redis info, error: %v", err)
	}
	if value == "" {
		r.Data.Rdb.Set(ctx, key, codeNum, 5*time.Minute)
	} else {
		ttl := r.Data.Rdb.TTL(ctx, key).Val()
		return fmt.Errorf("please try again in %d seconds", int64(ttl.Seconds()))
	}

	content := fmt.Sprintf(`
    <html>
    <body>
        <p>尊敬的 Chaterm 用户，您的验证码为：</p>
		<p style="text-align: center; margin: 20px 0;">
        	<b style="font-size: 28px; color: #3366cc; background-color: #f5f5f5; padding: 10px 20px; border-radius: 4px;">%s</b>
        </p>
        <p>5分钟内有效,请及时填写。如果您并未请求此验证码，则可能是他人正在尝试访问以下账号：%s。请勿将此验证码转发给或提供给任何人。</p>
        <p>此致</p>
        <p>Chaterm 团队敬上</p>
    </body>
    </html>
    `, codeNum, req.Email)

	err = notice.SendEmailHTML(r.Data.EmailConfig, []string{req.Email}, "CTM 验证码", content)
	if err != nil {
		r.Data.Log.Warnf("failed to send verification code to email, error: %v", err)
		return fmt.Errorf("failed to send verification code to email, error: %v", err)
	}

	return nil
}

func (r *UserRepo) UpsertUserPersonal(ctx context.Context, req *userBiz.User) (*userBiz.User, error) {
	r.Data.Log.WithContext(ctx).Infof("UpdateUser: %v", req.Uid)
	var existingItems []*User
	user := &User{}
	var err error
	err = r.Data.Db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		if err = tx.Model(&User{}).Where("email = ? and registration_type = 2", req.Email).Find(&existingItems).Error; err != nil {
			r.Data.Log.WithContext(ctx).Errorf("query user error, err: %s", err.Error())
			return fmt.Errorf("failed to query user by email, error: %v", err)
		}
		_ = copier.Copy(&user, req)
		if len(existingItems) != 0 {
			user = existingItems[0]
		} else {
			user.Avatar = "https://wework.qpic.cn/wwpic3az/559591_7cVmSC3_RBGhMRh_1728899662/0"
			user.Username = req.Email
			if err = tx.Model(&User{}).Create(&user).Error; err != nil {
				r.Data.Log.WithContext(ctx).Errorf("create user error, err: %s", err.Error())
				return fmt.Errorf("create user error, err: %v", err)
			}
			uid := userPersonalBase + uint64(user.ID)
			user.Uid = int64(uid)
			user.Name = fmt.Sprintf("user_%d", uid)
			if err = tx.Model(&User{}).Where("id = ?", user.ID).Updates(&user).Error; err != nil {
				r.Data.Log.WithContext(ctx).Errorf("update uid of user error, err: %s", err.Error())
				return fmt.Errorf("update uid of user error, err: %v", err)
			}
			if _, err = r.GenerateLiteLLMKey(biz.UserAccountInfo{
				Email:        user.Email,
				Uid:          user.Uid,
				Subscription: "lite",
			}); err != nil {
				r.Data.Log.WithContext(ctx).Errorf("generate liteLLM key error, err: %s", err.Error())
				//return fmt.Errorf("enerate liteLLM key error, err: %v", err)
			}
		}
		existingUserTermConfigs := make([]*UserTermConfig, 0)
		if err = tx.Model(&UserTermConfig{}).Where("uid = ?", req.Uid).Find(&existingUserTermConfigs).Error; err != nil {
			r.Data.Log.WithContext(ctx).Errorf("query user config error, err: %s", err.Error())
			return fmt.Errorf("query user config error, err: %v", err)
		}
		userConfig := &UserTermConfig{
			Uid:   user.Uid,
			User:  user.Name,
			Email: user.Email,
		}
		if len(existingUserTermConfigs) == 0 {
			if err := tx.Model(&UserTermConfig{}).Create(&userConfig).Error; err != nil {
				r.Data.Log.WithContext(ctx).Errorf("create user config error, err: %s", err.Error())
				return fmt.Errorf("create user config error, err: %v", err)
			}
		}
		return nil
	})
	if err != nil {
		return nil, err
	}

	rsp := &userBiz.User{}
	_ = copier.Copy(&rsp, user)
	rsp.Id = user.ID
	rsp.CreatedAt = user.CreatedAt.Format(time.DateTime)
	rsp.UpdatedAt = user.UpdatedAt.Format(time.DateTime)
	return rsp, nil
}

func (r *UserRepo) UpsertUserSSO(ctx context.Context, req *userBiz.User) (*userBiz.User, error) {
	r.Data.Log.WithContext(ctx).Infof("UpdateUser: %v", req.Uid)
	var existingItems []*User
	user := &User{}
	var err error
	err = r.Data.Db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		if err = tx.Model(&User{}).Where("uid = ? and registration_type = 1", req.Uid).Find(&existingItems).Error; err != nil {
			r.Data.Log.WithContext(ctx).Errorf("query user error, err: %s", err.Error())
			return fmt.Errorf("query user error, err: %s", err.Error())
		}
		_ = copier.Copy(&user, req)
		if len(existingItems) != 0 {
			if err := tx.Model(&User{}).Where("id = ?", existingItems[0].ID).Updates(user).Error; err != nil {
				r.Data.Log.WithContext(ctx).Errorf("update user error, err: %s", err.Error())
				return fmt.Errorf("update user error, err: %s", err.Error())
			}
			user = existingItems[0]
		} else {
			if err := tx.Model(&User{}).Create(&user).Error; err != nil {
				r.Data.Log.WithContext(ctx).Errorf("create user error, err: %s", err.Error())
				return fmt.Errorf("create user error, err: %s", err.Error())
			}
			if _,err = r.GenerateLiteLLMKey(biz.UserAccountInfo{
				Email:        user.Email,
				Uid:          user.Uid,
				Subscription: "lite",
			}); err != nil {
				r.Data.Log.WithContext(ctx).Errorf("generate liteLLM key error, err: %s", err.Error())
				//return fmt.Errorf("enerate liteLLM key error, err: %v", err)
			}
		}
		existingUserTermConfigs := make([]*UserTermConfig, 0)
		if err = tx.Model(&UserTermConfig{}).Where("uid = ?", req.Uid).Find(&existingUserTermConfigs).Error; err != nil {
			r.Data.Log.WithContext(ctx).Errorf("query user config error, err: %s", err.Error())
			return fmt.Errorf("query user config error, err: %s", err.Error())
		}
		if len(existingUserTermConfigs) == 0 {
			userConfig := &UserTermConfig{
				Uid:   user.Uid,
				User:  user.Name,
				Email: user.Email,
			}
			if err := tx.Model(&UserTermConfig{}).Create(&userConfig).Error; err != nil {
				r.Data.Log.WithContext(ctx).Errorf("create user config error, err: %s", err.Error())
				return fmt.Errorf("create user config error, err: %s", err.Error())
			}
		}
		return nil
	})
	if err != nil {
		return nil, err
	}

	rsp := &userBiz.User{}
	_ = copier.Copy(&rsp, user)
	rsp.Id = user.ID
	rsp.PlatformId = r.Data.Sso.GetPlatformId()
	rsp.CreatedAt = user.CreatedAt.Format(time.DateTime)
	rsp.UpdatedAt = user.UpdatedAt.Format(time.DateTime)
	return rsp, nil
}

func (r *UserRepo) UpdateUserPwd(ctx context.Context, req *userBiz.UpdateUserPwdReq) error {
	r.Data.Log.WithContext(ctx).Infof("UpdateUser: %v", req.Uid)
	existingItem := &User{}
	var err error
	err = r.Data.Db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		if err = r.Data.Db.WithContext(ctx).Model(&User{}).Where("uid = ? and registration_type = 2", req.Uid).First(&existingItem).Error; err != nil {
			r.Data.Log.WithContext(ctx).Errorf("query user error, err: %s", err.Error())
			return fmt.Errorf("query user error, err: %s", err.Error())
		}
		if err := tx.Model(&User{}).Where("id = ?", existingItem.ID).Update("password", req.Password).Error; err != nil {
			r.Data.Log.WithContext(ctx).Errorf("update user password error, err: %s", err.Error())
			return fmt.Errorf("update user password error, err: %s", err.Error())
		}
		return nil
	})
	if err != nil {
		return err
	}

	return nil
}

func (r *UserRepo) AuthVerifyToken(token, platForm string) (*userBiz.SSOUserInfo, error) {
	userInfo := &userBiz.SSOUserInfo{}
	url := r.Data.Sso.GetApi() + "/auth/verify-access-token?token=" + token
	requestParam := map[string]string{
		"platform_id": r.Data.Sso.GetPlatformId(),
	}
	if platForm == userBiz.PlatFormWeb {
		requestParam["platform_id"] = r.Data.Sso.GetWebPlatformId()
	}
	_, responseData, err := r.Data.Client.PostJson(url, token, requestParam)
	if err != nil {
		return userInfo, fmt.Errorf("failed to get sso info, err: %s", err.Error())
	}
	responseDataStr := string(responseData)
	if 0 != gjson.Get(responseDataStr, "code").Int() {
		return nil, fmt.Errorf("AuthVerifyToken fail. Data:%s", responseDataStr)
	}
	userInfo = &userBiz.SSOUserInfo{
		Uid:                      gjson.Get(responseDataStr, "data.uid").Int(),
		Email:                    gjson.Get(responseDataStr, "data.email").String(),
		Name:                     gjson.Get(responseDataStr, "data.name").String(),
		Avatar:                   gjson.Get(responseDataStr, "data.avatar").String(),
		EnName:                   gjson.Get(responseDataStr, "data.en_name").String(),
		Mobile:                   gjson.Get(responseDataStr, "data.mobile").String(),
		Status:                   gjson.Get(responseDataStr, "data.status").Int(),
		OrganizationOid:          gjson.Get(responseDataStr, "data.organization_oid").Int(),
		DepartmentOid:            gjson.Get(responseDataStr, "data.department_oid").Int(),
		DepartmentName:           gjson.Get(responseDataStr, "data.department_name").String(),
		DepartmentTreePath:       gjson.Get(responseDataStr, "data.department_tree_path").String(),
		DepartmentTreePathStr:    gjson.Get(responseDataStr, "data.department_tree_path_str").String(),
		StraightLineManagerUid:   gjson.Get(responseDataStr, "data.straight_line_manager_uid").Int(),
		StraightLineManagerEmail: gjson.Get(responseDataStr, "data.straight_line_manager_email").String(),
		StraightLineManagerName:  gjson.Get(responseDataStr, "data.straight_line_manager_name").String(),
		MentorUid:                gjson.Get(responseDataStr, "data.mentor_uid").Int(),
		MentorEmail:              gjson.Get(responseDataStr, "data.mentor_email").String(),
		MentorName:               gjson.Get(responseDataStr, "data.mentor_name").String(),
		PlatformId:               r.Data.Sso.GetPlatformId(),
	}
	weChatWorkId, _ := r.describeUsersWeChatWorkInfo(userInfo.Email)
	if len(weChatWorkId) > 0 {
		userInfo.WeChatWorkId = weChatWorkId
	}

	return userInfo, nil
}

func (r *UserRepo) describeUsersWeChatWorkInfo(email string) (string, error) {
	accessToken, err := r.Data.GetAccessToken(context.Background())
	url := "https://qyapi.weixin.qq.com/cgi-bin/user/get_userid_by_email?access_token=" + accessToken
	data := make(map[string]interface{})
	data["email"] = email
	data["email_type"] = 1 // 1-企业邮箱（默认）；2-个人邮箱
	bytesData, _ := json.Marshal(data)
	req, _ := http.NewRequest("POST", url, bytes.NewReader(bytesData))
	client := &http.Client{}
	resp, _ := client.Do(req)
	var result map[string]interface{}
	body, err := ioutil.ReadAll(resp.Body)
	if err == nil {
		err = json.Unmarshal(body, &result)
	}
	code := result["errcode"].(float64)
	if code != 0 {
		return "", fmt.Errorf("failed to get wecom info")
	}

	return result["userid"].(string), err
}

func (r *UserRepo) UserLoginOutSSO(ctx context.Context, platForm string) (err error) {
	userInfo, err := common.GetUserInfoByToken(ctx)
	if err != nil {
		return err
	}
	if userInfo.RegistrationType != 1 {
		return nil
	}
	tokenName := "ctm-token"
	if platForm == userBiz.PlatFormWeb {
		tokenName = "ctm-web-token"
	}
	var token string
	if tr, ok := transport.FromServerContext(ctx); ok {
		token = tr.RequestHeader().Get(tokenName)
	}
	if len(token) > 0 {
		_, err = r.loginOutSSO(token, platForm)
		if err != nil {
			return
		}
	} else {
		return fmt.Errorf("missing token")
	}

	return nil
}

func (r *UserRepo) loginOutSSO(token, platForm string) (msg string, err error) {
	url := r.Data.Sso.GetApi() + "/auth/jwt/v1/login-out"
	requestParam := map[string]string{
		"platform_id": r.Data.Sso.GetPlatformId(),
	}
	if platForm == userBiz.PlatFormWeb {
		requestParam["platform_id"] = r.Data.Sso.GetWebPlatformId()
	}
	_, responseData, err := r.Data.Client.PostJson(url, token, requestParam)
	if err != nil {
		return "", err
	}
	msg = string(responseData)
	return
}

func (r *UserRepo) UserLoginOutAddBlackList(ctx context.Context) (err error) {
	jwtToken, err := common.GetUserToken(ctx)
	if err != nil {
		r.Data.Log.WithContext(ctx).Errorf("get user info from token error, err:%s", err.Error())
		return fmt.Errorf("get user info from token error, err:%s", err.Error())
	}
	// 解析 Token
	parsedToken, err := jwt5.Parse(jwtToken, func(token *jwt5.Token) (interface{}, error) {
		return userBiz.JwtSecret, nil
	})
	if err != nil {
		r.Data.Log.WithContext(ctx).Errorf("parse token error: err:%s", err.Error())
		return err
	}
	// 获取 Token 过期时间
	claims, ok := parsedToken.Claims.(jwt5.MapClaims)
	if !ok {
		return fmt.Errorf("invalid token claims")
	}
	expirationTime, _ := claims.GetExpirationTime()
	duration := expirationTime.Time.Sub(time.Now()).Minutes()
	// 将 jwtToken 加入黑名单
	tokenKey := fmt.Sprintf("UserLoginBlackToken:%s", jwtToken)
	err = r.Data.Rdb.Set(context.Background(), tokenKey, "1", time.Duration(duration)*time.Minute).Err()
	if err != nil {
		r.Data.Log.WithContext(ctx).Errorf("set redis error, err:%s, key:%s", err.Error(), tokenKey)
		return err
	}

	return nil
}

func (r *UserRepo) IsExistInJwtBlackList(ctx context.Context) (exist bool, err error) {
	token, err := common.GetUserToken(ctx)
	if err != nil {
		r.Data.Log.WithContext(ctx).Errorf("get user info from token error, err:%s", err.Error())
		return false, err
	}
	tokenKey := fmt.Sprintf("UserLoginBlackToken:%s", token)
	tokenResult, _ := r.Data.Rdb.Get(context.Background(), tokenKey).Result()
	if tokenResult == "1" {
		return true, nil
	}

	return false, nil
}
