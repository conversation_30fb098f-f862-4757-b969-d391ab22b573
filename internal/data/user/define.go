package data

import (
	"time"

	"gorm.io/gorm"
)

var userPersonalBase uint64 = 2000000

type User struct {
	ID                       uint           `gorm:"column:id;type:int;primary_key;AUTO_INCREMENT;comment:'模型ID'"`
	CreatedAt                time.Time      `gorm:"column:created_at;type:TIMESTAMP;default:CURRENT_TIMESTAMP;comment:'创建时间'"`
	UpdatedAt                time.Time      `gorm:"column:updated_at;type:TIMESTAMP;default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;comment:'更新时间'"`
	DeletedAt                gorm.DeletedAt `gorm:"column:deleted_at;comment:'删除时间'"`
	Uid                      int64          `gorm:"default:0;type:int comment 'uid'"`
	Email                    string         `gorm:"type:varchar(128) comment '邮箱'"`
	Name                     string         `gorm:"type:varchar(128) comment '名字'"`
	Avatar                   string         `gorm:"type:varchar(128) comment '头像'"`
	EnName                   string         `gorm:"type:varchar(128) comment '英文名字'"`
	Mobile                   string         `gorm:"type:varchar(128) comment '手机'"`
	Username                 string         `gorm:"type:varchar(128) comment '账号'"`
	Password                 string         `gorm:"type:varchar(128) comment '密码'"`
	Status                   int64          `gorm:"default:0;type:int comment '人员状态:1待入职,2试用,3正式,4调出,5待调入,6退休,8离职,12非正式'"`
	SecondaryOrganization    string         `gorm:"type:varchar(128)  comment '二级组织'"`
	TertiaryOrganization     string         `gorm:"type:varchar(128)  comment '三级组织'"`
	Team                     string         `gorm:"type:varchar(128)  comment '团队'"`
	DepartmentName           string         `gorm:"type:varchar(128) comment '部门名字'"`
	StraightLineManagerUid   int64          `gorm:"default:0;type:int comment '直线经理uid'"`
	StraightLineManagerEmail string         `gorm:"type:varchar(128) comment '直线经理邮箱'"`
	StraightLineManagerName  string         `gorm:"type:varchar(128) comment '直线经理名字'"`
	WeChatWorkId             string         `gorm:"type:varchar(128) comment '企业微信ID'"`
	ConsoleStatus            int64          `gorm:"default:2;type:int comment '是否开启控制台:1-开启,2-关闭'"`
	RegistrationType         int64          `gorm:"default:2;type:int comment '用户类型:1-SSO,2-个人用户'"`
}

type UserTermConfig struct {
	gorm.Model
	Uid                int64  `gorm:"default:0;type:int comment 'uid'"`
	User               string `gorm:"type:varchar(32) comment '用户名';not null"`
	Email              string `gorm:"type:varchar(64) comment '用户邮箱';not null"`
	AuthModel          string `gorm:"default:publicKey;type:varchar(16) comment 'publicKey:密钥认证,password:密码认证'"`
	Password           string `gorm:"type:varchar(64) comment '密码'"`
	PrivateKey         string `gorm:"type:text comment '私钥'"`
	PublicKey          string `gorm:"type:text comment '公钥'"`
	UserPrivateKey     string `gorm:"type:text comment 'User私钥'"`
	UserPublicKey      string `gorm:"type:text comment 'User公钥'"`
	Comment            string `gorm:"type:varchar(128) comment '备注'"`
	FontSize           int64  `gorm:"default:12;type:int comment '字体大小'"`
	CursorStyle        string `gorm:"default:bar;type:varchar(16) comment '光标样式, block|bar|underline'"`
	ScrollBack         int64  `gorm:"default:1000;type:int comment '终端中的回滚量'"`
	Menu               string `gorm:"type:text comment '定制化菜单'"`
	QuickVimStatus     int64  `gorm:"default:2;type:int comment '快捷定制化vim, -1未开启，1开启，2关闭'"`
	CommonVimStatus    int64  `gorm:"default:2;type:int comment '常规定制化vim，1开启，2关闭'"`
	AliasStatus        int64  `gorm:"default:2;type:int comment '全局alias开关，1-开启，2-关闭'"`
	AutoCompleteStatus int64  `gorm:"default:2;type:int comment '全局自动补全开关，1-开启，2-关闭'"`
	HighlightStatus    int64  `gorm:"default:2;type:int comment '全局高亮开关，1-开启，2-关闭'"`
	Language           string `gorm:"default:zh-CN;type:varchar(64) comment '语言:en-US,zh-CN'"`
}

type UserQuickCommand struct {
	gorm.Model
	Uid     int64  `gorm:"type:varchar(64) comment '用户 uid';not null"`
	Alias   string `gorm:"type:varchar(256) comment '别名'"`
	Command string `gorm:"type:text comment '命令'"`
	Comment string `gorm:"type:varchar(128) comment '备注'"`
}

type UserKey struct {
	Id        uint64      `gorm:"column:id;primary_key; auto_increment;comment:'表ID'"`
	CreatedAt time.Time `gorm:"column:created_at;type:TIMESTAMP;default:CURRENT_TIMESTAMP;comment:'创建时间'"`
	UpdatedAt time.Time `gorm:"column:updated_at;type:TIMESTAMP;default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;comment:'更新时间'"`
	DeletedAt gorm.DeletedAt `gorm:"index"`
	Uid       int64     `gorm:"type:varchar(64) comment '用户 uid';not null"`
	Email     string    `gorm:"type:varchar(64) comment '用户邮箱';not null"`
	Key       string    `gorm:"type:text comment '模型认证 key'"`
	Subscription  string    `gorm:"type:varchar(64) comment '类型: 试用lite,订阅 pro,订阅 ultra'"`
	Comment   string    `gorm:"type:varchar(128) comment '备注'"`
}
