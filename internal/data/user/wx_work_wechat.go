package data

import (
	"context"
	"fmt"
	"os"
	"strings"

	"github.com/jinzhu/copier"
	"gopkg.in/resty.v1"

	userBiz "gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/internal/biz/user"
)

type ReqSendCardBtn struct {
	Touser       string          `json:"touser"`
	Msgtype      string          `json:"msgtype"`
	Agentid      int             `json:"agentid"`
	TemplateCard BtnTemplateCard `json:"template_card"`
}
type MainTitle struct {
	Title string `json:"title"`
	Desc  string `json:"desc"`
}
type HorizontalContentList struct {
	Keyname string `json:"keyname"`
	Value   string `json:"value"`
	Type    int    `json:"type,omitempty"`
	URL     string `json:"url,omitempty"`
}
type ButtonList struct {
	Text  string `json:"text"`
	Style int    `json:"style"`
	Key   string `json:"key"`
}
type BtnTemplateCard struct {
	CardType              string                  `json:"card_type"`
	TaskID                string                  `json:"task_id"`
	MainTitle             MainTitle               `json:"main_title"`
	HorizontalContentList []HorizontalContentList `json:"horizontal_content_list"`
	SubTitleText          string                  `json:"sub_title_text"`
	ButtonList            []ButtonList            `json:"button_list"`
}

type ReqSendCard struct {
	Touser       string       `json:"touser"`
	Msgtype      string       `json:"msgtype"`
	Agentid      int          `json:"agentid"`
	TemplateCard TemplateCard `json:"template_card"`
}

type CardAction struct {
	Type int    `json:"type"`
	URL  string `json:"url"`
}
type TemplateCard struct {
	CardType              string                  `json:"card_type"`
	MainTitle             MainTitle               `json:"main_title"`
	HorizontalContentList []HorizontalContentList `json:"horizontal_content_list"`
	SubTitleText          string                  `json:"sub_title_text"`
	CardAction            CardAction              `json:"card_action"`
}

type RespSendCardBtn struct {
	Errcode      int    `json:"errcode"`
	Errmsg       string `json:"errmsg"`
	Msgid        string `json:"msgid"`
	ResponseCode string `json:"response_code"`
}

type RespSendCard struct {
	Errcode      int    `json:"errcode"`
	Errmsg       string `json:"errmsg"`
	Msgid        string `json:"msgid"`
	ResponseCode string `json:"response_code"`
}

// ReqUpdateTaskCard 更新任务卡片状态的请求
type ReqUpdateTaskCard struct {
	Atall        int    `json:"atall"`
	Agentid      int    `json:"agentid"`
	ResponseCode string `json:"response_code"`
	Button       Button `json:"button"`
}
type Button struct {
	ReplaceName string `json:"replace_name"`
}

// RespUpdateTaskCard 更新任务卡片状态的响应
type RespUpdateTaskCard struct {
	Errcode         int           `json:"errcode"`
	Errmsg          string        `json:"errmsg"`
	InvalidUserids  []interface{} `json:"invalid_userids"`
	InvalidPartyids []interface{} `json:"invalid_partyids"`
	InvalidTagids   []interface{} `json:"invalid_tagids"`
}

type ReqGetUseridByEmail struct {
	Email     string `json:"email"`
	EmailType int    `json:"email_type"`
}

type RespGetUseridByEmail struct {
	Errcode int    `json:"errcode"`
	Errmsg  string `json:"errmsg"`
	Userid  string `json:"userid"`
}

type TextNotionRequest struct {
	Touser  string `json:"touser"`
	Msgtype string `json:"msgtype"`
	Agentid int    `json:"agentid"`
	Text    Text   `json:"text"`
}
type Text struct {
	Content string `json:"content"`
}

func newDefaultRestyClient() *resty.Client {
	client := resty.New()
	//client.SetDebug(os.Getenv("DEBUG") == "true")
	client.SetLogger(os.Stdout)
	client.SetHostURL("https://qyapi.weixin.qq.com")
	return client
}

func (rp *Data) SimplePost(url string, body interface{}, respObj interface{}) error {
	resp, err := newDefaultRestyClient().R().
		SetHeader("Content-Type", "application/json").
		SetBody(body).
		SetResult(&respObj).
		Post(url)

	if err != nil {
		rp.Log.Errorf("qiyweixin api err %v, response %v", os.Stdout, resp.Body())
		return err
	}
	return nil
}

// 发送文本通知消息状态  SendText
func (rp *Data) SendText(ctx context.Context, v TextNotionRequest) (RespSendCard, error) {
	apiPath := "/cgi-bin/message/send"
	token, _ := rp.GetAccessToken(ctx)
	uri := fmt.Sprintf("%s?access_token=%s", apiPath, token)

	var result RespSendCard
	err := rp.SimplePost(uri, v, &result)
	if err != nil {
		rp.Log.Warnf("send  qyweixin message err, %v", err)
		return RespSendCard{}, err
	}
	rp.Log.Infof("send  qyweixin message response, %v", result)
	return result, nil
}

func (rp *Data) GetUserForEmail(ctx context.Context, email string) (*userBiz.User, error) {
	var user User
	var err error
	if strings.Contains(email, "@") {
		err := rp.Db.WithContext(ctx).Where("email = ?", email).First(&user).Error
		if err != nil {
			return nil, err
		}
	} else {
		email = strings.ReplaceAll(email, "%", "\\%")
		likeStr := email + "%"
		err := rp.Db.WithContext(ctx).Where("email like ?", likeStr).First(&user).Error
		if err != nil {
			return nil, err
		}
	}

	resp := &userBiz.User{}
	if err = copier.Copy(resp, user); err != nil {
		return nil, err
	}
	resp.Id = user.ID
	resp.CreatedAt = user.CreatedAt.Format("2006-01-02 15:04:05")
	resp.UpdatedAt = user.CreatedAt.Format("2006-01-02 15:04:05")
	return resp, nil
}

func (rp *Data) GetUserInfo(ctx context.Context, founderEmail string) (string, string, error) {
	reply, err := rp.GetUserForEmail(ctx, founderEmail)

	if err != nil {
		rp.Log.Errorf("request user list error: %s", err.Error())
		return "", "", err
	}
	name := reply.Name
	wechatId := reply.WeChatWorkId
	if len(wechatId) == 0 {
		wechatId, _ = rp.GetUseridByEmail(ctx, founderEmail)
	}
	return name, wechatId, nil
}

// 根据企业邮箱获取企业微信ID
func (rp *Data) GetUseridByEmail(ctx context.Context, email string) (string, error) {
	apiPath := "/cgi-bin/user/get_userid_by_email"
	token, _ := rp.GetAccessToken(ctx)
	uri := fmt.Sprintf("%s?access_token=%s", apiPath, token)

	v := ReqGetUseridByEmail{
		Email:     email,
		EmailType: 2,
	}
	var result RespGetUseridByEmail
	err := rp.SimplePost(uri, v, &result)
	if err != nil {
		rp.Log.Errorf("qyweixin get_userid_by_email response err, %v", err)
		return "", err
	}
	return result.Userid, nil
}
