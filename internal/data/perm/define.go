package data

import (
	"time"
)

type UserAssetPermission struct {
	Id             int       `gorm:"column:id;type:int;primary_key;AUTO_INCREMENT;comment:'ID'"`
	CreatedAt      time.Time `gorm:"column:created_at;type:TIMESTAMP;default:CURRENT_TIMESTAMP;comment:'创建时间'"`
	UpdatedAt      time.Time `gorm:"column:updated_at;type:TIMESTAMP;default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;comment:'更新时间'"`
	OrganizationId string    `gorm:"type:varchar(1024) comment '组织'"`
	Name           string    `gorm:"type:varchar(1024) comment '用户名'"`
	Email          string    `gorm:"type:varchar(1024) comment '邮箱'"`
	Ip             string    `gorm:"type:varchar(1024) comment 'IP'"`
	Hostname       string    `gorm:"type:varchar(1024) comment 'hostname'"`
	Uid            int64     `gorm:"default:0;type:int comment 'uid'"`
	Role           string    `gorm:"type:varchar(16) comment '角色'"`
	ExpireTime     time.Time `gorm:"comment '过期时间'"`
}

type AssetAuthorizationLog struct {
	Id             int       `gorm:"column:id;type:int;primary_key;AUTO_INCREMENT;comment:'ID'"`
	CreatedAt      time.Time `gorm:"column:created_at;type:TIMESTAMP;default:CURRENT_TIMESTAMP;comment:'创建时间'"`
	UpdatedAt      time.Time `gorm:"column:updated_at;type:TIMESTAMP;default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;comment:'更新时间'"`
	OrganizationId string    `gorm:"type:varchar(1024) comment '组织'"`
	Name           string    `gorm:"type:varchar(1024) comment '用户名'"`
	Email          string    `gorm:"type:varchar(1024) comment '邮箱'"`
	Uid            int64     `gorm:"default:0;type:int comment 'uid'"`
	Ip             string    `gorm:"type:varchar(1024) comment 'IP'"`
	Role           string    `gorm:"type:varchar(16) comment '角色'"`
	Creator        string    `gorm:"type:varchar(1024) comment '操作人'"`
	ExpireTime     time.Time `gorm:"comment '过期时间'"`
	TaskId         int64     `gorm:"default:0;type:int comment '任务id'"`
	TaskLog        string    `gorm:"type:text comment '任务日志'"`
}

type AnsibleTask struct {
	Id           int       `gorm:"column:id;type:int;primary_key;AUTO_INCREMENT;comment:'ID'"`
	CreatedAt    time.Time `gorm:"column:created_at;type:TIMESTAMP;default:CURRENT_TIMESTAMP;comment:'创建时间'"`
	UpdatedAt    time.Time `gorm:"column:updated_at;type:TIMESTAMP;default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;comment:'更新时间'"`
	TaskType     string    `gorm:"type:varchar(50) comment '任务类型'"`
	TemplateName string    `gorm:"type:varchar(100) comment '模板名称'"`
	Status       int64     `gorm:"default:0;type:int comment '过期状态,1-成功,0-失败'"`
	Target       string    `gorm:"type:varchar(100) comment '目标机器'"`
	ExtraArgs    string    `gorm:"type:text comment '执行参数'"`
	Log          string    `gorm:"type:text comment '日志内容'"`
	ExecutedAt   time.Time `gorm:"comment '执行时间'"`
	FinishedAt   time.Time `gorm:"comment '完成时间'"`
}
