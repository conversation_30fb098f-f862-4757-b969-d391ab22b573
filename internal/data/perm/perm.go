package data

import (
	"context"
	"github.com/go-kratos/kratos/v2/log"
	permV1 "gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/api/perm/v1"
	"gorm.io/gorm"

	permBiz "gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/internal/biz/perm"
)

type PermData struct {
	Db  *gorm.DB
	Log *log.Helper
}

type PermRepo struct {
	Data *PermData
	Log  *log.Helper
}

func (r *PermRepo) CreateUserAssetPermission(ctx context.Context, permData *permBiz.UserAssetPermission) (string, error) {
	var count int64
	//确认权限是否存在
	if err := r.Data.Db.Model(&UserAssetPermission{}).Where("uid = ? and organization_id = ? and ip = ?", permData.Uid, permData.OrganizationId, permData.Ip).Count(&count).Error; err != nil {
		r.Log.WithContext(ctx).Errorf("get perm data, err: %v", err.Error())
		return err.Error(), err
	}
	if count == 0 {
		if err := r.Data.Db.Model(&UserAssetPermission{}).Create(&UserAssetPermission{
			Uid:            permData.Uid,
			OrganizationId: permData.OrganizationId,
			Name:           permData.Name,
			Email:          permData.Email,
			Ip:             permData.Ip,
			Role:           permData.Role,
			Hostname:       permData.Hostname,
		}).Error; err != nil {
			r.Log.WithContext(ctx).Errorf("create perm data, err: %v", err.Error())
			return err.Error(), err
		}
	}
	return "success", nil
}

func (r *PermRepo) CreateAssetAuthorizationLog(ctx context.Context, logData *permBiz.AssetAuthorizationLog) (string, error) {
	if err := r.Data.Db.Model(&AssetAuthorizationLog{}).Create(&AssetAuthorizationLog{
		Uid:            logData.Uid,
		OrganizationId: logData.OrganizationId,
		Name:           logData.Name,
		Email:          logData.Email,
		Ip:             logData.Ip,
		Role:           logData.Role,
		Creator:        logData.Creator,
		TaskId:         logData.TaskId,
		TaskLog:        logData.TaskLog,
	}).Error; err != nil {
		r.Log.WithContext(ctx).Errorf("create asset authorization log, err: %v", err.Error())
		return err.Error(), err
	}
	return "success", nil
}

func (r *PermRepo) UpdateAssetAuthorizationLog(ctx context.Context, logData *permBiz.AssetAuthorizationLog) error {
	if err := r.Data.Db.Debug().Model(&AssetAuthorizationLog{}).Where("task_id = ?", logData.TaskId).Updates(&logData).Error; err != nil {
		r.Log.WithContext(ctx).Errorf("update ansible task log, err: %v", err.Error())
		return err
	}
	return nil
}

func (r *PermRepo) CreateAnsibleTask(ctx context.Context, taskData *permBiz.AnsibleTask) (*permBiz.AnsibleTask, error) {
	data := &AnsibleTask{
		TaskType:     taskData.TaskType,
		TemplateName: taskData.TemplateName,
		Status:       taskData.Status,
		Target:       taskData.Target,
		ExtraArgs:    taskData.ExtraArgs,
		ExecutedAt:   taskData.ExecutedAt,
		FinishedAt:   taskData.FinishedAt,
	}
	if err := r.Data.Db.Model(&AnsibleTask{}).Create(data).Error; err != nil {
		r.Log.WithContext(ctx).Errorf("create asset ansible task log, err: %v", err.Error())
		return nil, err
	}
	taskData.Id = uint(data.Id)
	return taskData, nil
}

func (r *PermRepo) GetAnsibleTaskByID(ctx context.Context, taskId int64) (*permBiz.AnsibleTask, error) {
	var res *permBiz.AnsibleTask
	if err := r.Data.Db.Model(&AnsibleTask{}).Where("id = ?", taskId).First(&res).Error; err != nil {
		r.Log.WithContext(ctx).Errorf("get ansible task log, err: %v", err.Error())
		return nil, err
	}
	return res, nil
}

func (r *PermRepo) UpdateAnsibleTask(ctx context.Context, taskData *permBiz.AnsibleTask) error {
	if err := r.Data.Db.Model(&AnsibleTask{}).Where("id = ?", taskData.Id).Updates(&taskData).Error; err != nil {
		r.Log.WithContext(ctx).Errorf("update ansible task log, err: %v", err.Error())
		return err
	}
	return nil
}

func (r *PermRepo) GetUserAssetPermissionList(ctx context.Context, req *permV1.GetUserAssetPermissionListRequest) ([]*permBiz.UserAssetPermissionList, int64, error) {
	var userOut []*permBiz.UserAssetPermissionList
	var userData []*permBiz.UserAssetPermissionData
	var count int64

	db := r.Data.Db.WithContext(ctx).Table("t_user_term_configs")

	if req.SearchKey != "" {
		db = db.Where("email like ? or user like ?", "%"+req.SearchKey+"%", "%"+req.SearchKey+"%")
	}

	if req.Ip != "" {
		db = db.Joins("JOIN t_user_asset_permissions ON t_user_term_configs.uid = t_user_asset_permissions.uid")
		db = db.Where("t_user_asset_permissions.ip = ?", req.Ip)
		db = db.Group("t_user_term_configs.uid")
	}

	result := db.Count(&count)
	if result.Error != nil {
		return userOut, 0, result.Error
	}

	if req.PageSize != 0 && req.PageNo != 0 {
		result = db.Offset(int(req.PageNo-1) * int(req.PageSize)).Limit(int(req.PageSize)).Find(&userData)
	} else {
		result = db.Find(&userData)
	}

	if result.Error != nil {
		return userOut, 0, result.Error
	}

	for _, userInfo := range userData {
		var permissionListData []UserAssetPermission
		var permissionListOut []*permBiz.PermissionList
		query := r.Data.Db.Model(&UserAssetPermission{}).Where("uid = ?", userInfo.Uid).Order("created_at")

		if req.Ip != "" {
			query = query.Where("ip = ?", req.Ip)
		}
		query.Find(&permissionListData)
		for _, permission := range permissionListData {
			data := &permBiz.PermissionList{
				Id:             uint(permission.Id),
				CreatedAt:      permission.CreatedAt.Format("2006-01-02 15:04:05"),
				UpdatedAt:      permission.UpdatedAt.Format("2006-01-02 15:04:05"),
				OrganizationId: permission.OrganizationId,
				Ip:             permission.Ip,
				Role:           permission.Role,
				Hostname:       permission.Hostname,
				Uid:            permission.Uid,
			}
			if !permission.ExpireTime.IsZero() {
				data.ExpireTime = permission.ExpireTime.Format("2006-01-02 15:04:05")
			}
			permissionListOut = append(permissionListOut, data)
		}
		userOut = append(userOut, &permBiz.UserAssetPermissionList{
			Uid:                 userInfo.Uid,
			Email:               userInfo.Email,
			User:                userInfo.User,
			OrganizationId:      userInfo.OrganizationId,
			CreatedAt:           userInfo.CreatedAt.Format("2006-01-02 15:04:05"),
			PermissionAssetList: permissionListOut,
		})
	}

	return userOut, count, nil
}
