package data

import "time"

type AiModel struct {
	Id        int       `gorm:"column:id;type:int;primary_key;AUTO_INCREMENT;comment:'模型ID'"`
	CreatedAt time.Time `gorm:"column:created_at;type:TIMESTAMP;default:CURRENT_TIMESTAMP;comment:'创建时间'"`
	UpdatedAt time.Time `gorm:"column:updated_at;type:TIMESTAMP;default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;comment:'更新时间'"`
	Name      string    `gorm:"type:varchar(128);comment:'模型名称';column:name"`
	Object    string    `gorm:"type:varchar(128);comment:'模型对象';column:object"`
	Provider  string    `gorm:"type:varchar(128);comment:'模型提供者';column:provider"`
	Status    int       `gorm:"comment:'模型状态 2:未启用 1:启用';column:status"`
}

type AiChatTitle struct {
	Id             int       `gorm:"column:id;type:int;primary_key;AUTO_INCREMENT;comment:'标题ID'"`
	CreatedAt      time.Time `gorm:"column:created_at;type:TIMESTAMP;default:CURRENT_TIMESTAMP;comment:'创建时间'"`
	UpdatedAt      time.Time `gorm:"column:updated_at;type:TIMESTAMP;default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;comment:'更新时间'"`
	Email          string    `gorm:"type:varchar(128);comment:'用户邮箱';column:email"`
	Title          string    `gorm:"type:varchar(128);comment:'标题';column:title"`
	Model          string    `gorm:"type:varchar(128);comment:'chat模型';column:model"`
	ConversateType string    `gorm:"type:varchar(128);comment:'会话类型';column:conversate_type"`
	ConversationId string    `gorm:"type:varchar(128);comment:'会话ID';column:conversation_id"`
}

type AiChatHistory struct {
	Id             int       `gorm:"column:id;type:int;primary_key;AUTO_INCREMENT;comment:'记录ID'"`
	SendAt         time.Time `gorm:"column:send_at;type:TIMESTAMP;default:CURRENT_TIMESTAMP;comment:'消息发送时间'"`
	CreatedAt      time.Time `gorm:"column:created_at;type:TIMESTAMP;default:CURRENT_TIMESTAMP;comment:'消息创建时间'"`
	Email          string    `gorm:"type:varchar(128);comment:'用户邮箱';column:email"`
	Model          string    `gorm:"type:varchar(128);comment:'chat模型';column:model"`
	ConversationId string    `gorm:"type:varchar(128);comment:'会话ID';column:conversation_id"`
	ConversateType string    `gorm:"type:varchar(128);comment:'会话类型';column:conversate_type"`
	Content        string    `gorm:"type:longtext;comment:'内容';column:content"`
}
