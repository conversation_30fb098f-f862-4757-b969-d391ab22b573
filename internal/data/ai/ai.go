package data

import (
	"context"
	"time"

	"github.com/bytedance/sonic"
	"github.com/go-kratos/kratos/v2/log"
	aiV1 "gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/api/ai/v1"
	aiBiz "gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/internal/biz/ai"
	pkg "gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/pkg/util/common"
	"gorm.io/gorm"
)

type AiData struct {
	Db  *gorm.DB
	Log *log.Helper
}

type AiRepo struct {
	Data *AiData
	Log  *log.Helper
}

func (r *AiRepo) GetModelList(ctx context.Context) ([]aiBiz.AiModel, error) {
	finds := []aiBiz.AiModel{}
	if err := r.Data.Db.Table("t_ai_models").Where("status = 1").Find(&finds).Error; err != nil {
		r.Log.WithContext(ctx).Errorf("find AiModel data, err: %v", err.Error())
		return nil, err
	}
	return finds, nil
}

func (r *AiRepo) GetUserConversationList(ctx context.Context) ([]aiBiz.AiChatTitle, error) {
	userInfo, err := pkg.GetUserInfo(ctx)
	if err != nil {
		return nil, err
	}
	finds := []aiBiz.AiChatTitle{}
	if err := r.Data.Db.Table("t_ai_chat_titles").Where("email = ?", userInfo.Email).Order("updated_at desc").Find(&finds).Error; err != nil {
		r.Log.WithContext(ctx).Errorf("find AiChatTitle data, err: %v", err.Error())
		return nil, err
	}
	return finds, nil
}

func (r *AiRepo) GetConversationDetail(ctx context.Context, req *aiV1.GetConversationDetailRequest) ([]aiBiz.AiChatHistory, int64, error) {
	finds := []aiBiz.AiChatHistory{}
	userInfo, err := pkg.GetUserInfo(ctx)
	if err != nil {
		return nil, 0, err
	}
	condition := &aiBiz.AiChatHistory{
		Email:          userInfo.Email,
		ConversationId: req.ConversationId,
	}

	db := r.Data.Db.Debug().Table("t_ai_chat_histories").Where(condition)

	var count int64
	if err := db.Count(&count).Error; err != nil {
		r.Log.WithContext(ctx).Errorf("find AiChatHistory count, err: %v", err.Error())
		return nil, 0, err
	}
	if err := db.Offset(int(req.Offset)).Limit(int(req.Limit)).Find(&finds).Error; err != nil {
		r.Log.WithContext(ctx).Errorf("find AiChatHistory data, err: %v", err.Error())
		return nil, 0, err
	}

	return finds, count, nil
}

func (r *AiRepo) UpdateConversationTitle(conversationId, email, model, conversateType string, messaget []aiBiz.ChatMessage) error {
	var find aiBiz.AiChatTitle
	if err := r.Data.Db.Table("t_ai_chat_titles").Where("email = ? and conversation_id=?", email, conversationId).First(&find).Error; err != nil && err != gorm.ErrRecordNotFound {
		r.Log.Errorf("UpdateConversationTitle find AiChatTitle data, err: %v", err.Error())
		return err
	}
	if find.Id == 0 {
		var title string
		if len(messaget) > 0 {
			if len(messaget[0].Content) > 30 {
				title = messaget[0].Content[:30]
			} else {
				title = messaget[0].Content
			}
		}
		if err := r.Data.Db.Table("t_ai_chat_titles").Create(&aiBiz.AiChatTitle{
			Email:          email,
			Model:          model,
			Title:          title,
			ConversateType: conversateType,
			ConversationId: conversationId,
			UpdatedAt:      time.Now(),
			CreatedAt:      time.Now(),
		}).Error; err != nil {
			r.Log.Errorf("UpdateConversationTitle create AiChatTitle data, err: %v", err.Error())
			return err
		}
	} else {
		if err := r.Data.Db.Table("t_ai_chat_titles").Where("email = ? and conversation_id=?", email, conversationId).Updates(map[string]interface{}{
			"model":      model,
			"updated_at": time.Now(),
		}).Error; err != nil {
			r.Log.Errorf("UpdateConversationTitle update AiChatTitle data, err: %v", err.Error())
			return err
		}
	}
	return nil
}

func (r *AiRepo) GetConversationHistoryDetail(email, conversationId string) ([]aiBiz.ChatMessage, error) {
	finds := []aiBiz.AiChatHistory{}

	if err := r.Data.Db.Table("t_ai_chat_histories").Where("email = ? and conversation_id = ?", email, conversationId).Order("created_at").Limit(20).Find(&finds).Error; err != nil && err != gorm.ErrRecordNotFound {
		r.Log.Errorf("GetConversationHistoryDetail find AiChatHistory data, err: %v", err.Error())
		return nil, err
	}
	var outs []aiBiz.ChatMessage
	for _, v := range finds {
		var out aiBiz.ChatMessage
		sonic.UnmarshalString(v.Content, &out)
		outs = append(outs, out)
	}
	return outs, nil
}

func (r *AiRepo) CreateConversationHistory(history aiBiz.AiChatHistory) error {
	if err := r.Data.Db.Table("t_ai_chat_histories").Create(&history).Error; err != nil {
		r.Log.Errorf("CreateConversationHistory create AiChatHistory data, err: %v", err.Error())
		return err
	}
	return nil
}
