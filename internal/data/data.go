package data

import (
	permBiz "gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/internal/biz/perm"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-redis/redis/extra/redisotel"
	"github.com/go-redis/redis/v8"
	"github.com/google/wire"
	aiBiz "gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/internal/biz/ai"
	assetBiz "gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/internal/biz/asset"
	userBiz "gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/internal/biz/user"
	"gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/internal/client"
	"gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/internal/conf"
	aiData "gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/internal/data/ai"
	assetData "gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/internal/data/asset"
	permData "gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/internal/data/perm"
	userData "gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/internal/data/user"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/schema"
)

// ProviderSet is data providers.
var ProviderSet = wire.NewSet(NewData, NewDB, NewRedis, NewUserRepo, NewAiRepo, NewAssetRepo, NewPermRepo)

// Data .
type Data struct {
	// TODO wrapped database client
	db     *gorm.DB
	rdb    *redis.Client
	sso    *conf.Sso
	client *client.Client
	conf   *conf.OtherInfo
}

// NewData .
func NewData(db *gorm.DB, rdb *redis.Client, c *conf.Bootstrap, logger log.Logger) (*Data, func(), error) {
	cleanup := func() {
		log.NewHelper(logger).Info("closing the data resources")
	}
	d := &Data{
		db:  db,
		rdb: rdb,
		conf: c.OtherInfo,
	}
	return d, cleanup, nil
}

func NewDB(c *conf.Data) *gorm.DB {
	db, err := gorm.Open(mysql.Open(c.Database.Source), &gorm.Config{
		DisableForeignKeyConstraintWhenMigrating: true,
	})
	if err != nil {
		log.Fatalf("failed opening connection to mysql: %v", err)
	}
	InitDB(db)
	return db
}

func NewRedis(c *conf.Data) *redis.Client {
	t1, _ := time.ParseDuration(c.Redis.WriteTimeout)
	t2, _ := time.ParseDuration(c.Redis.ReadTimeout)
	rds := redis.NewClient(&redis.Options{
		Addr:         c.Redis.Addr,
		Username:     c.Redis.User,
		Password:     c.Redis.Password,
		DB:           1,
		WriteTimeout: t1,
		ReadTimeout:  t2,
	})
	rds.AddHook(redisotel.TracingHook{})
	return rds
}

func InitDB(db *gorm.DB) {
	db.NamingStrategy = schema.NamingStrategy{
		SingularTable: false,
		TablePrefix:   "t_", // 表名前缀，`User` 的表名应该是 `t_users`
	}
	if err := db.Set("gorm:table_options", "ENGINE=InnoDB DEFAULT CHARSET=utf8").AutoMigrate(&userData.User{}, &userData.UserKey{}); err != nil {
		panic(err)
	}
	// if err := db.Set("gorm:table_options", "ENGINE=InnoDB DEFAULT CHARSET=utf8").AutoMigrate(&aiData.AiModel{}, &aiData.AiChatTitle{}, &aiData.AiChatHistory{}); err != nil {
	// 	panic(err)
	// }
	if err := db.Set("gorm:table_options", "ENGINE=InnoDB DEFAULT CHARSET=utf8").AutoMigrate(&assetData.Asset{}, &assetData.UserAssetFavorite{}, &assetData.UserAssetAlias{}); err != nil {
		panic(err)
	}

	if err := db.Set("gorm:table_options", "ENGINE=InnoDB DEFAULT CHARSET=utf8").AutoMigrate(&permData.UserAssetPermission{}); err != nil {
		panic(err)
	}
	if err := db.Set("gorm:table_options", "ENGINE=InnoDB DEFAULT CHARSET=utf8").AutoMigrate(&permData.AssetAuthorizationLog{}); err != nil {
		panic(err)
	}
	if err := db.Set("gorm:table_options", "ENGINE=InnoDB DEFAULT CHARSET=utf8").AutoMigrate(&permData.AnsibleTask{}); err != nil {
		panic(err)
	}
	if err := db.Set("gorm:table_options", "ENGINE=InnoDB DEFAULT CHARSET=utf8").AutoMigrate(&assetData.AssetProxyGateway{}); err != nil {
		panic(err)
	}
	if err := db.Set("gorm:table_options", "ENGINE=InnoDB DEFAULT CHARSET=utf8").AutoMigrate(&assetData.AssetAdminUser{}); err != nil {
		panic(err)
	}
	if err := db.Set("gorm:table_options", "ENGINE=InnoDB DEFAULT CHARSET=utf8").AutoMigrate(&userData.UserTermConfig{}); err != nil {
		panic(err)
	}
	if err := db.Set("gorm:table_options", "ENGINE=InnoDB DEFAULT CHARSET=utf8").AutoMigrate(&assetData.TerminalCommand{}); err != nil {
		panic(err)
	}
	if err := db.Set("gorm:table_options", "ENGINE=InnoDB DEFAULT CHARSET=utf8").AutoMigrate(&assetData.TerminalRecs{}); err != nil {
		panic(err)
	}
}

func NewUserRepo(data *Data, logger log.Logger, sso *conf.Sso, client *client.Client, conf *conf.OtherInfo, emailConfig *conf.EmailConfig, wecomConfig *conf.WecomConfig) userBiz.UserRepo {
	return &userData.UserRepo{
		Data: &userData.Data{
			Db:          data.db,
			Rdb:         data.rdb,
			Sso:         sso,
			Client:      client,
			Log:         log.NewHelper(logger),
			Conf:        conf,
			EmailConfig: emailConfig,
			WecomConfig: wecomConfig,
		},
		Log: log.NewHelper(logger),
	}
}

func NewAiRepo(data *Data, logger log.Logger) aiBiz.AiRepo {
	return &aiData.AiRepo{
		Data: &aiData.AiData{
			Db:  data.db,
			Log: log.NewHelper(log.With(logger, "module", "data/ai")),
		},
		Log: log.NewHelper(logger),
	}
}

func NewAssetRepo(data *Data, logger log.Logger, conf *conf.OtherInfo) assetBiz.AssetRepo {
	return &assetData.AssetRepo{
		Data: &assetData.AssetData{
			Db: data.db,
		},
		Log:  log.NewHelper(log.With(logger, "module", "data/asset")),
		Conf: conf,
	}
}

func NewPermRepo(data *Data, logger log.Logger) permBiz.PermRepo {
	return &permData.PermRepo{
		Data: &permData.PermData{
			Db: data.db,
		},
		Log: log.NewHelper(log.With(logger, "module", "data/perm")),
	}
}
