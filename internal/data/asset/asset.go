package data

import (
	"context"
	"github.com/elliotchance/pie/pie"
	"github.com/gofrs/uuid/v5"
	assetV1 "gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/api/asset/v1"
	assetBiz "gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/internal/biz/asset"
	biz "gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/internal/biz/asset"
	userData "gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/internal/data/user"
	cryptoUtil "gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/pkg/crypto_utils"
	pkg "gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/pkg/util/common"
	"gorm.io/gorm"
	"regexp"
	"time"
)

func (r *AssetRepo) GetAssetList(ctx context.Context, req *assetV1.GetAssetListRequest) ([]assetBiz.Asset, int64, error) {
	condition := &biz.Asset{
		OrganizationId: req.OrganizationId,
		Idc:            req.Idc,
		ProxyGatewayId: int(req.ProxyGatewayId),
	}
	db := r.Data.Db.Table("t_assets").Where(condition)

	if req.SearchText != "" {
		key := "%" + req.SearchText + "%"
		db = db.Where("name like ? or asset_ip like ?", key, key)
	}

	var count int64
	if err := db.Count(&count).Error; err != nil {
		r.Log.WithContext(ctx).Errorf("count GetAssetList, err: %v", err.Error())
		return nil, count, err
	}
	finds := []assetBiz.Asset{}

	db.Offset(int(req.PageNo-1) * int(req.PageSize)).Limit(int(req.PageSize)).Find(&finds)
	return finds, count, nil
}

func (r *AssetRepo) CreateAsset(ctx context.Context, req *assetV1.CreateAssetRequest) error {
	uuid, err := uuid.NewV4()
	if err != nil {
		r.Log.Errorf("CreateAsset uuid err: %v", err.Error())
	}

	db := r.Data.Db.Table("t_assets")
	db = db.Create(&biz.Asset{
		Uuid:           uuid.String(),
		Name:           req.Name,
		Idc:            req.Idc,
		AssetIp:        req.AssetIp,
		Active:         req.Active,
		Platform:       req.Platform,
		ProxyGatewayId: int(req.ProxyGatewayId),
		Protocol:       req.Protocol,
		AssetSource:    req.AssetSource,
		Os:             req.Os,
		OrganizationId: req.OrganizationId,
		Comment:        req.Comment,
	})
	if db.Error != nil {
		r.Log.WithContext(ctx).Errorf("CreateAsset, err: %v", db.Error.Error())
		return db.Error
	}
	return nil
}

func (r *AssetRepo) DeleteAsset(ctx context.Context, req *assetV1.DeleteAssetRequest) error {
	if err := r.Data.Db.Table("t_assets").Where("uuid = ?", req.Uuid).Delete(&biz.Asset{}).Error; err != nil && err != gorm.ErrRecordNotFound {
		r.Log.WithContext(ctx).Errorf("DeleteAsset, err: %v", err.Error())
	}
	return nil
}

func (r *AssetRepo) UpdateAsset(ctx context.Context, req *assetV1.UpdateAseetRequest) error {
	if err := r.Data.Db.Table("t_assets").Where("uuid = ?", req.Uuid).Updates(map[string]interface{}{
		"name":             req.Name,
		"idc":              req.Idc,
		"active":           req.Active,
		"proxy_gateway_id": req.ProxyGatewayId,
		"organization_id":  req.OrganizationId,
		"comment":          req.Comment,
		"platform":         req.Platform,
		"protocol":         req.Protocol,
		"os":               req.Os,
	}).Error; err != nil && err != gorm.ErrRecordNotFound {
		r.Log.WithContext(ctx).Errorf("DeleteAsset, err: %v", err.Error())
	}
	return nil
}

func (r *AssetRepo) UserAssetAlias(ctx context.Context, req *assetV1.UserAssetAliasRequest) error {

	userInfo, err := pkg.GetUserInfo(ctx)
	if err != nil {
		r.Log.WithContext(ctx).Errorf("UserAssetFavorite, err: %v", err.Error())
		return err
	}

	ip := regexp.MustCompile(`(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})$`).FindAllString(req.Key, -1)[0]

	var find UserAssetAlias
	if err := r.Data.Db.Table("t_user_asset_aliases").Where("user_id = ? and asset_ip = ?", userInfo.Uid, ip).First(&find).Error; err != nil && err != gorm.ErrRecordNotFound {
		r.Log.WithContext(ctx).Errorf("UserAssetFavorite, err: %v", err.Error())
		return err
	}
	if find.Id == 0 {
		if err := r.Data.Db.Table("t_user_asset_aliases").Create(&UserAssetAlias{
			UserId:  int64(userInfo.Uid),
			AssetIp: ip,
			Alias:   req.Alias,
		}).Error; err != nil {
			r.Log.WithContext(ctx).Errorf("UserAssetFavorite, err: %v", err.Error())
			return err
		}
	} else {
		if err := r.Data.Db.Table("t_user_asset_aliases").Where("user_id = ? and asset_ip = ?", userInfo.Uid, ip).Update("alias", req.Alias).Error; err != nil {
			r.Log.WithContext(ctx).Errorf("UserAssetFavorite, err: %v", err.Error())
			return err
		}
	}
	return nil
}

func (r *AssetRepo) UserAssetFavorite(ctx context.Context, req *assetV1.UserAssetFavoriteRequest) error {
	userInfo, err := pkg.GetUserInfo(ctx)
	if err != nil {
		r.Log.WithContext(ctx).Errorf("UserAssetFavorite, err: %v", err.Error())
		return err
	}
	db := r.Data.Db.Table("t_user_asset_favorites")
	if req.Action == "favorite" {
		db = db.Create(&UserAssetFavorite{
			UserId:  int64(userInfo.Uid),
			AssetIp: req.AssetIp,
		})
	} else if req.Action == "unfavorite" {
		db = db.Where(" asset_ip = ?", req.AssetIp).Delete(&UserAssetFavorite{})
	}
	if db.Error != nil {
		r.Log.WithContext(ctx).Errorf("UserAssetFavorite, err: %v", db.Error.Error())
		return db.Error
	}
	return nil
}

func (r *AssetRepo) GetTop5Asset(ctx context.Context, uid int64, originationId string) ([]*assetV1.ListAssetRouteReply_Children, error) {

	var finds []biz.AssetConnectCount
	if err := r.Data.Db.Debug().Table("t_terminal_recs").
		Select("ip, COUNT(*) as connect_count").
		Where("uid = ?", uid).
		Group("ip").
		Order("connect_count DESC").
		Limit(5).
		Find(&finds).Error; err != nil {
		r.Log.WithContext(ctx).Errorf("GetTop5Asset, err: %v", err.Error())
		return nil, err
	}
	var assetIPs []string
	for _, f := range finds {
		assetIPs = append(assetIPs, f.Ip)
	}
	var notExpireAsset []string
	if err := r.Data.Db.Debug().Table("t_user_asset_permissions").Select("ip").Where("ip in ?", assetIPs).Order("ip").Find(&notExpireAsset).Error; err != nil {
		r.Log.WithContext(ctx).Errorf("GetTop5Asset assets not  expire, err: %v", err.Error())
		return nil, err
	}
	var findUserAsset []string
	if err := r.Data.Db.Table("t_assets").Select("asset_ip").Where("asset_ip in ? and organization_id= ?  and active='allow' ", notExpireAsset, originationId).Order("asset_ip").Find(&findUserAsset).Error; err != nil {
		r.Log.WithContext(ctx).Errorf("GetTop5Asset assets, err: %v", err.Error())
		return nil, err
	}

	var out []*assetV1.ListAssetRouteReply_Children
	for _, v := range findUserAsset {
		out = append(out, &assetV1.ListAssetRouteReply_Children{
			Key:            "common_" + v,
			Title:          v,
			Ip:             v,
			OrganizationId: originationId,
		})
	}
	return out, nil
}

func (r *AssetRepo) GetFavoriteAsset(ctx context.Context, ssoId int64, originationId string) ([]*assetV1.ListAssetRouteReply_Children, pie.Strings, error) {
	var finds []string
	favoriteAsset := pie.Strings{}

	if err := r.Data.Db.Debug().Table("t_user_asset_favorites").
		Select("asset_ip").
		Where("user_id = ?", ssoId).
		Order("created_at DESC").
		Limit(40).
		Scan(&finds).Error; err != nil {
		r.Log.WithContext(ctx).Errorf("GetFavoriteAsset, err: %v", err.Error())
		return nil, favoriteAsset, err
	}
	var notExpireAsset []string
	if err := r.Data.Db.Debug().Table("t_user_asset_permissions").Select("ip").Where("ip in ? and (expire_time ='0000-00-00 00:00:00' or expire_time > ?)", finds, time.Now()).Order("ip").Find(&notExpireAsset).Error; err != nil {
		r.Log.WithContext(ctx).Errorf("GetTop5Asset assets not  expire, err: %v", err.Error())
		return nil, favoriteAsset, err
	}
	var findUserAsset []string
	if err := r.Data.Db.Table("t_assets").Select("asset_ip").Where("asset_ip in ? and organization_id= ?  and active='allow'  ", notExpireAsset, originationId).Order("asset_ip").Find(&findUserAsset).Error; err != nil {
		r.Log.WithContext(ctx).Errorf("GetFavoriteAsset assets, err: %v", err.Error())
		return nil, favoriteAsset, err
	}
	var out []*assetV1.ListAssetRouteReply_Children
	for _, v := range findUserAsset {
		out = append(out, &assetV1.ListAssetRouteReply_Children{
			Key:            "favorite_" + v,
			Title:          v,
			Favorite:       true,
			Ip:             v,
			OrganizationId: originationId,
		})
		favoriteAsset = append(favoriteAsset, v)
	}
	return out, favoriteAsset, nil
}
func (r *AssetRepo) GetUserTermMenu(ctx context.Context, ssoId int64) (string, error) {
	var termConfig userData.UserTermConfig
	err := r.Data.Db.Where("uid = ?", ssoId).First(&termConfig).Error
	if err != nil {
		return "", err
	}

	return termConfig.Menu, nil
}

func (r *AssetRepo) CheckAssetPermExpireStatus(ctx context.Context, ssoId int64, ip string) (bool, error) {
	var id int
	if err := r.Data.Db.Table("t_user_asset_permissions").Select("id").Where("uid = ? and ip = ? and expire_time !='0000-00-00 00:00:00' and expire_time < ?", ssoId, ip, time.Now()).Scan(&id).Error; err != nil {
		r.Log.WithContext(ctx).Errorf("GetUserIdcAsset user_assets err: %v", err.Error())
		return false, err
	}
	if id != 0 {
		return true, nil
	}
	return false, nil
}

func (r *AssetRepo) GetUserIdcAsset(ctx context.Context, ssoId int64, originationId string) ([]biz.UserAsset, error) {
	var finds []string
	if err := r.Data.Db.Table("t_user_asset_permissions").Select("ip").Where("uid = ? and (expire_time ='0000-00-00 00:00:00' or expire_time > ?)", ssoId, time.Now()).Scan(&finds).Error; err != nil {
		r.Log.WithContext(ctx).Errorf("GetUserIdcAsset user_assets err: %v", err.Error())
		return nil, err
	}

	var findUserAsset []biz.UserAsset
	if err := r.Data.Db.Table("t_assets").Select("idc, GROUP_CONCAT(asset_ip) as ips").Where("asset_ip in ? and organization_id= ?  and active='allow' ", finds, originationId).Group("idc").Find(&findUserAsset).Error; err != nil {
		r.Log.WithContext(ctx).Errorf("GetUserIdcAsset assets, err: %v", err.Error())
		return nil, err
	}
	return findUserAsset, nil
}

func (r *AssetRepo) GetAssetAlias(ctx context.Context, ssoId int64) (map[string]string, error) {
	var finds []UserAssetAlias
	if err := r.Data.Db.Table("t_user_asset_aliases").Where("user_id = ?", ssoId).Find(&finds).Error; err != nil {
		r.Log.WithContext(ctx).Errorf("GetAssetAlias, err: %v", err.Error())
		return nil, err
	}
	out := make(map[string]string)
	for _, v := range finds {
		out[v.AssetIp] = v.Alias
	}
	return out, nil
}

func (r *AssetRepo) ListUserWorkSpace(ctx context.Context) ([]biz.UserSpace, error) {
	userInfo, err := pkg.GetUserInfo(ctx)
	if err != nil {
		r.Log.WithContext(ctx).Errorf("UserAssetFavorite, err: %v", err.Error())
		return nil, err
	}
	var finds []string
	if err := r.Data.Db.Table("t_user_asset_permissions").Distinct("ip").Where("uid= ?", userInfo.Uid).Find(&finds).Error; err != nil {
		r.Log.WithContext(ctx).Errorf("ListUserWorkSpace, err: %v", err.Error())
		return nil, err
	}

	var findOriginations []string
	if err := r.Data.Db.Table("t_assets").Distinct("organization_id").Where("asset_ip in ?", finds).Find(&findOriginations).Error; err != nil {
		r.Log.WithContext(ctx).Errorf("ListUserWorkSpace organization_id, err: %v", err.Error())
		return nil, err
	}
	var out []biz.UserSpace
	for _, v := range findOriginations {
		var lable string
		switch v {
		case "hehe-firm-0001":
			lable = "合合"
		case "hehe-firm-0002":
			lable = "DG"
		default:
			lable = "合合"
		}
		out = append(out, biz.UserSpace{
			Key:   v,
			Label: lable,
		})
	}
	return out, nil
}
func (r *AssetRepo) GetAssetProxyGateway(ctx context.Context, ip, organizationId string) (assetBiz.AssetProxyGateway, error) {
	var err error
	var asset Asset
	var assetProxyGateway assetBiz.AssetProxyGateway
	r.Data.Db.Debug().Model(&Asset{}).Where("asset_ip = ? and organization_id = ?", ip, organizationId).First(&asset)
	if asset.ProxyGatewayId != 0 {
		r.Data.Db.Model(&AssetProxyGateway{}).Where("organization_id = ? and id = ?", organizationId, asset.ProxyGatewayId).First(&assetProxyGateway)
	}
	secretKey := []byte(r.Conf.OptSecretKey)
	assetProxyGateway.PrivateKey, err = cryptoUtil.Decrypt(secretKey, assetProxyGateway.PrivateKey)
	if err != nil {
		r.Log.Errorf("Decrypt assetProxyGateway PrivateKey, err: %v", err.Error())
		return assetProxyGateway, err
	}
	return assetProxyGateway, nil
}

func (r *AssetRepo) GetAssetListByIp(ctx context.Context, req *assetV1.GetAssetListByIpRequest) ([]assetBiz.Asset, int64, error) {
	db := r.Data.Db.Table("t_assets")

	if req.Ip != "" {
		key := "%" + req.Ip + "%"
		db = db.Debug().Where("asset_ip like ?", key)
	}
	if req.OrganizationId != "" {
		db = db.Debug().Where("organization_id", req.OrganizationId)
	}
	var count int64
	if err := db.Count(&count).Error; err != nil {
		r.Log.WithContext(ctx).Errorf("count GetAssetListByIp, err: %v", err.Error())
		return nil, count, err
	}
	var finds []assetBiz.Asset
	pageNo := int64(1)
	pageSize := int64(10)
	if req.GetPageSize() != 0 {
		pageSize = req.GetPageSize()
	}
	if req.GetPageNo() != 0 {
		pageNo = req.GetPageNo()
	}
	db.Offset(int(pageNo-1) * int(pageSize)).Limit(int(pageSize)).Find(&finds)
	return finds, count, nil
}
