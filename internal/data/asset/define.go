package data

import (
	"time"

	"gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/internal/conf"

	"github.com/go-kratos/kratos/v2/log"
	"gorm.io/gorm"
)

type AssetData struct {
	Db *gorm.DB
}

type AssetRepo struct {
	Data *AssetData
	Log  *log.Helper
	Conf *conf.OtherInfo
}
type Asset struct {
	Id             int            `gorm:"column:id;type:int;primary_key;AUTO_INCREMENT;comment:'模型ID'"`
	CreatedAt      time.Time      `gorm:"column:created_at;type:TIMESTAMP;default:CURRENT_TIMESTAMP;comment:'创建时间'"`
	UpdatedAt      time.Time      `gorm:"column:updated_at;type:TIMESTAMP;default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;comment:'更新时间'"`
	DeletedAt      gorm.DeletedAt `gorm:"column:deleted_at;comment:'删除时间'"`
	Name           string         `gorm:"type:varchar(128);comment:'服务器名称';column:name"`
	Idc            string         `gorm:"type:varchar(128);comment:'机房';column:idc"`
	Uuid           string         `gorm:"type:varchar(128);comment:'唯一ID';column:uuid;column:uuid;uniqueIndex:idx_asset_uuid"`
	AssetIp        string         `gorm:"type:varchar(128);comment:'服务器IP';column:asset_ip"`
	Active         string         `gorm:"type:varchar(128);comment:'激活状态';column:active"`
	Platform       string         `gorm:"type:varchar(128);comment:'平台';column:platform"`
	Protocol       string         `gorm:"type:varchar(128);comment:'协议';column:protocol"`
	AssetSource    string         `gorm:"type:varchar(128);comment:'资产来源： cloud, manual';column:asset_source"`
	Os             string         `gorm:"type:varchar(128);comment:'操作系统';column:os"`
	OrganizationId string         `gorm:"type:varchar(128);comment:'组织ID';column:organization_id"`
	Comment        string         `gorm:"type:varchar(128);comment:'备注';column:comment"`
	ProxyGatewayId int            `gorm:"default:0;type:int comment '代理id'"`
}

type UserAssetFavorite struct {
	Id        int       `gorm:"column:id;type:int;primary_key;AUTO_INCREMENT;comment:'ID'"`
	CreatedAt time.Time `gorm:"column:created_at;type:TIMESTAMP;default:CURRENT_TIMESTAMP;comment:'收藏时间'"`
	UserId    int64     `gorm:"type:int;comment:'用户ID,ssoId';column:user_id;uniqueIndex:idx_user_asset"`
	AssetIp   string    `gorm:"type:varchar(128);comment:'服务器IP';column:asset_ip;uniqueIndex:idx_user_asset"`
}

type UserAssetAlias struct {
	Id        int       `gorm:"column:id;type:int;primary_key;AUTO_INCREMENT;comment:'ID'"`
	CreatedAt time.Time `gorm:"column:created_at;type:TIMESTAMP;default:CURRENT_TIMESTAMP;comment:'收藏时间'"`
	UserId    int64     `gorm:"type:int;comment:'用户ID,ssoId';column:user_id;uniqueIndex:idx_user_asset"`
	AssetIp   string    `gorm:"type:varchar(128);comment:'服务器IP';column:asset_ip;uniqueIndex:idx_user_asset"`
	Alias     string    `gorm:"type:varchar(128);comment:'别名';column:alias;uniqueIndex:idx_user_asset"`
}

type AssetProxyGateway struct {
	gorm.Model
	OrganizationId string `gorm:"type:varchar(1024) comment '组织'"`
	Name           string `gorm:"type:varchar(128) comment '代理名称'"`
	Ip             string `gorm:"type:varchar(128) comment '代理ip'"`
	Port           int64  `gorm:"default:16;type:int comment '代理端口'"`
	Username       string `gorm:"type:varchar(128) comment '代理用户'"`
	Password       string `gorm:"type:varchar(128) comment '代理密码'"`
	PrivateKey     string `gorm:"type:text comment '代理密钥'"`
	Comment        string `gorm:"type:varchar(128);comment:'备注'"`
}

type AssetAdminUser struct {
	gorm.Model
	OrganizationId string `gorm:"type:varchar(1024) comment '组织'"`
	Username       string `gorm:"type:varchar(128) comment '用户'"`
	Password       string `gorm:"type:varchar(128) comment '密码'"`
	PrivateKey     string `gorm:"type:text comment '密钥'"`
	Comment        string `gorm:"type:varchar(128);comment:'备注'"`
}

type TerminalRecs struct {
	gorm.Model
	Ip         string `gorm:"type:varchar(32) comment 'IP';not null"`
	User       string `gorm:"type:varchar(32) comment '用户名';not null"`
	Uid        int64  `gorm:"default:0;type:int comment 'uid'"`
	Date       string `gorm:"type:varchar(32) comment '日期';not null"`
	Url        string `gorm:"type:varchar(256) comment 'URL地址';not null"`
	RemoteAddr string `gorm:"type:varchar(32) comment '来源IP'"`
	Platform   string `gorm:"type:varchar(128) comment '来源平台'"`
}

type TerminalCommand struct {
	gorm.Model
	Ip        string `gorm:"type:varchar(128) comment 'IP';not null"`
	User      string `gorm:"type:varchar(128) comment '系统用户';not null"`
	Email     string `gorm:"type:varchar(128) comment '连接用户邮箱';not null"`
	Uid       string `gorm:"type:varchar(128) comment '连接用户邮箱';not null"`
	Input     string `gorm:"type:text comment '输入';not null"`
	Output    string `gorm:"type:text comment '输出';not null"`
	Timestamp int64  `gorm:"default:16;type:int comment '时间戳'"`
	SessionID string `gorm:"type:varchar(128) comment '连接sessionId';not null"`
}
