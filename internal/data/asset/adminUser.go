package data

import (
	"context"

	assetV1 "gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/api/asset/v1"
	assetBiz "gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/internal/biz/asset"
	biz "gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/internal/biz/asset"
	cryptoUtil "gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/pkg/crypto_utils"
	"gorm.io/gorm"
)

func (r *AssetRepo) GetAssetAdminUserByOrganizationId(ctx context.Context, organizationId string) (assetBiz.AssetAdminUser, error) {
	var err error
	var assetAdminUser assetBiz.AssetAdminUser
	r.Data.Db.Debug().Model(&AssetAdminUser{}).Where("organization_id = ?", organizationId).First(&assetAdminUser)

	secretKey := []byte(r.Conf.OptSecretKey)
	assetAdminUser.PrivateKey, err = cryptoUtil.Decrypt(secretKey, assetAdminUser.PrivateKey)
	if err != nil {
		r.Log.Errorf("Decrypt assetProxyGateway PrivateKey, err: %v", err.Error())
		return assetAdminUser, err
	}
	return assetAdminUser, nil
}

// 管理员用户
func (r *AssetRepo) GetAssetAdminUserList(ctx context.Context, req *assetV1.GetAssetAdminUserListRequest) ([]assetBiz.AssetAdminUser, int64, error) {
	db := r.Data.Db.Table("t_asset_admin_users")

	if req.SearchText != "" {
		key := "%" + req.SearchText + "%"
		db = db.Where("username like ? ", key)
	}
	var count int64
	if err := db.Count(&count).Error; err != nil {
		r.Log.WithContext(ctx).Errorf("count GetAssetAdminUserList, err: %v", err.Error())
		return nil, count, err
	}
	var finds []assetBiz.AssetAdminUser

	db.Offset(int(req.PageNo-1) * int(req.PageSize)).Limit(int(req.PageSize)).Find(&finds)
	return finds, count, nil
}

func (r *AssetRepo) CreateAssetAdminUser(ctx context.Context, req *assetV1.CreateAssetAdminUserRequest) error {
	secretKey := []byte(r.Conf.OptSecretKey)
	privateKey, err := cryptoUtil.Encrypt(secretKey, req.PrivateKey)
	if err != nil {
		r.Log.Errorf("Decrypt Asset Admin User PrivateKey, err: %v", err.Error())
		return err
	}

	db := r.Data.Db.Table("t_asset_admin_users")
	db = db.Create(&biz.AssetAdminUser{
		OrganizationId: req.OrganizationId,
		Username:       req.Username,
		Password:       req.Password,
		PrivateKey:     privateKey,
		Comment:        req.Comment,
	})
	if db.Error != nil {
		r.Log.WithContext(ctx).Errorf("CreateAssetAdminUser, err: %v", db.Error.Error())
		return db.Error
	}
	return nil
}

func (r *AssetRepo) DeleteAssetAdminUser(ctx context.Context, req *assetV1.DeleteAssetAdminUserRequest) error {
	if err := r.Data.Db.Table("t_asset_admin_users").Where("id = ?", req.Id).Delete(&biz.AssetAdminUser{}).Error; err != nil && err != gorm.ErrRecordNotFound {
		r.Log.WithContext(ctx).Errorf("DeleteAssetAdminUser, err: %v", err.Error())
	}
	return nil
}

func (r *AssetRepo) UpdateAssetAdminUser(ctx context.Context, req *assetV1.UpdateAssetAdminUserRequest) error {
	secretKey := []byte(r.Conf.OptSecretKey)
	privateKey, err := cryptoUtil.Decrypt(secretKey, req.PrivateKey)
	if err != nil {
		r.Log.Errorf("Decrypt Asset Admin User PrivateKey, err: %v", err.Error())
		return err
	}

	if err := r.Data.Db.Debug().Table("t_asset_admin_users").Where("id = ?", req.Id).Updates(&AssetAdminUser{
		OrganizationId: req.OrganizationId,
		Username:       req.Username,
		Password:       req.Password,
		PrivateKey:     privateKey,
		Comment:        req.Comment,
	}).Error; err != nil && err != gorm.ErrRecordNotFound {
		r.Log.WithContext(ctx).Errorf("UpdateAssetAdminUser, err: %v", err.Error())
	}
	return nil
}
