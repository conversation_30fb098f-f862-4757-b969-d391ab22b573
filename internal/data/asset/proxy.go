package data

import (
	"context"

	cryptoUtil "gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/pkg/crypto_utils"

	assetV1 "gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/api/asset/v1"
	assetBiz "gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/internal/biz/asset"
	biz "gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/internal/biz/asset"
	"gorm.io/gorm"
)

func (r *AssetRepo) GetAssetProxyGatewayList(ctx context.Context, req *assetV1.GetAssetProxyGatewayListRequest) ([]assetBiz.AssetProxyGateway, int64, error) {
	db := r.Data.Db.Table("t_asset_proxy_gateways")

	if req.SearchText != "" {
		key := "%" + req.SearchText + "%"
		db = db.Where("name like ? or ip like ?", key, key)
	}
	var count int64
	if err := db.Count(&count).Error; err != nil {
		r.Log.WithContext(ctx).Errorf("count GetAssetProxyGatewayList, err: %v", err.Error())
		return nil, count, err
	}
	var finds []assetBiz.AssetProxyGateway

	db.Offset(int(req.PageNo-1) * int(req.PageSize)).Limit(int(req.PageSize)).Find(&finds)
	return finds, count, nil
}

func (r *AssetRepo) CreateAssetProxyGateway(ctx context.Context, req *assetV1.CreateAssetProxyGatewayRequest) error {
	secretKey := []byte(r.Conf.OptSecretKey)
	privateKey, err := cryptoUtil.Encrypt(secretKey, req.PrivateKey)
	if err != nil {
		r.Log.Errorf("Decrypt Asset Proxy Gateway PrivateKey, err: %v", err.Error())
		return err
	}
	db := r.Data.Db.Table("t_asset_proxy_gateways")
	db = db.Create(&biz.AssetProxyGateway{
		OrganizationId: req.OrganizationId,
		Name:           req.Name,
		Ip:             req.Ip,
		Port:           req.Port,
		Idc:            req.Idc,
		Username:       req.Username,
		Password:       privateKey,
		PrivateKey:     req.PrivateKey,
		Comment:        req.Comment,
	})
	if db.Error != nil {
		r.Log.WithContext(ctx).Errorf("CreateAssetProxyGateway, err: %v", db.Error.Error())
		return db.Error
	}
	return nil
}

func (r *AssetRepo) DeleteAssetProxyGateway(ctx context.Context, req *assetV1.DeleteAssetProxyGatewayRequest) error {
	if err := r.Data.Db.Table("t_asset_proxy_gateways").Where("id = ?", req.Id).Delete(&biz.AssetProxyGateway{}).Error; err != nil && err != gorm.ErrRecordNotFound {
		r.Log.WithContext(ctx).Errorf("DeleteAssetProxyGateway, err: %v", err.Error())
	}
	return nil
}

func (r *AssetRepo) UpdateAssetProxyGateway(ctx context.Context, req *assetV1.UpdateAssetProxyGatewayRequest) error {
	secretKey := []byte(r.Conf.OptSecretKey)
	privateKey, err := cryptoUtil.Encrypt(secretKey, req.PrivateKey)
	if err != nil {
		r.Log.Errorf("Decrypt Asset Proxy Gateway PrivateKey, err: %v", err.Error())
		return err
	}
	if err := r.Data.Db.Debug().Table("t_asset_proxy_gateways").Where("id = ?", req.Id).Updates(&AssetProxyGateway{
		OrganizationId: req.OrganizationId,
		Name:           req.Name,
		Ip:             req.Ip,
		Port:           req.Port,
		Username:       req.Username,
		Password:       req.Password,
		PrivateKey:     privateKey,
		Comment:        req.Comment,
	}).Error; err != nil && err != gorm.ErrRecordNotFound {
		r.Log.WithContext(ctx).Errorf("UpdateAssetProxyGateway, err: %v", err.Error())
	}
	return nil
}
func (r *AssetRepo) GetAssetProxyGatewaySelect(ctx context.Context) ([]assetBiz.AssetProxyGateway, error) {
	var finds []assetBiz.AssetProxyGateway
	if err := r.Data.Db.Table("t_asset_proxy_gateways").Find(&finds).Error; err != nil {
		r.Log.WithContext(ctx).Errorf("GetAssetProxyGatewaySelect, err: %v", err.Error())
		return nil, err
	}
	return finds, nil
}
