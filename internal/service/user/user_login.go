package service

import (
	"context"
	"errors"

	"github.com/go-kratos/kratos/v2/transport"
	"github.com/go-kratos/kratos/v2/transport/http"
	"github.com/jinzhu/copier"
	"gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/pkg/util/common"

	userV1 "gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/api/user/v1"
	userBiz "gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/internal/biz/user"
)

func (s *UserService) UserLoginPwd(ctx context.Context, reqPB *userV1.UserLoginPwdRequest) (reply *userV1.UserLoginReply, err error) {
	req := &userBiz.UserLoginPwdReq{}
	_ = copier.Copy(&req, reqPB)
	userLoginReply, err := s.user.UserLoginPwd(ctx, req)
	if err != nil {
		return nil, err
	}
	reply = &userV1.UserLoginReply{}
	_ = copier.Copy(&reply, userLoginReply)
	return
}

func (s *UserService) UserLoginEmailVerification(ctx context.Context, reqPB *userV1.UserLoginEmailVerificationRequest) (reply *userV1.UserLoginReply, err error) {
	req := &userBiz.UserLoginEmailVerificationReq{}
	_ = copier.Copy(&req, reqPB)
	userLoginReply, err := s.user.UserLoginEmailVerification(ctx, req)
	if err != nil {
		return nil, err
	}
	reply = &userV1.UserLoginReply{}
	_ = copier.Copy(&reply, userLoginReply)
	return
}

func (s *UserService) UserLoginEmailSendCode(ctx context.Context, reqPB *userV1.UserLoginEmailSendCodeRequest) (reply *userV1.CommonReply, err error) {
	req := &userBiz.UserLoginEmailSendCodeReq{}
	_ = copier.Copy(&req, reqPB)
	err = s.user.UserLoginEmailSendCode(ctx, req)
	if err != nil {
		return nil, err
	}
	reply = &userV1.CommonReply{
		Code:    200,
		Message: "success",
	}
	return
}

func (s *UserService) UserLoginSSO(ctx context.Context, reqPB *userV1.EmptyRequest) (reply *userV1.UserLoginReply, err error) {
	var ssoToken string
	if header, ok := transport.FromServerContext(ctx); ok {
		ssoToken = header.RequestHeader().Get("Ctm-Token")
	}
	userLoginReply, err := s.user.UserLoginSSO(ctx, ssoToken, userBiz.PlatFormTerminal)
	if err != nil {
		return nil, err
	}
	reply = &userV1.UserLoginReply{}
	_ = copier.Copy(reply, userLoginReply)
	return
}

func (s *UserService) UserLoginOut(ctx context.Context, reqPB *userV1.EmptyRequest) (reply *userV1.CommonReply, err error) {
	err = s.user.UserLoginOut(ctx, userBiz.PlatFormTerminal)
	if err != nil {
		s.log.WithContext(ctx).Errorf("get user info from token error, err:%s", err.Error())
		return nil, err
	}
	reply = &userV1.CommonReply{
		Code:    200,
		Message: "success",
	}
	return
}

func (s *UserService) IsExistInJwtBlackList(ctx context.Context) (exist bool, err error) {
	if httpTransport, ok := http.RequestFromServerContext(ctx); ok {
		whiteList := httpTransport.Header.Get("WhiteList")
		if whiteList == "true" {
			return false, nil
		}
	}

	return s.user.IsExistInJwtBlackList(ctx)
}

func (s *UserService) UpdateUserPwd(ctx context.Context, reqPB *userV1.UpdateUserPwdRequest) (reply *userV1.CommonReply, err error) {
	userInfo, err := common.GetUserInfoByToken(ctx)
	if err != nil {
		return nil, errors.New("token check error: " + err.Error())
	}
	req := &userBiz.UpdateUserPwdReq{}
	_ = copier.Copy(&req, reqPB)
	req.Uid = userInfo.Uid
	err = s.user.UpdateUserPwd(ctx, req)
	if err != nil {
		return nil, err
	}

	return &userV1.CommonReply{
		Code:    200,
		Message: "success",
	}, nil
}
