package service

import (
	"context"
	"errors"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/jinzhu/copier"
	userV1 "gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/api/user/v1"
	userBiz "gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/internal/biz/user"
	"gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/pkg/util/common"
	"google.golang.org/protobuf/types/known/emptypb"
)

type UserService struct {
	userV1.UnimplementedUserServer
	user *userBiz.UserUsecase
	log  *log.Helper
}

func NewUserService(user *userBiz.UserUsecase, logger log.Logger) *UserService {
	return &UserService{
		user: user,
		log:  log.NewHelper(log.With(logger, "module", "service/asset"))}
}

func (s *UserService) <PERSON><PERSON><PERSON>(ctx context.Context, reqPB *userV1.HelloRequest) (*userV1.HelloReply, error) {
	rsp, _ := s.user.GetUserByCtx(ctx)
	return &userV1.HelloReply{
		Message: "Hello_" + rsp.Name + "_" + rsp.Email + "_" + rsp.Avatar,
	}, nil
}

func (s *UserService) GetUserByCtx(ctx context.Context, reqPB *userV1.EmptyRequest) (*userV1.GetUserByCtxReply, error) {
	getUserRsp, _ := s.user.GetUserByCtx(ctx)
	rsp := &userV1.GetUserByCtxReply{}
	_ = copier.Copy(&rsp, getUserRsp)
	return rsp, nil
}

func (s *UserService) GetUserTermConfig(ctx context.Context, reqPB *userV1.EmptyRequest) (reply *userV1.GetUserTermConfigReply, err error) {
	getUserTermConfigReply, err := s.user.GetUserTermConfig(ctx)
	if err != nil {
		return nil, err
	}
	reply = &userV1.GetUserTermConfigReply{}
	_ = copier.Copy(&reply, getUserTermConfigReply)
	return
}

func (s *UserService) UpdateUserTermConfig(ctx context.Context, reqPB *userV1.UpdateUserTermConfigRequest) (reply *userV1.CommonReply, err error) {
	req := &userBiz.UserTermConfig{}
	_ = copier.Copy(&req, reqPB)
	err = s.user.UpdateUserTermConfig(ctx, req)
	if err != nil {
		return nil, err
	}
	reply = &userV1.CommonReply{
		Code:    200,
		Message: "success",
	}
	return
}

func (s *UserService) ListUserQuickCommand(ctx context.Context, reqPB *userV1.ListUserQuickCommandRequest) (*userV1.ListUserQuickCommandReply, error) {
	req := &userBiz.ListUserQuickCommandReq{}
	_ = copier.Copy(&req, reqPB)
	userInfo, err := common.GetUserInfoByToken(ctx)
	if err != nil {
		return nil, errors.New("token check error: " + err.Error())
	}
	req.Uid = userInfo.Uid
	listUserQuickCommandRsp, count, err := s.user.ListUserQuickCommand(ctx, req)
	if err != nil {
		return nil, err
	}
	userQuickCommandPBs := make([]*userV1.UserQuickCommand, 0, len(listUserQuickCommandRsp))
	_ = copier.Copy(&userQuickCommandPBs, listUserQuickCommandRsp)
	var totalCount, totalPage int32
	totalCount = int32(count)
	if totalCount > 0 && req.PageSize != 0 && totalCount%req.PageSize == 0 {
		totalPage = totalCount / req.PageSize
	} else if req.PageSize != 0 {
		totalPage = totalCount/req.PageSize + 1
	}

	return &userV1.ListUserQuickCommandReply{
		Data:       userQuickCommandPBs,
		PageNo:     req.PageNo,
		PageSize:   req.PageSize,
		TotalCount: totalCount,
		TotalPage:  totalPage,
	}, nil
}

func (s *UserService) CreateUserQuickCommand(ctx context.Context, reqPB *userV1.CreateUserQuickCommandRequest) (*userV1.CommonReply, error) {
	userInfo, err := common.GetUserInfoByToken(ctx)
	if err != nil {
		return nil, errors.New("token check error: " + err.Error())
	}
	req := &userBiz.UserQuickCommand{}
	_ = copier.Copy(&req, reqPB)
	req.Uid = userInfo.Uid
	err = s.user.CreateUserQuickCommand(ctx, req)
	if err != nil {
		return nil, err
	}
	rsp := &userV1.CommonReply{
		Code:    200,
		Message: "ok",
	}
	return rsp, nil
}

func (s *UserService) GetUserQuickCommand(ctx context.Context, req *userV1.GetUserQuickCommandRequest) (*userV1.GetUserQuickCommandReply, error) {
	userInfo, err := common.GetUserInfoByToken(ctx)
	if err != nil {
		return nil, errors.New("token check error: " + err.Error())
	}
	reply, err := s.user.GetUserQuickCommand(ctx, req.Id, userInfo.Uid)
	if err != nil {
		return nil, err
	}
	term := new(userV1.GetUserQuickCommandReply)
	_ = copier.Copy(term, reply)
	return term, nil
}

func (s *UserService) UpdateUserQuickCommand(ctx context.Context, reqPB *userV1.UpdateUserQuickCommandRequest) (*userV1.CommonReply, error) {
	userInfo, err := common.GetUserInfoByToken(ctx)
	if err != nil {
		return nil, errors.New("token check error: " + err.Error())
	}
	req := &userBiz.UserQuickCommand{}
	_ = copier.Copy(&req, reqPB)
	req.Uid = userInfo.Uid
	err = s.user.UpdateUserQuickCommand(ctx, req)
	if err != nil {
		return nil, err
	}
	rsp := &userV1.CommonReply{
		Code:    200,
		Message: "ok",
	}
	return rsp, nil
}

func (s *UserService) DeleteUserQuickCommand(ctx context.Context, reqPB *userV1.DeleteUserQuickCommandRequest) (*userV1.CommonReply, error) {
	userInfo, err := common.GetUserInfoByToken(ctx)
	if err != nil {
		return nil, errors.New("token check error: " + err.Error())
	}
	err = s.user.DeleteUserQuickCommand(ctx, reqPB.Id, userInfo.Uid)
	if err != nil {
		return nil, err
	}
	return &userV1.CommonReply{
		Message: "ok",
	}, nil
}

func (s *UserService) CheckUserDevice(ctx context.Context, reqPB *userV1.CheckUserDeviceRequest) (*userV1.CheckUserDeviceReply, error) {
	userInfo, err := common.GetUserInfoByToken(ctx)
	if err != nil {
		return nil, errors.New("token check error: " + err.Error())
	}
	if userInfo.RegistrationType != 1 {
		return &userV1.CheckUserDeviceReply{
			Ip:             reqPB.Ip,
			MacAddress:     reqPB.MacAddress,
			IsOfficeDevice: false,
		}, nil
	}
	req := &userBiz.CheckUserDeviceReq{}
	_ = copier.Copy(&req, reqPB)
	req.Email = userInfo.Email
	isOfficeDevice, err := s.user.CheckUserDevice(ctx, req)
	if err != nil {
		return nil, err
	}
	return &userV1.CheckUserDeviceReply{
		Ip:             req.Ip,
		MacAddress:     req.MacAddress,
		IsOfficeDevice: isOfficeDevice,
	}, nil
}

func (s *UserService) GetUserInfoByEmail(ctx context.Context, req *userV1.GetUserInfoByEmailRequest) (*userV1.GetUserInfoByEmailReply, error) {
	rsp, err := s.user.GetUserInfoByEmail(ctx, req.Email, req.OrganizationId)
	if err != nil {
		return nil, err
	}
	var outs []*userV1.GetUserInfoByEmailReply_User
	for _, v := range rsp {
		var out = &userV1.GetUserInfoByEmailReply_User{}
		copier.Copy(out, v)
		outs = append(outs, out)
	}

	return &userV1.GetUserInfoByEmailReply{
		Data: outs,
	}, nil
}

func (s *UserService) UpdateUser(ctx context.Context, reqPB *userV1.UpdateUserRequest) (*userV1.GetUserByCtxReply, error) {
	userInfo, err := common.GetUserInfoByToken(ctx)
	if err != nil {
		return nil, errors.New("token check error: " + err.Error())
	}
	req := &userBiz.UpdateUserReq{}
	_ = copier.Copy(&req, reqPB)
	req.Uid = userInfo.Uid
	user, err := s.user.UpdateUser(ctx, req)
	if err != nil {
		return nil, err
	}
	rsp := &userV1.GetUserByCtxReply{}
	_ = copier.Copy(&rsp, user)
	return rsp, nil
}

func (s *UserService) BatchFlushUserInfo(ctx context.Context, req *emptypb.Empty) (*userV1.CommonReply, error) {
	err := s.user.BatchFlushUserInfo(ctx)
	if err != nil {
		return nil, err
	}
	rsp := &userV1.CommonReply{
		Code:    200,
		Message: "success",
	}
	return rsp, nil
}
