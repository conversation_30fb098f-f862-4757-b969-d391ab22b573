package service

import (
	"github.com/google/wire"
	aiSvc "gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/internal/service/ai"
	assetSvc "gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/internal/service/asset"
	permSvc "gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/internal/service/perm"
	userSvc "gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/internal/service/user"
)

// ProviderSet is service providers.
var ProviderSet = wire.NewSet(userSvc.NewUserService, aiSvc.NewAiService, assetSvc.NewAssetService, permSvc.NewPermService)
