package service

import (
	"log"
	"net/http"

	"github.com/gorilla/websocket"
	aiBiz "gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/internal/biz/ai"
	"gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/internal/client"
	pkg "gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/pkg/util/common"
)

// Server 表示 WebSocket 服务器
type Server struct {
	LlmAddr  string
	Upgrader websocket.Upgrader
}

func NewServer(llmAddr string) *Server {
	return &Server{
		LlmAddr: llmAddr,
		Upgrader: websocket.Upgrader{
			CheckOrigin: func(r *http.Request) bool {
				return true
			},
		},
	}
}

func (t *Server) HandleWebSocket(s *AiService) func(w http.ResponseWriter, r *http.Request) {
	return func(w http.ResponseWriter, r *http.Request) {
		// 从查询参数中获取 token
		token := r.URL.Query().Get("token")
		if token == "" {
			log.Printf("WebSocket连接缺少token")
			w.WriteHeader(http.StatusUnauthorized)
			return
		}
		user, _ := pkg.GetTokenToUserInfo(token)
		// 创建 WebSocket 连接
		wsConn, err := t.Upgrader.Upgrade(w, r, nil)
		if err != nil {
			log.Printf("WebSocket升级失败: %v", err)
			return
		}
		defer wsConn.Close()

		// 创建 LLM 客户端
		llmClient := client.NewLLMClient(t.LlmAddr)
		// 创建 WebSocket 处理器
		handler := aiBiz.NewWsHandler(wsConn, llmClient, s.ai, user.Email)
		// 处理 WebSocket 连接
		handler.Handle()
	}
}
