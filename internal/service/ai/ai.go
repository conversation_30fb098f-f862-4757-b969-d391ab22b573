package service

import (
	"context"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/jinzhu/copier"
	aiV1 "gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/api/ai/v1"
	aiBiz "gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/internal/biz/ai"
	"google.golang.org/protobuf/types/known/emptypb"
)

type AiService struct {
	aiV1.UnimplementedAiServer
	ai  *aiBiz.AiUsecase
	log *log.Helper
}

func NewAiService(ai *aiBiz.AiUsecase, logger log.Logger) *AiService {
	return &AiService{
		ai:  ai,
		log: log.NewHelper(log.With(logger, "module", "service/ai"))}
}

func (s *AiService) GetModelList(ctx context.Context, req *emptypb.Empty) (*aiV1.GetModelListReply, error) {
	rsp, err := s.ai.GetModelList(ctx)
	if err != nil {
		s.log.Errorf("GetModelList error: %v", err)
		return nil, err
	}
	var outs = []*aiV1.GetModelListReply_Model{}
	for _, v := range rsp {
		var out = &aiV1.GetModelListReply_Model{}
		copier.Copy(out, v)
		outs = append(outs, out)
	}

	return &aiV1.GetModelListReply{
		Models: outs,
	}, nil
}

func (s *AiService) GetUserConversationList(ctx context.Context, req *emptypb.Empty) (*aiV1.GetUserConversationListReply, error) {
	rsp, err := s.ai.GetUserConversationList(ctx)
	if err != nil {
		s.log.Errorf("GetUserConversationList error: %v", err)
		return nil, err
	}
	var outs = []*aiV1.GetUserConversationListReply_Conversation{}
	for _, v := range rsp {
		var out = &aiV1.GetUserConversationListReply_Conversation{}
		copier.Copy(out, v)
		out.CreatedAt = uint64(v.CreatedAt.Unix())
		out.UpdatedAt = uint64(v.UpdatedAt.Unix())
		outs = append(outs, out)
	}

	return &aiV1.GetUserConversationListReply{
		List: outs,
	}, nil
}

func (s *AiService) GetConversationDetail(ctx context.Context, req *aiV1.GetConversationDetailRequest) (*aiV1.GetConversationDetailReply, error) {
	s.log.Info("Received GetConversationDetail request :", req)

	rsp, count, err := s.ai.GetConversationDetail(ctx, req)
	if err != nil {
		s.log.Errorf("GetConversationDetail error: %v", err)
		return nil, err
	}
	var outs = []*aiV1.GetConversationDetailReply_Detail{}
	for _, v := range rsp {
		var out = &aiV1.GetConversationDetailReply_Detail{}
		copier.Copy(out, v)
		out.CreatedAt = uint64(v.CreatedAt.Unix())
		out.SendAt = uint64(v.SendAt.Unix())
		outs = append(outs, out)
	}

	return &aiV1.GetConversationDetailReply{
		List:  outs,
		Total: count,
	}, nil
}
