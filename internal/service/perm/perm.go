package service

import (
	"context"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/jinzhu/copier"
	permV1 "gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/api/perm/v1"
	permBiz "gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/internal/biz/perm"
	cm "gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/pkg/util/common"
)

type PermService struct {
	permV1.UnimplementedPermServer
	perm *permBiz.PermUsecase
	log  *log.Helper
}

func NewPermService(perm *permBiz.PermUsecase, logger log.Logger) *PermService {
	return &PermService{
		perm: perm,
		log:  log.NewHelper(log.With(logger, "module", "service/perm"))}

}

func (s *PermService) CreateUserAssetPermission(ctx context.Context, reqPB *permV1.CreateUserAssetPermissionRequest) (*permV1.CommonReply, error) {
	s.log.Infof("CreateUserAssetPermission", reqPB)
	msg, _ := s.perm.CreateUserAssetPermission(ctx, reqPB)
	return &permV1.CommonReply{
		Message: msg,
	}, nil
}

func (s *PermService) GetUserAssetPermissionList(ctx context.Context, reqPB *permV1.GetUserAssetPermissionListRequest) (*permV1.GetUserAssetPermissionListReply, error) {
	s.log.Infof("GetUserAssetPermissionList", reqPB)
	rsp, totalCount, err := s.perm.GetUserAssetPermissionList(ctx, reqPB)
	if err != nil {
		return nil, err
	}
	var outs []*permV1.GetUserAssetPermissionListReply_User
	for _, v := range rsp {
		var out = &permV1.GetUserAssetPermissionListReply_User{}
		copier.Copy(out, v)
		outs = append(outs, out)
	}

	return &permV1.GetUserAssetPermissionListReply{
		Data:       outs,
		PageNo:     reqPB.PageNo,
		PageSize:   reqPB.PageSize,
		TotalCount: totalCount,
		TotalPage:  cm.ListPage(totalCount, reqPB.PageSize),
	}, nil
}
