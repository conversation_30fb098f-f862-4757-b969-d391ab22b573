package service

import (
	"context"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/jinzhu/copier"
	assetV1 "gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/api/asset/v1"
	assetBiz "gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/internal/biz/asset"
	cm "gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/pkg/util/common"
	"google.golang.org/protobuf/types/known/emptypb"
)

type AssetService struct {
	assetV1.UnimplementedAssetServer
	asset *assetBiz.AssetUsecase
	log   *log.Helper
}

func NewAssetService(asset *assetBiz.AssetUsecase, logger log.Logger) *AssetService {
	return &AssetService{
		asset: asset,
		log:   log.NewHelper(log.With(logger, "module", "service/asset"))}
}

func (s *AssetService) GetAssetList(ctx context.Context, req *assetV1.GetAssetListRequest) (*assetV1.GetAssetListReply, error) {
	s.log.Infof("GetAssetList", req)
	rsp, totalCount, err := s.asset.GetAssetList(ctx, req)
	if err != nil {
		s.log.Errorf("GetModelList error: %v", err)
		return nil, err
	}
	var outs = []*assetV1.GetAssetListReply_Asset{}
	for _, v := range rsp {
		var out = &assetV1.GetAssetListReply_Asset{}
		copier.Copy(out, v)
		out.CreatedAt = v.CreatedAt.Format("2006-01-02 15:04:05")
		out.UpdatedAt = v.UpdatedAt.Format("2006-01-02 15:04:05")
		outs = append(outs, out)
	}

	return &assetV1.GetAssetListReply{
		Data:       outs,
		PageNo:     req.PageNo,
		PageSize:   req.PageSize,
		TotalCount: totalCount,
		TotalPage:  cm.ListPage(totalCount, req.PageSize),
	}, nil
}

func (s *AssetService) UserAssetFavorite(ctx context.Context, req *assetV1.UserAssetFavoriteRequest) (*assetV1.Reply, error) {
	s.log.Infof("UserAssetFavorite", req)
	err := s.asset.UserAssetFavorite(ctx, req)
	if err != nil {
		s.log.Errorf("UserAssetFavorite error: %v", err)
		return nil, err
	}
	return &assetV1.Reply{
		Message: "success",
	}, nil
}

func (s *AssetService) ListAssetRoute(ctx context.Context, req *assetV1.ListAssetRouteRequest) (*assetV1.ListAssetRouteReply, error) {
	s.log.Infof("ListAssetRoute", req)
	out, err := s.asset.ListAssetRoute(ctx, req.OrganizationId)
	if err != nil {
		s.log.Errorf("UserAssetFavorite error: %v", err)
		return nil, err
	}
	return &assetV1.ListAssetRouteReply{
		Routers: out,
	}, nil
}

func (s *AssetService) CreateAsset(ctx context.Context, req *assetV1.CreateAssetRequest) (*assetV1.Reply, error) {
	s.log.Infof("CreateAsset", req)
	err := s.asset.CreateAsset(ctx, req)
	if err != nil {
		s.log.Errorf("CreateAsset error: %v", err)
		return nil, err
	}
	return &assetV1.Reply{
		Message: "success",
	}, nil
}

func (s *AssetService) DeleteAsset(ctx context.Context, req *assetV1.DeleteAssetRequest) (*assetV1.Reply, error) {
	s.log.Infof("DeleteAsset", req)
	err := s.asset.DeleteAsset(ctx, req)
	if err != nil {
		s.log.Errorf("DeleteAsset error: %v", err)
		return nil, err
	}
	return &assetV1.Reply{
		Message: "success",
	}, nil
}

func (s *AssetService) UpdateAsset(ctx context.Context, req *assetV1.UpdateAseetRequest) (*assetV1.Reply, error) {
	s.log.Infof("UpdateAsset", req)
	err := s.asset.UpdateAsset(ctx, req)
	if err != nil {
		s.log.Errorf("UpdateAsset error: %v", err)
		return nil, err
	}
	return &assetV1.Reply{
		Message: "success",
	}, nil
}

func (s *AssetService) UserAssetAlias(ctx context.Context, req *assetV1.UserAssetAliasRequest) (*assetV1.Reply, error) {
	s.log.Infof("userAssetAlias", req)
	err := s.asset.UserAssetAlias(ctx, req)
	if err != nil {
		s.log.Errorf("userAssetAlias error: %v", err)
		return nil, err
	}
	return &assetV1.Reply{
		Message: "success",
	}, nil
}

func (s *AssetService) ListUserWorkSpace(ctx context.Context, req *emptypb.Empty) (*assetV1.ListUserWorkSpaceReply, error) {
	s.log.Infof("ListUserWorkSpace", req)

	outWorkSpaces, err := s.asset.ListUserWorkSpace(ctx)
	if err != nil {
		s.log.Errorf("ListUserWorkSpace error: %v", err)
		return nil, err
	}
	var outs = []*assetV1.ListUserWorkSpaceReply_WorkSpace{}
	for _, v := range outWorkSpaces {
		var out = &assetV1.ListUserWorkSpaceReply_WorkSpace{}
		copier.Copy(out, v)
		outs = append(outs, out)
	}
	return &assetV1.ListUserWorkSpaceReply{
		Data: outs}, nil
}

func (s *AssetService) GetAssetListByIp(ctx context.Context, req *assetV1.GetAssetListByIpRequest) (*assetV1.GetAssetListByIpReply, error) {
	s.log.Infof("GetAssetListByIp", req)
	rsp, totalCount, err := s.asset.GetAssetListByIp(ctx, req)
	if err != nil {
		s.log.Errorf("GetModelList error: %v", err)
		return nil, err
	}
	var outs []*assetV1.GetAssetListByIpReply_Asset
	for _, v := range rsp {
		var out = &assetV1.GetAssetListByIpReply_Asset{}
		copier.Copy(out, v)
		outs = append(outs, out)
	}

	return &assetV1.GetAssetListByIpReply{
		Data:       outs,
		PageNo:     req.PageNo,
		PageSize:   req.PageSize,
		TotalCount: totalCount,
		TotalPage:  cm.ListPage(totalCount, req.PageSize),
	}, nil
}

// 代理
func (s *AssetService) GetAssetProxyGatewaySelect(ctx context.Context, req *emptypb.Empty) (*assetV1.GetAssetProxyGatewaySelectReply, error) {
	s.log.Infof("GetAssetProxyGatewaySelect", req)
	rsp, err := s.asset.GetAssetProxyGatewaySelect(ctx)
	if err != nil {
		s.log.Errorf("GetModelList error: %v", err)
		return nil, err
	}
	var outs []*assetV1.GetAssetProxyGatewaySelectReply_AssetProxyGateway
	for _, v := range rsp {
		var out = &assetV1.GetAssetProxyGatewaySelectReply_AssetProxyGateway{}
		copier.Copy(out, v)
		outs = append(outs, out)
	}
	return &assetV1.GetAssetProxyGatewaySelectReply{
		Data: outs,
	}, nil
}

func (s *AssetService) GetAssetProxyGatewayList(ctx context.Context, req *assetV1.GetAssetProxyGatewayListRequest) (*assetV1.GetAssetProxyGatewayListReply, error) {
	s.log.Infof("GetAssetProxyGatewayList", req)
	rsp, totalCount, err := s.asset.GetAssetProxyGatewayList(ctx, req)
	if err != nil {
		s.log.Errorf("GetModelList error: %v", err)
		return nil, err
	}
	var outs []*assetV1.GetAssetProxyGatewayListReply_AssetProxyGateway
	for _, v := range rsp {
		var out = &assetV1.GetAssetProxyGatewayListReply_AssetProxyGateway{}
		copier.Copy(out, v)
		outs = append(outs, out)
	}

	return &assetV1.GetAssetProxyGatewayListReply{
		Data:       outs,
		PageNo:     req.PageNo,
		PageSize:   req.PageSize,
		TotalCount: totalCount,
		TotalPage:  cm.ListPage(totalCount, req.PageSize),
	}, nil
}

func (s *AssetService) CreateAssetProxyGateway(ctx context.Context, req *assetV1.CreateAssetProxyGatewayRequest) (*assetV1.Reply, error) {
	s.log.Infof("CreateAssetProxyGateway", req)
	err := s.asset.CreateAssetProxyGateway(ctx, req)
	if err != nil {
		s.log.Errorf("CreateAssetProxyGateway error: %v", err)
		return nil, err
	}
	return &assetV1.Reply{
		Message: "success",
	}, nil
}

func (s *AssetService) DeleteAssetProxyGateway(ctx context.Context, req *assetV1.DeleteAssetProxyGatewayRequest) (*assetV1.Reply, error) {
	s.log.Infof("DeleteAssetProxyGateway", req)
	err := s.asset.DeleteAssetProxyGateway(ctx, req)
	if err != nil {
		s.log.Errorf("DeleteAssetProxyGateway error: %v", err)
		return nil, err
	}
	return &assetV1.Reply{
		Message: "success",
	}, nil
}

func (s *AssetService) UpdateAssetProxyGateway(ctx context.Context, req *assetV1.UpdateAssetProxyGatewayRequest) (*assetV1.Reply, error) {
	s.log.Infof("UpdateAssetProxyGateway", req)
	err := s.asset.UpdateAssetProxyGateway(ctx, req)
	if err != nil {
		s.log.Errorf("UpdateAssetProxyGateway error: %v", err)
		return nil, err
	}
	return &assetV1.Reply{
		Message: "success",
	}, nil
}

// 管理员账户
func (s *AssetService) GetAssetAdminUserList(ctx context.Context, req *assetV1.GetAssetAdminUserListRequest) (*assetV1.GetAssetAdminUserListReply, error) {
	s.log.Infof("GetAssetAdminUserList", req)
	rsp, totalCount, err := s.asset.GetAssetAdminUserList(ctx, req)
	if err != nil {
		s.log.Errorf("GetModelList error: %v", err)
		return nil, err
	}
	var outs []*assetV1.GetAssetAdminUserListReply_AssetAdminUser
	for _, v := range rsp {
		var out = &assetV1.GetAssetAdminUserListReply_AssetAdminUser{}
		copier.Copy(out, v)
		outs = append(outs, out)
	}

	return &assetV1.GetAssetAdminUserListReply{
		Data:       outs,
		PageNo:     req.PageNo,
		PageSize:   req.PageSize,
		TotalCount: totalCount,
		TotalPage:  cm.ListPage(totalCount, req.PageSize),
	}, nil
}

func (s *AssetService) CreateAssetAdminUser(ctx context.Context, req *assetV1.CreateAssetAdminUserRequest) (*assetV1.Reply, error) {
	s.log.Infof("CreateAssetAdminUser", req)
	err := s.asset.CreateAssetAdminUser(ctx, req)
	if err != nil {
		s.log.Errorf("CreateAssetAdminUser error: %v", err)
		return nil, err
	}
	return &assetV1.Reply{
		Message: "success",
	}, nil
}

func (s *AssetService) DeleteAssetAdminUser(ctx context.Context, req *assetV1.DeleteAssetAdminUserRequest) (*assetV1.Reply, error) {
	s.log.Infof("DeleteAssetAdminUser", req)
	err := s.asset.DeleteAssetAdminUser(ctx, req)
	if err != nil {
		s.log.Errorf("DeleteAssetAdminUser error: %v", err)
		return nil, err
	}
	return &assetV1.Reply{
		Message: "success",
	}, nil
}

func (s *AssetService) UpdateAssetAdminUser(ctx context.Context, req *assetV1.UpdateAssetAdminUserRequest) (*assetV1.Reply, error) {
	s.log.Infof("UpdateAssetAdminUser", req)
	err := s.asset.UpdateAssetAdminUser(ctx, req)
	if err != nil {
		s.log.Errorf("UpdateAssetAdminUser error: %v", err)
		return nil, err
	}
	return &assetV1.Reply{
		Message: "success",
	}, nil
}
