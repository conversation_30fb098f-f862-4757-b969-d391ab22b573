syntax = "proto3";
package kratos.api;

option go_package = "terminal/internal/conf;conf";


message Bootstrap {
  Server server = 1;
  Data data = 2;
  Sso sso = 3;
  repeated string internalIP = 4;
  OtherInfo otherInfo = 5;
  EmailConfig emailConfig = 6;
  WecomConfig wecomConfig = 7;
}

message EmailConfig {
  string host = 1;
  string port = 2;
  string user = 3;
  string pwd = 4;
  string nickname = 5;
}

message WecomConfig {
  string corpId = 1;
  string corpSecret = 2;
  string agentId = 3;
}

message Sso {
  string platform_id = 1;
  string api = 2;
  string key = 3;
  string open_secret = 4;
  map<string, string> cloud_plate_id = 5;
  string web_platform_id = 6;
}

message OtherInfo {
  string optSecretKey = 1;
  string llmGatewayAddr = 2;
  string llmGatewayKey = 3;
  string officeDeviceApi = 4;
  string cloudApi = 5;
  string cloudPlatId = 6;
}

message Server {
  message HTTP {
    string network = 1;
    string addr = 2;
    string timeout = 3;
  }
  message GRPC {
    string network = 1;
    string addr = 2;
    string timeout = 3;
  }
  HTTP http = 1;
  GRPC grpc = 2;
}

message Data {
  message Database {
    string driver = 1;
    string source = 2;
  }
  message Redis {
    string network = 1;
    string addr = 2;
    string user = 3;
    string password = 4;
    string read_timeout = 5;
    string write_timeout = 6;
  }
  Database database = 1;
  Redis redis = 2;
}
