package client

import (
	"bytes"
	"crypto/tls"
	"encoding/json"
	"io/ioutil"
	"net"
	"net/http"
	"time"
)

// Client struct
type Client struct {
	Dial      int64
	KeepAlive int64
	Client    *http.Client
}

// NewClient new a http client.
func NewClient() (client *Client) {
	client = &Client{
		Dial:      2,
		KeepAlive: 2,
	}

	var (
		transport *http.Transport
		dialer    *net.Dialer
	)
	dialer = &net.Dialer{
		Timeout:   time.Duration(2 * int64(time.Second)),
		KeepAlive: time.Duration(2 * int64(time.Second)),
	}
	transport = &http.Transport{
		DialContext:         dialer.DialContext,
		TLSClientConfig:     &tls.Config{InsecureSkipVerify: true},
		MaxIdleConnsPerHost: 20,
	}
	client.Client = &http.Client{
		Transport: transport,
		Timeout:   200 * time.Second,
	}
	return
}

// 保留状态码
func (client *Client) DoReq(req *http.Request) (res *http.Response, body []byte, err error) {
	if res, err = client.Client.Do(req); err != nil {
		return
	}
	defer res.Body.Close()
	if body, err = ioutil.ReadAll(res.Body); err != nil {
		return
	}
	return
}

func (client *Client) PostJson(uri string, token string, reqBody interface{}) (res *http.Response, body []byte, err error) {
	var reqJson []byte
	if reqJson, err = json.Marshal(reqBody); err != nil {
		return
	}
	req, err := http.NewRequest("POST", uri, bytes.NewBuffer(reqJson))
	if err != nil {
		return
	}
	req.Close = true
	if token != "" {
		req.Header.Add("X-Token", token)
	}
	req.Header.Set("Content-Type", "application/json")
	res, body, err = client.DoReq(req)
	if err != nil {
		return
	}
	return
}

func (client *Client) HttpPostJson(url string, data interface{}, header map[string]string) (result interface{}, err error) {
	buf := bytes.NewBuffer(nil)
	encoder := json.NewEncoder(buf)
	if err := encoder.Encode(data); err != nil {
		return nil, err
	}
	request, err := http.NewRequest(http.MethodPost, url, buf)
	if err != nil {
		return nil, err
	}

	if header != nil {
		for k, v := range header {
			request.Header.Add(k, v)
		}
	} else {
		request.Header.Add("Content-Type", "application/json")
	}

	response, err := client.Client.Do(request)
	if err != nil {
		return nil, err
	}
	defer response.Body.Close()
	body, err := ioutil.ReadAll(response.Body)
	if err != nil {
		return nil, err
	}
	errs := json.Unmarshal(body, &result)
	if err != nil {
		return nil, errs
	}
	return result, err
}

func (client *Client) HttpGetJson(url string, data interface{}, header map[string]string) (result interface{}, err error) {
	buf := bytes.NewBuffer(nil)
	encoder := json.NewEncoder(buf)
	if err := encoder.Encode(data); err != nil {
		return nil, err
	}
	request, err := http.NewRequest(http.MethodGet, url, buf)
	if err != nil {
		return nil, err
	}
	request.Header.Add("Content-Type", "application/json")
	if header != nil {
		for k, v := range header {
			request.Header.Add(k, v)
		}
	}

	response, err := client.Client.Do(request)
	if err != nil {
		return nil, err
	}
	defer response.Body.Close()
	body, err := ioutil.ReadAll(response.Body)
	if err != nil {
		return nil, err
	}
	errs := json.Unmarshal(body, &result)
	if err != nil {
		return nil, errs
	}
	return result, err
}
