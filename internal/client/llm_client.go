package client

import (
	"bufio"
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"strings"
)

// LLMClient 表示大模型 API 客户端
type LLMClient struct {
	baseURL        string
	client         *http.Client
	ConversationId string
}

// NewLLMClient 创建一个新的大模型 API 客户端
func NewLLMClient(baseURL string) *LLMClient {
	return &LLMClient{
		baseURL:        baseURL,
		client:         &http.Client{},
		ConversationId: "",
	}
}

// Chat 发送聊天请求到大模型 API
func (c *LLMClient) Chat(request interface{}) (<-chan string, error) {
	// 将请求转换为 JSON
	requestBody, err := json.Marshal(request)
	if err != nil {
		return nil, fmt.Errorf("序列化请求失败: %w", err)
	}

	log.Printf("[%s] LLMClient请求体: %s", c.ConversationId, string(requestBody))

	// 创建 HTTP 请求
	req, err := http.NewRequest("POST", c.baseURL+"/v1/chat/completions", bytes.NewBuffer(requestBody))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %w", err)
	}
	req.Header.Set("Content-Type", "application/json")

	// 发送请求
	resp, err := c.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %w", err)
	}

	// 检查响应状态码
	if resp.StatusCode != http.StatusOK {
		resp.Body.Close()
		return nil, fmt.Errorf("API 返回错误状态码: %d", resp.StatusCode)
	}

	// 创建一个通道来传递响应
	responseChan := make(chan string)

	// 在后台处理流式响应
	go func() {
		defer resp.Body.Close()
		defer close(responseChan)

		reader := bufio.NewReader(resp.Body)
		for {
			line, err := reader.ReadString('\n')
			if err != nil {
				if err != io.EOF {
					responseChan <- fmt.Sprintf("读取响应失败: %v", err)
				}
				break
			}

			line = strings.TrimSpace(line)
			if line == "" || line == "data: [DONE]" {
				continue
			}

			// 去掉 "data: " 前缀
			if strings.HasPrefix(line, "data: ") {
				line = line[6:]
			}

			responseChan <- line
		}
	}()
	return responseChan, nil
}
