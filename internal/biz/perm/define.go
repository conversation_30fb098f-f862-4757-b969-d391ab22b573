package biz

import "time"

const (
	//脚本名称
	PushUserToAsset = "push_user_to_asset.yaml"
)

type UserAssetPermission struct {
	Id             uint
	CreatedAt      time.Time
	UpdatedAt      time.Time
	OrganizationId string
	Hostname       string
	Name           string
	Email          string
	Ip             string
	Role           string
	Uid            int64
	ExpireTime     time.Time
}
type PermissionList struct {
	Id             uint
	CreatedAt      string
	UpdatedAt      string
	OrganizationId string
	Ip             string
	Role           string
	Hostname       string
	Uid            int64
	ExpireTime     string
}

type UserAssetPermissionData struct {
	Uid            int64
	Email          string
	User           string
	OrganizationId string
	CreatedAt      time.Time
}
type UserAssetPermissionList struct {
	Uid                 int64
	Email               string
	User                string
	OrganizationId      string
	CreatedAt           string
	PermissionAssetList []*PermissionList
}

type AssetAuthorizationLog struct {
	Id             uint
	CreatedAt      time.Time
	UpdatedAt      time.Time
	OrganizationId string
	Name           string
	Email          string
	Ip             string
	Role           string
	Creator        string
	ExpireTime     time.Time
	TaskId         int64
	TaskLog        string
	Uid            int64
}

type AnsibleTask struct {
	Id           uint
	CreatedAt    time.Time
	UpdatedAt    time.Time
	TaskType     string
	TemplateName string
	Status       int64
	Target       string
	ExtraArgs    string
	ExecutedAt   time.Time
	FinishedAt   time.Time
	Log          string
}
