package biz

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"github.com/go-kratos/kratos/v2/log"
	assetV1 "gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/api/asset/v1"
	permV1 "gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/api/perm/v1"
	assetBiz "gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/internal/biz/asset"
	userBiz "gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/internal/biz/user"
	"gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/pkg/ansible"
	sshUtil "gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/pkg/ssh_utils"
	"strings"
	"time"
)

type PermRepo interface {
	CreateUserAssetPermission(ctx context.Context, data *UserAssetPermission) (string, error)
	CreateAssetAuthorizationLog(ctx context.Context, logData *AssetAuthorizationLog) (string, error)
	UpdateAssetAuthorizationLog(ctx context.Context, logData *AssetAuthorizationLog) error
	CreateAnsibleTask(ctx context.Context, taskData *AnsibleTask) (*AnsibleTask, error)
	GetAnsibleTaskByID(ctx context.Context, taskId int64) (*AnsibleTask, error)
	UpdateAnsibleTask(ctx context.Context, taskData *AnsibleTask) error
	GetUserAssetPermissionList(ctx context.Context, req *permV1.GetUserAssetPermissionListRequest) ([]*UserAssetPermissionList, int64, error)
}

type PermUsecase struct {
	repo         PermRepo
	log          *log.Helper
	userUsecase  *userBiz.UserUsecase
	assetUsecase *assetBiz.AssetUsecase
}

func NewPermUsecase(repo PermRepo, userUsecase *userBiz.UserUsecase, assetUsecase *assetBiz.AssetUsecase, logger log.Logger) *PermUsecase {
	return &PermUsecase{
		repo:         repo,
		userUsecase:  userUsecase,
		assetUsecase: assetUsecase,
		log:          log.NewHelper(log.With(logger, "module", "biz/perm")),
	}
}

type UserCreationParams struct {
	Username string
	Shell    string
	Home     string
	Groups   []string
	Host     string
	SSHUser  string
}

func (uc *PermUsecase) HandleUserAuthorization(ctx context.Context, uid int64, creator, email, organizationId, role, ip, decryptUserPublicKey string, isExpired bool, expireTime time.Time) error {
	ansibleTask := &AnsibleTask{
		TaskType:     "playbook",
		TemplateName: "push_user_to_asset.yaml",
		Target:       ip,
		Status:       0,
	}

	pushExtraVars := make(map[string]interface{})
	if role == "root" {
		pushExtraVars["sudo_setting"] = "ALL"
	} else {
		pushExtraVars["sudo_setting"] = "/bin/whoami"
	}
	pushExtraVars["username"] = strings.Split(email, "@")[0]
	pushExtraVars["public_key"] = decryptUserPublicKey

	extraVarsBytes, err := json.Marshal(pushExtraVars)
	if err != nil {
		uc.log.Errorf("failed to marshal extra vars: %w", err)
		return err
	}

	ansibleTask.ExtraArgs = string(extraVarsBytes)
	task, err := uc.repo.CreateAnsibleTask(ctx, ansibleTask)
	if err != nil {
		uc.log.Errorf("failed to create ansible task: %w", err)
		return err
	}

	logData := &AssetAuthorizationLog{
		OrganizationId: organizationId,
		Name:           strings.Split(email, "@")[0],
		Email:          email,
		Ip:             ip,
		Role:           role,
		Creator:        creator,
		Uid:            uid,
		TaskId:         int64(task.Id),
	}
	if isExpired {
		logData.ExpireTime = expireTime
	}
	_, err = uc.repo.CreateAssetAuthorizationLog(ctx, logData)
	if err != nil {
		uc.log.Errorf("Failed to create authorization log for IP %s: %v", ip, err)
		return err
	}

	go func(ip string) {
		result, status, err := uc.PushUserToAsset(ctx, organizationId, ip, pushExtraVars)
		uc.updateTaskAndLog(ctx, int64(task.Id), ip, result, status, err)
	}(ip)

	return nil
}

func (uc *PermUsecase) PushUserToAsset(ctx context.Context, organizationId, ip string, pushExtraVars map[string]interface{}) (string, bool, error) {
	proxyGateway, err := uc.assetUsecase.GetAssetProxyGateway(ctx, ip, organizationId)
	if err != nil {
		return "", false, fmt.Errorf("failed to get proxy gateway: %w", err)
	}
	gateway := &ansible.Gateway{
		Username:   proxyGateway.Username,
		Ip:         proxyGateway.Ip,
		Port:       proxyGateway.Port,
		Password:   proxyGateway.Password,
		PrivateKey: proxyGateway.PrivateKey,
	}

	//adminUser, err := uc.assetUsecase.GetAssetAdminUser(ctx, ip, organizationId)
	adminUser, err := uc.assetUsecase.GetAssetAdminUserByOrganizationId(ctx, organizationId)
	if err != nil {
		return "", false, fmt.Errorf("failed to get admin user: %w", err)
	}

	playbooks := PushUserToAsset
	opts := &ansible.ExecuteOption{
		Hosts:      ip,
		PrivateKey: adminUser.PrivateKey,
		User:       adminUser.Username,
		Variables:  pushExtraVars,
	}

	buf := new(bytes.Buffer)
	exec := ansible.CreateExecutor(gateway, playbooks, opts, buf)

	err = exec.Execute(context.Background())
	result := buf.String()
	status := ansible.GetAnsibleOutputResult(result)

	return result, status, err
}

func (uc *PermUsecase) updateTaskAndLog(ctx context.Context, taskID int64, ip, result string, status bool, err error) {
	task, fetchErr := uc.repo.GetAnsibleTaskByID(ctx, taskID)
	if fetchErr != nil {
		uc.log.Errorf("Failed to fetch task %d: %v", taskID, fetchErr)
		return
	}

	task.Log = result
	task.FinishedAt = time.Now()
	if status {
		task.Status = 1
	}

	logData := &AssetAuthorizationLog{
		TaskLog: result,
		TaskId:  taskID,
	}

	if err != nil {
		task.Status = 2
		logData.TaskLog = err.Error()
	}

	updateErr := uc.repo.UpdateAnsibleTask(ctx, task)
	if updateErr != nil {
		uc.log.Errorf("Failed to update task %d: %v", taskID, updateErr)
	}

	updateLogErr := uc.repo.UpdateAssetAuthorizationLog(ctx, logData)
	if updateLogErr != nil {
		uc.log.Errorf("Failed to update authorization log for IP %s: %v", ip, updateLogErr)
	}
}
func (uc *PermUsecase) CreateUserAssetPermission(ctx context.Context, req *permV1.CreateUserAssetPermissionRequest) (string, error) {
	var msg string
	uid := req.GetUid()
	organizationId := req.GetOrganizationId()
	email := req.GetEmail()
	role := req.GetRole()
	creator := req.GetCreator()
	isExpired := req.GetIsExpired()

	// 获取用户公钥
	decryptUserPublicKey := uc.userUsecase.GetUserTermPublicKey(ctx, uid, organizationId, role)

	if decryptUserPublicKey == "" {
		// 新建用户或者用户公钥不存在重新生成
		privateKey, publicKey, err := sshUtil.GenerateSSHKeyPair()
		if err != nil {
			uc.log.WithContext(ctx).Errorf("create user public key, private key, err: %v", err.Error())
			return msg, err
		}
		uc.userUsecase.CreateOrUpdateUserPublicKey(ctx, uid, organizationId, email, role, publicKey, privateKey)
	}

	var expireTime time.Time
	if isExpired {
		validHours := req.ValidHours
		expireTime = time.Now().Add(time.Duration(validHours) * time.Hour)
	}

	// 推送
	for _, ip := range req.IpList {
		go func(ip string) {
			assetInfo, _, err := uc.assetUsecase.GetAssetListByIp(context.Background(), &assetV1.GetAssetListByIpRequest{
				Ip: ip,
			})
			if err != nil || len(assetInfo) == 0 {
				uc.log.Errorf("failed to get asset info: %v", err)
				return
			}
			uc.HandleUserAuthorization(ctx, uid, creator, email, organizationId, role, ip, decryptUserPublicKey, isExpired, expireTime)
			data := &UserAssetPermission{
				OrganizationId: organizationId,
				Name:           strings.Split(email, "@")[0],
				Email:          email,
				Ip:             ip,
				Role:           role,
				Uid:            uid,
				Hostname:       assetInfo[0].Name,
			}
			if isExpired {
				data.ExpireTime = expireTime
			}
			uc.repo.CreateUserAssetPermission(ctx, data)
		}(ip)
	}

	return "success", nil
}
func (uc *PermUsecase) GetUserAssetPermissionList(ctx context.Context, req *permV1.GetUserAssetPermissionListRequest) ([]*UserAssetPermissionList, int64, error) {
	return uc.repo.GetUserAssetPermissionList(ctx, req)
}
