package biz

import (
	"sync"
	"time"

	"github.com/gorilla/websocket"
	"gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/internal/client"
)

const (
	// 允许的最大消息大小
	maxMessageSize = 1024 * 1024

	// 写入超时
	writeWait = 10 * time.Second

	// 读取超时
	pongWait = 60 * time.Second

	// Ping 周期，必须小于 pongWait
	pingPeriod = (pongWait * 9) / 10
	// 最大连接时长
	maxConnectionDuration = 30 * time.Minute

	gptPrompt        = "你是一名拥有20年的 Shell 专家。如果用户输入的内容与linux命令无关, 返回内容无关linux命令; 如果用户输入内容与linux命令相关, 请只返回一行可执行的linux命令, 不要有其他任何内容。"
	gptDefaultPrompt = "你是一名拥有20年的资深运维工程师，你负责保障系统和服务的正常运行。你熟悉各种监控工具，熟悉各种操作系统原理，精通路由交换和网络安全防护规则，你具备各种黑客入侵检测和安全威胁发现及修复的能力，能够高效地处理故障和进行系统优化。你还懂得如何进行数据备份和恢复，以保证数据安全。你现在协助客户在一个线上生产环境排查和解决问题。客户的数据十分重要，请以保障用户数据和服务稳定为第一前提，在尽量保证数据可靠和安全，尽量不影响正在稳定运行的服务的情况下，为客户解答以下问题。"
)

// ReqRequest  websocket 请求参数
type ReqRequest struct {
	Model          string        `json:"model"`
	Messages       []ChatMessage `json:"messages"`
	ModelMethod    string        `json:"modelMethod"`
	ConversationId string        `json:"conversationId"`
}

// ChatRequest 表示聊天请求
type ChatRequest struct {
	Model    string        `json:"model"`
	Messages []ChatMessage `json:"messages"`
	Stream   bool          `json:"stream"`
}

// Message 定义聊天消息
type Message struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

// ChatResponse 定义LLM服务返回的响应
type ChatResponse struct {
	ID      string `json:"id"`
	Created int64  `json:"created"`
	Model   string `json:"model"`
	Object  string `json:"object"`
	Choices []struct {
		Index int `json:"index"`
		Delta struct {
			Content string `json:"content"`
			Role    string `json:"role,omitempty"`
		} `json:"delta"`
		FinishReason string `json:"finish_reason,omitempty"`
	} `json:"choices"`
}

// Handler 处理 WebSocket 连接
type WsHandler struct {
	conn           *websocket.Conn
	llmClient      *client.LLMClient
	sessionMutex   sync.Mutex
	conversationId string
	messages       []ChatMessage
	service        *AiUsecase
	email          string
}

type ChatMessage struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

// 用户数据
type AiModel struct {
	Name     string `json:"name"`
	Object   string `json:"object"`
	Provider string `json:"provider"`
}

type AiChatTitle struct {
	Id             int
	CreatedAt      time.Time
	UpdatedAt      time.Time
	ConversationId string
	Email          string
	Title          string
	Model          string
	ConversateType string
}

type AiChatHistory struct {
	Id             int
	SendAt         time.Time
	CreatedAt      time.Time
	Email          string
	Model          string
	ConversationId string
	ConversateType string
	Content        string
}
