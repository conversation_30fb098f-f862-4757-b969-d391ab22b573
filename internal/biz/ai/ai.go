package biz

import (
	"context"

	"github.com/go-kratos/kratos/v2/log"
	aiV1 "gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/api/ai/v1"
)

type AiRepo interface {
	GetModelList(context.Context) ([]AiModel, error)
	GetUserConversationList(ctx context.Context) ([]AiChatTitle, error)
	GetConversationDetail(ctx context.Context, req *aiV1.GetConversationDetailRequest) ([]AiChatHistory, int64, error)
	UpdateConversationTitle(conversationId, email, model, conversateType string, messaget []ChatMessage) error
	GetConversationHistoryDetail(email, conversationId string) ([]ChatMessage, error)
	CreateConversationHistory(history AiChatHistory) error
}

type AiUsecase struct {
	repo AiRepo
	log  *log.Helper
}

func NewAiUsecase(repo AiRepo, logger log.Logger) *AiUsecase {
	return &AiUsecase{repo: repo, log: log.NewHelper(log.With(logger, "module", "biz/ai"))}
}

func (uc *AiUsecase) GetModelList(ctx context.Context) ([]AiModel, error) {
	return uc.repo.GetModelList(ctx)
}

func (uc *AiUsecase) GetUserConversationList(ctx context.Context) ([]AiChatTitle, error) {
	return uc.repo.GetUserConversationList(ctx)
}

func (uc *AiUsecase) GetConversationDetail(ctx context.Context, req *aiV1.GetConversationDetailRequest) ([]AiChatHistory, int64, error) {
	return uc.repo.GetConversationDetail(ctx, req)
}
