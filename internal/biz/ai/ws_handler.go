package biz

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"time"

	"gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/internal/client"

	"github.com/bytedance/sonic"
	"github.com/gorilla/websocket"
	gonanoid "github.com/matoous/go-nanoid/v2"
)

// NewHandler 创建一个新的处理器
func NewWsHandler(conn *websocket.Conn, llmClient *client.LLMClient, s *AiUsecase, email string) *WsHandler {
	return &WsHandler{
		conn:           conn,
		llmClient:      llmClient,
		conversationId: llmClient.ConversationId,
		messages:       []ChatMessage{},
		service:        s,
		email:          email,
	}
}

// Handle 处理 WebSocket 连接
func (h *WsHandler) Handle() {
	log.Printf("[%s] WebSocket连接建立", h.conversationId)

	// chat 连接超时设置
	ctx, cancel := context.WithTimeout(context.Background(), maxConnectionDuration)
	defer cancel()

	// 设置连接参数
	h.conn.SetReadLimit(maxMessageSize)
	h.conn.SetReadDeadline(time.Now().Add(pongWait))
	h.conn.SetPongHandler(func(string) error {
		h.conn.SetReadDeadline(time.Now().Add(pongWait))
		return nil
	})

	// 启动 ping 协程
	pingCtx, pingCancel := context.WithCancel(ctx)
	go h.ping(pingCtx)

	// 监听连接超时
	timeoutChan := make(chan struct{})
	go func() {
		<-ctx.Done()
		// 只有在非正常关闭时才打印超时日志
		if ctx.Err() == context.DeadlineExceeded {
			log.Printf("[%s] WebSocket连接超过最大时长,即将关闭", h.conversationId)
			close(timeoutChan)
			h.conn.Close()
		}
	}()

	// 处理消息
	for {
		select {
		case <-timeoutChan:
			pingCancel() // 停止ping协程
			return
		default:
			// 设置读取超时
			h.conn.SetReadDeadline(time.Now().Add(pongWait))

			// 读取消息
			messageType, message, err := h.conn.ReadMessage()
			if err != nil {
				// 客户端主动断开
				if websocket.IsCloseError(err, websocket.CloseNormalClosure) {
					log.Printf("[%s] 客户端主动断开连接", h.conversationId)
					cancel()     // 取消超时上下文
					pingCancel() // 停止ping协程
					return
				}
				if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
					log.Printf("[%s] WebSocket读取错误: %v", h.conversationId, err)
				}
				return
			}

			// 处理客户端关闭消息
			if messageType == websocket.CloseMessage {
				log.Printf("[%s] 收到客户端关闭消息", h.conversationId)
				cancel()     // 取消超时上下文
				pingCancel() // 停止ping协程
				return
			}

			// 解析请求
			var request ReqRequest
			if err := json.Unmarshal(message, &request); err != nil {
				log.Printf("[%s] 解析请求失败: %v", h.conversationId, err)
				h.sendError("解析请求失败: " + err.Error())
				continue
			}

			// 处理请求
			h.handleChatRequest(request)
		}
	}
}

// handleChatRequest 处理聊天请求
func (h *WsHandler) handleChatRequest(request ReqRequest) {
	// 参数验证
	if request.Model == "" {
		log.Printf("[%s] 模型参数不能为空", h.conversationId)
		h.sendError("模型参数不能为空")
		return
	}

	if len(request.Messages) == 0 {
		log.Printf("[%s] 消息内容不能为空", h.conversationId)
		h.sendError("消息内容不能为空")
		return
	}

	log.Printf("[%s] 收到聊天请求: 模型=%s, 消息数=%d", h.conversationId, request.Model, len(request.Messages))

	if request.ConversationId != "" && len(request.ConversationId) == 56 {
		h.llmClient.ConversationId = request.ConversationId
		h.conversationId = request.ConversationId
		// 获取历史会话消息
		if messages, err := h.service.repo.GetConversationHistoryDetail(h.email, request.ConversationId); err != nil {
			log.Printf("[%s] 获取历史会话消息失败: %v", h.conversationId, err)
			return
		} else {
			h.messages = messages
		}
	} else {
		id, err := gonanoid.New(56)
		if err != nil {
			log.Printf("生成会话ID失败: %v", err)
			return
		}
		h.llmClient.ConversationId = id
		h.conversationId = id
		h.messages = []ChatMessage{}
		h.conn.SetWriteDeadline(time.Now().Add(writeWait))
		if err := h.conn.WriteMessage(websocket.TextMessage, []byte(fmt.Sprintf("{\"conversation_id\":\"%s\"}", id))); err != nil {
			log.Printf("[%s] 发送响应失败: %v", h.conversationId, err)
			return
		}
	}
	h.service.repo.UpdateConversationTitle(h.conversationId, h.email, request.Model, request.ModelMethod, request.Messages)
	// 新历史数据记录
	newsMessaget := AiChatHistory{
		Email:          h.email,
		ConversationId: h.conversationId,
		Model:          request.Model,
		ConversateType: request.ModelMethod,
	}
	if len(request.Messages) > 0 {
		content, _ := sonic.MarshalString(request.Messages[0])
		newsMessaget.Content = content
		newsMessaget.SendAt = time.Now()
		h.service.repo.CreateConversationHistory(newsMessaget)
	}

	h.sessionMutex.Lock()
	if request.ModelMethod == "ctm-cmd" {
		h.messages = append([]ChatMessage{{Role: "system", Content: gptPrompt}}, h.messages...)
	} else {
		h.messages = append([]ChatMessage{{Role: "system", Content: gptDefaultPrompt}}, h.messages...)
	}
	// 累积新的消息到会话历史
	h.messages = append(h.messages, request.Messages...)
	fullRequest := ChatRequest{
		Model:    request.Model,
		Messages: h.messages,
		Stream:   true,
	}
	h.sessionMutex.Unlock()

	// 调用大模型 API
	responses, err := h.llmClient.Chat(fullRequest)
	if err != nil {
		log.Printf("[%s] 调用大模型 API 失败: %v", h.conversationId, err)
		h.sendError("调用大模型 API 失败: " + err.Error())
		return
	}

	// 存储助手的响应
	var assistantResponse string
	// 发送响应
	for response := range responses {
		h.conn.SetWriteDeadline(time.Now().Add(writeWait))
		if err := h.conn.WriteMessage(websocket.TextMessage, []byte(response)); err != nil {
			log.Printf("[%s] 发送响应失败: %v", h.conversationId, err)
			return
		}
		// 尝试解析 JSON 响应
		var chatResp ChatResponse
		if err := json.Unmarshal([]byte(response), &chatResp); err == nil {
			// 提取 content 部分
			if len(chatResp.Choices) > 0 {
				assistantResponse += chatResp.Choices[0].Delta.Content
			}
		} else {
			log.Printf("[%s] 解析响应失败: %v", h.conversationId, err)
		}
	}

	// 将助手响应添加到会话历史
	content, _ := sonic.MarshalString(ChatMessage{
		Role:    "assistant",
		Content: assistantResponse,
	})
	newsMessaget.Content = content
	h.service.repo.CreateConversationHistory(newsMessaget)
	log.Printf("[%s] 响应:\n%s", h.conversationId, assistantResponse)
}

// ping 定期发送 ping 消息
func (h *WsHandler) ping(ctx context.Context) {
	ticker := time.NewTicker(pingPeriod)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			log.Printf("[%s] 发送Ping消息", h.conversationId)
			h.conn.SetWriteDeadline(time.Now().Add(writeWait))
			if err := h.conn.WriteControl(
				websocket.PingMessage,
				[]byte{},
				time.Now().Add(writeWait),
			); err != nil {
				log.Printf("[%s] 发送Ping消息失败: %v", h.conversationId, err)
				return
			}
		}
	}
}

// sendError 发送错误消息
func (h *WsHandler) sendError(message string) {
	h.conn.SetWriteDeadline(time.Now().Add(writeWait))
	errorMsg := map[string]interface{}{
		"error": message,
		"code":  400, // 可以根据不同错误类型设置不同的错误码
	}
	data, _ := json.Marshal(errorMsg)
	h.conn.WriteMessage(websocket.TextMessage, data)
}
