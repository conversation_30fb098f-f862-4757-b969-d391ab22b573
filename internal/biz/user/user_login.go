package biz

import (
	"context"
	"net/http"
	"strings"

	kratosErrors "github.com/go-kratos/kratos/v2/errors"
	"github.com/jinzhu/copier"
)

// UserLoginPwd 用户个人页面登录
func (uc *UserUsecase) UserLoginPwd(ctx context.Context, req *UserLoginPwdReq) (rsp *UserLoginRsp, err error) {
	// 1.校验验证码
	userLoginPwdCheckRsp, err := uc.repo.UserLoginPwdCheck(ctx, req)
	if err != nil {
		return nil, kratosErrors.New(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), err.Error())
	}
	// 2.生成Token
	generateTokenReq := &UserInfoShort{}
	_ = copier.Copy(&generateTokenReq, userLoginPwdCheckRsp)
	token, err := GenerateToken(generateTokenReq)
	rsp = &UserLoginRsp{
		Token:            token,
		Name:             userLoginPwdCheckRsp.Name,
		Avatar:           userLoginPwdCheckRsp.Avatar,
		Email:            userLoginPwdCheckRsp.Email,
		Uid:              userLoginPwdCheckRsp.Uid,
		RegistrationType: 2,
	}

	return
}

// UserLoginEmailSendCode 用户邮箱验证码发送
func (uc *UserUsecase) UserLoginEmailSendCode(ctx context.Context, req *UserLoginEmailSendCodeReq) (err error) {
	err = uc.repo.UserLoginEmailSendCode(ctx, req)
	if err != nil {
		return kratosErrors.New(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), err.Error())
	}
	return nil
}

// UserLoginEmailVerification 用户个人页面登录
func (uc *UserUsecase) UserLoginEmailVerification(ctx context.Context, req *UserLoginEmailVerificationReq) (rsp *UserLoginRsp, err error) {
	// 1.校验验证码
	err = uc.repo.UserLoginEmailCheck(ctx, req)
	if err != nil {
		err = kratosErrors.New(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), err.Error())
		return
	}
	// 2.用户存在则获取信息，不存在则创建
	upsertUserReq := &User{
		Email:            req.Email,
		RegistrationType: 2,
	}
	upsertUserRsp, err := uc.repo.UpsertUserPersonal(ctx, upsertUserReq)
	if err != nil {
		err = kratosErrors.New(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), err.Error())
		return
	}
	// 3.生成Token
	generateTokenReq := &UserInfoShort{}
	_ = copier.Copy(&generateTokenReq, upsertUserRsp)
	generateTokenReq.RegistrationType = 2
	token, err := GenerateToken(generateTokenReq)
	if err != nil {
		err = kratosErrors.New(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), err.Error())
	}
	rsp = &UserLoginRsp{
		Token:            token,
		Name:             upsertUserRsp.Name,
		Avatar:           upsertUserRsp.Avatar,
		Email:            upsertUserRsp.Email,
		Uid:              upsertUserRsp.Uid,
		RegistrationType: upsertUserRsp.RegistrationType,
	}

	return
}

// UserLoginSSO 用户SSO登录
func (uc *UserUsecase) UserLoginSSO(ctx context.Context, ssoToken, platForm string) (rsp *UserLoginRsp, err error) {
	// token拿取用户信息
	ssoTokenInfo, err := uc.repo.AuthVerifyToken(ssoToken, platForm)
	if err != nil {
		return nil, kratosErrors.New(http.StatusUnauthorized, http.StatusText(http.StatusUnauthorized), err.Error())
	}
	upsertUserReq := &User{}
	_ = copier.Copy(upsertUserReq, ssoTokenInfo)
	departmentTreePathStr := ssoTokenInfo.DepartmentTreePathStr
	if len(departmentTreePathStr) != 0 {
		arr := strings.Split(departmentTreePathStr, "/")
		if len(arr) > 0 {
			upsertUserReq.SecondaryOrganization = arr[0]
			if len(arr) > 1 {
				upsertUserReq.TertiaryOrganization = arr[1]
			} else {
				upsertUserReq.TertiaryOrganization = arr[0]
			}
			upsertUserReq.Team = arr[len(arr)-1]
		}
	}
	// 存用户信息到user模块
	upsertUserReq.RegistrationType = 1
	updateUserRsp, err := uc.repo.UpsertUserSSO(ctx, upsertUserReq)
	if err != nil {
		return nil, err
	}
	if platForm == PlatFormWeb && updateUserRsp.ConsoleStatus == 2 {
		return nil, kratosErrors.New(http.StatusUnauthorized, http.StatusText(http.StatusUnauthorized), "no permission")
	}
	userInfoShort := new(UserInfoShort)
	_ = copier.Copy(userInfoShort, ssoTokenInfo)
	userInfoShort.RegistrationType = 1
	token, err := GenerateToken(userInfoShort)
	if err != nil {
		return nil, kratosErrors.New(http.StatusUnauthorized, http.StatusText(http.StatusUnauthorized), err.Error())
	}

	return &UserLoginRsp{
		Uid:              updateUserRsp.Uid,
		Token:            token,
		Name:             updateUserRsp.Name,
		Avatar:           updateUserRsp.Avatar,
		Email:            updateUserRsp.Email,
		RegistrationType: updateUserRsp.RegistrationType,
	}, nil
}

// UpdateUserPwd 更新密码
func (uc *UserUsecase) UpdateUserPwd(ctx context.Context, req *UpdateUserPwdReq) (err error) {
	if req.Uid == 2000001 {
		return kratosErrors.New(http.StatusUnauthorized, http.StatusText(http.StatusUnauthorized), "no permission to modify")
	}
	err = uc.repo.UpdateUserPwd(ctx, req)
	if err != nil {
		return kratosErrors.New(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), err.Error())
	}
	return nil
}
