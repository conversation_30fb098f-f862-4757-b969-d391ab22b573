package biz

import (
	"github.com/golang-jwt/jwt"
)

var JwtSecret = []byte("jBWWmWdY7ohjnDas")
var PlatFormWeb = "web"
var PlatFormTerminal = "terminal"

type UserInfoShort struct {
	Uid              int64  `json:"uid"`
	Email            string `json:"email"`
	Name             string `json:"name"`
	Avatar           string `json:"avatar"`
	RegistrationType int64  `json:"registrationType"`
}

type MyCustomClaims struct {
	*UserInfoShort `json:"sso"`
	jwt.StandardClaims
}

type UserLoginRsp struct {
	Token            string
	Name             string
	Avatar           string
	Email            string
	Uid              int64
	RegistrationType int64 // 用户类型:1-SSO,2-个人用户
}

type User struct {
	Id                       uint
	CreatedAt                string
	UpdatedAt                string
	Uid                      int64
	Email                    string
	Name                     string
	Username                 string
	Avatar                   string
	EnName                   string
	Mobile                   string
	Status                   int64
	SecondaryOrganization    string
	TertiaryOrganization     string
	Team                     string
	DepartmentName           string
	StraightLineManagerUid   int64
	StraightLineManagerEmail string
	StraightLineManagerName  string
	WeChatWorkId             string
	RegistrationType         int64
	ConsoleStatus            int64
	PlatformId               string // biz层使用
	Ip                       string // biz层使用
	MacAddress               string // biz层使用
	Key                      string
	Subscription             string
	Models                   []string
	LlmGatewayAddr           string
	Expires                  string
	BudgetResetAt            string
	Ratio                    float64 // 支出比例
}

type UserLoginPwdReq struct {
	Username string
	Password string
}

type UserLoginEmailSendCodeReq struct {
	Email string
}

type UserLoginEmailVerificationReq struct {
	Email string
	Code  string
}

type SSOUserInfo struct {
	Uid                      int64  `json:"uid"`
	Email                    string `json:"email"`
	Name                     string `json:"name"`
	Avatar                   string `json:"avatar"`
	EnName                   string `json:"en_name"`
	Mobile                   string `json:"mobile"`
	Status                   int64  `json:"status"`
	OrganizationOid          int64  `json:"organization_oid"`
	DepartmentOid            int64  `json:"department_oid"`
	DepartmentName           string `json:"department_name"`
	DepartmentTreePath       string `json:"department_tree_path"`
	DepartmentTreePathStr    string `json:"department_tree_path_str"`
	StraightLineManagerUid   int64  `json:"straight_line_manager_uid"`
	StraightLineManagerEmail string `json:"straight_line_manager_email"`
	StraightLineManagerName  string `json:"straight_line_manager_name"`
	DottedLineManagerUid     int64  `json:"dotted_line_manager_uid"`
	DottedLineManagerEmail   string `json:"dotted_line_manager_email"`
	DottedLineManagerName    string `json:"dotted_line_manager_name"`
	MentorUid                int64  `json:"mentor_uid"`
	MentorEmail              string `json:"mentor_email"`
	MentorName               string `json:"mentor_name"`
	IsCharge                 int64  `json:"is_charge"` //是否是当前所属部门（DepartmentOid）的负责人
	LeaderUid                int64  `json:"leader_uid"`
	LeaderEmail              string `json:"leader_email"`
	LeaderName               string `json:"leader_name"`
	Organization             string `json:"organization"`
	Department               string `json:"department"`
	PlatformId               string `json:"platform_id"`
	WeChatWorkId             string `json:"we_chat_work_id"`
}

type UserTermConfig struct {
	Id                 uint
	CreatedAt          string
	UpdatedAt          string
	Uid                int64
	User               string
	Email              string
	AuthModel          string
	Password           string
	PrivateKey         string
	PublicKey          string
	Comment            string
	Menu               string
	FontSize           int64
	CursorStyle        string
	ScrollBack         int64
	QuickVimStatus     int64
	CommonVimStatus    int64
	AliasStatus        int64
	AutoCompleteStatus int64
	HighlightStatus    int64
	Language           string
}

type UserQuickCommand struct {
	Id        int64
	Uid       int64
	Command   string
	Comment   string
	Alias     string
	CreatedAt string
	UpdatedAt string
}

type ListUserQuickCommandReq struct {
	Uid        int64
	SearchText string
	PageNo     int32
	PageSize   int32
}

type CheckUserDeviceReq struct {
	Ip         string
	MacAddress string
	Email      string
}

type UpdateUserReq struct {
	Uid      int64
	Name     string
	Username string
	Mobile   string
}

type UpdateUserPwdReq struct {
	Uid      int64
	Password string
}

type UserAccountInfo struct {
	Uid          int64
	Email        string
	Key          string
	Subscription string
}
