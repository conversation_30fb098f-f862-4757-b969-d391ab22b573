package biz

import (
	"context"
	"net/http"
	"time"

	kratosErrors "github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/golang-jwt/jwt"
)

type UserRepo interface {
	GetUserByCtx(context.Context) (*User, error)
	UpdateUser(ctx context.Context, req *UpdateUserReq) (*User, error)
	UserLoginOutAddBlackList(ctx context.Context) (err error)
	IsExistInJwtBlackList(ctx context.Context) (exist bool, err error)
	GetUserTermConfig(ctx context.Context) (rsp *UserTermConfig, err error)
	UpdateUserTermConfig(ctx context.Context, req *UserTermConfig) (err error)
	CheckUserDevice(ctx context.Context, req *CheckUserDeviceReq) (bool, error)
	// 登录-个人
	UserLoginPwdCheck(ctx context.Context, req *UserLoginPwdReq) (*User, error)
	UserLoginEmailSendCode(ctx context.Context, req *UserLoginEmailSendCodeReq) (err error)
	UserLoginEmailCheck(ctx context.Context, req *UserLoginEmailVerificationReq) (err error)
	UpsertUserPersonal(ctx context.Context, req *User) (*User, error)
	UpdateUserPwd(ctx context.Context, req *UpdateUserPwdReq) error
	// 登录-SSO
	UpsertUserSSO(ctx context.Context, req *User) (*User, error)
	AuthVerifyToken(token, platForm string) (*SSOUserInfo, error)
	UserLoginOutSSO(ctx context.Context, platForm string) (err error)
	// 快速命令
	ListUserQuickCommand(ctx context.Context, req *ListUserQuickCommandReq) ([]*UserQuickCommand, int64, error)
	CreateUserQuickCommand(ctx context.Context, req *UserQuickCommand) error
	GetUserQuickCommand(ctx context.Context, id, uid int64) (*UserQuickCommand, error)
	UpdateUserQuickCommand(ctx context.Context, req *UserQuickCommand) error
	DeleteUserQuickCommand(ctx context.Context, id, uid int64) error

	GetUserTermPublicKey(ctx context.Context, uid int64, organization, role string) string
	CreateOrUpdateUserPublicKey(ctx context.Context, uid int64, organization, email, role, publicKey, privateKey string) string
	GetUserInfoByEmail(ctx context.Context, email, organizationId string) ([]User, error)

	FlushUserLlmBaseInfo(user UserAccountInfo) error
}

type UserUsecase struct {
	repo UserRepo
	log  *log.Helper
}

func NewUserUsecase(repo UserRepo, logger log.Logger) *UserUsecase {
	return &UserUsecase{repo: repo, log: log.NewHelper(logger)}
}

// GetUserByCtx 根据上下文获取用户的信息
func (uc *UserUsecase) GetUserByCtx(ctx context.Context) (*User, error) {
	rsp, err := uc.repo.GetUserByCtx(ctx)
	if err != nil {
		err = kratosErrors.New(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), err.Error())
	}

	return rsp, err
}

// UserLoginOut 用户登录出
func (uc *UserUsecase) UserLoginOut(ctx context.Context, platForm string) (err error) {
	err = uc.repo.UserLoginOutSSO(ctx, platForm)
	if err != nil {
		return kratosErrors.New(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), err.Error())
	}

	err = uc.repo.UserLoginOutAddBlackList(ctx)
	if err != nil {
		return kratosErrors.New(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), err.Error())
	}

	return nil
}

// IsExistInJwtBlackList 查询token是否存在于黑名单中
func (uc *UserUsecase) IsExistInJwtBlackList(ctx context.Context) (exist bool, err error) {
	exist, err = uc.repo.IsExistInJwtBlackList(ctx)
	if err != nil {
		err = kratosErrors.New(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), err.Error())
	}
	return exist, err
}

// GenerateToken 生成Token
func GenerateToken(userInfoShort *UserInfoShort) (string, error) {
	nowTime := time.Now()
	expireTime := nowTime.AddDate(0, 0, 1)
	claims := MyCustomClaims{
		userInfoShort,
		jwt.StandardClaims{
			ExpiresAt: expireTime.Unix(),
		},
	}
	token, err := jwt.NewWithClaims(jwt.SigningMethodHS256, claims).SignedString(JwtSecret)
	if err != nil {
		return "", kratosErrors.New(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), err.Error())
	}

	return token, nil
}

// GetUserTermConfig 获取用户配置
func (uc *UserUsecase) GetUserTermConfig(ctx context.Context) (rsp *UserTermConfig, err error) {
	rsp, err = uc.repo.GetUserTermConfig(ctx)
	if err != nil {
		err = kratosErrors.New(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), err.Error())
	}
	return rsp, err
}

// UpdateUserTermConfig 更新用户配置
func (uc *UserUsecase) UpdateUserTermConfig(ctx context.Context, req *UserTermConfig) (err error) {
	err = uc.repo.UpdateUserTermConfig(ctx, req)
	if err != nil {
		err = kratosErrors.New(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), err.Error())
	}
	return err
}

// 快速命令
// ListUserQuickCommand 查
func (uc *UserUsecase) ListUserQuickCommand(ctx context.Context, req *ListUserQuickCommandReq) ([]*UserQuickCommand, int64, error) {
	userQuickCommands, total, err := uc.repo.ListUserQuickCommand(ctx, req)
	if err != nil {
		err = kratosErrors.New(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), err.Error())
	}
	return userQuickCommands, total, err
}

// CreateUserQuickCommand 创建
func (uc *UserUsecase) CreateUserQuickCommand(ctx context.Context, req *UserQuickCommand) error {
	err := uc.repo.CreateUserQuickCommand(ctx, req)
	if err != nil {
		err = kratosErrors.New(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), err.Error())
	}
	return err
}

// GetUserQuickCommand 详情
func (uc *UserUsecase) GetUserQuickCommand(ctx context.Context, id, uid int64) (*UserQuickCommand, error) {
	userQuickCommand, err := uc.repo.GetUserQuickCommand(ctx, id, uid)
	if err != nil {
		err = kratosErrors.New(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), err.Error())
	}
	return userQuickCommand, err
}

// UpdateUserQuickCommand 更新
func (uc *UserUsecase) UpdateUserQuickCommand(ctx context.Context, req *UserQuickCommand) error {
	err := uc.repo.UpdateUserQuickCommand(ctx, req)
	if err != nil {
		err = kratosErrors.New(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), err.Error())
	}
	return err
}

// DeleteUserQuickCommand 删除
func (uc *UserUsecase) DeleteUserQuickCommand(ctx context.Context, id, uid int64) error {
	err := uc.repo.DeleteUserQuickCommand(ctx, id, uid)
	if err != nil {
		err = kratosErrors.New(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), err.Error())
	}
	return err
}

// CheckUserDevice 检查是否为公司设备
func (uc *UserUsecase) CheckUserDevice(ctx context.Context, req *CheckUserDeviceReq) (bool, error) {
	isOfficeEquipment, err := uc.repo.CheckUserDevice(ctx, req)
	if err != nil {
		err = kratosErrors.New(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), err.Error())
	}
	return isOfficeEquipment, err
}

func (uc *UserUsecase) GetUserTermPublicKey(ctx context.Context, uid int64, organization, role string) string {
	uc.log.WithContext(ctx).Infof("getUserTermPublicKey: %v", uid, organization, role)
	return uc.repo.GetUserTermPublicKey(ctx, uid, organization, role)
}

func (uc *UserUsecase) CreateOrUpdateUserPublicKey(ctx context.Context, uid int64, organizationId, email, role, publicKey, privateKey string) string {
	uc.log.WithContext(ctx).Infof("CreateOrUpdateUserPublicKey: %v", organizationId, email, role)
	return uc.repo.CreateOrUpdateUserPublicKey(ctx, uid, organizationId, email, role, publicKey, privateKey)
}

func (uc *UserUsecase) GetUserInfoByEmail(ctx context.Context, email, organizationId string) ([]User, error) {
	uc.log.WithContext(ctx).Infof("GetUserInfoByEmail: %v", email, organizationId)
	return uc.repo.GetUserInfoByEmail(ctx, email, organizationId)
}

// UpdateUser 更新用户信息
func (uc *UserUsecase) UpdateUser(ctx context.Context, req *UpdateUserReq) (user *User, err error) {
	user, err = uc.repo.UpdateUser(ctx, req)
	if err != nil {
		return nil, kratosErrors.New(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), err.Error())
	}
	return user, nil
}

// BatchFlushUserInfo 批量刷新用户信息
func (uc *UserUsecase) BatchFlushUserInfo(ctx context.Context) error {
	err := uc.repo.FlushUserLlmBaseInfo(UserAccountInfo{
		Uid:          0, // 0表示刷新所有用户
	})
	if err != nil {
		return kratosErrors.New(http.StatusInternalServerError, http.StatusText(http.StatusInternalServerError), err.Error())
	}
	return nil
}
