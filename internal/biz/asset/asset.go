package biz

import (
	"context"
	"encoding/json"
	"errors"
	"strconv"
	"strings"

	"github.com/elliotchance/pie/pie"
	"github.com/go-kratos/kratos/v2/log"
	assetV1 "gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/api/asset/v1"
	pkg "gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/pkg/util/common"
)

type AssetRepo interface {
	GetAssetList(ctx context.Context, req *assetV1.GetAssetListRequest) ([]Asset, int64, error)
	CreateAsset(ctx context.Context, req *assetV1.CreateAssetRequest) error
	UpdateAsset(ctx context.Context, req *assetV1.UpdateAseetRequest) error
	DeleteAsset(ctx context.Context, req *assetV1.DeleteAssetRequest) error
	UserAssetFavorite(ctx context.Context, req *assetV1.UserAssetFavoriteRequest) error
	ListUserWorkSpace(ctx context.Context) ([]UserSpace, error)

	GetAssetAlias(ctx context.Context, ssoId int64) (map[string]string, error)
	GetTop5Asset(ctx context.Context, ssoId int64, originationId string) ([]*assetV1.ListAssetRouteReply_Children, error)
	GetFavoriteAsset(ctx context.Context, ssoId int64, originationId string) ([]*assetV1.ListAssetRouteReply_Children, pie.Strings, error)
	GetUserTermMenu(ctx context.Context, ssoId int64) (string, error)
	GetUserIdcAsset(ctx context.Context, ssoId int64, originationId string) ([]UserAsset, error)
	CheckAssetPermExpireStatus(ctx context.Context, ssoId int64, ip string) (bool, error)

	GetAssetProxyGateway(ctx context.Context, ip, originationId string) (AssetProxyGateway, error)
	GetAssetAdminUserByOrganizationId(ctx context.Context, organizationId string) (AssetAdminUser, error)
	UserAssetAlias(ctx context.Context, req *assetV1.UserAssetAliasRequest) error

	GetAssetListByIp(ctx context.Context, req *assetV1.GetAssetListByIpRequest) ([]Asset, int64, error)
	GetAssetProxyGatewaySelect(ctx context.Context) ([]AssetProxyGateway, error)
	GetAssetProxyGatewayList(ctx context.Context, req *assetV1.GetAssetProxyGatewayListRequest) ([]AssetProxyGateway, int64, error)
	CreateAssetProxyGateway(ctx context.Context, req *assetV1.CreateAssetProxyGatewayRequest) error
	UpdateAssetProxyGateway(ctx context.Context, req *assetV1.UpdateAssetProxyGatewayRequest) error
	DeleteAssetProxyGateway(ctx context.Context, req *assetV1.DeleteAssetProxyGatewayRequest) error

	GetAssetAdminUserList(ctx context.Context, req *assetV1.GetAssetAdminUserListRequest) ([]AssetAdminUser, int64, error)
	CreateAssetAdminUser(ctx context.Context, req *assetV1.CreateAssetAdminUserRequest) error
	UpdateAssetAdminUser(ctx context.Context, req *assetV1.UpdateAssetAdminUserRequest) error
	DeleteAssetAdminUser(ctx context.Context, req *assetV1.DeleteAssetAdminUserRequest) error
}

type AssetUsecase struct {
	repo AssetRepo
	log  *log.Helper
}

func NewAssetUsecase(repo AssetRepo, logger log.Logger) *AssetUsecase {
	return &AssetUsecase{repo: repo, log: log.NewHelper(log.With(logger, "module", "biz/asset"))}
}

func (uc *AssetUsecase) GetAssetList(ctx context.Context, req *assetV1.GetAssetListRequest) ([]Asset, int64, error) {
	return uc.repo.GetAssetList(ctx, req)
}

func (uc *AssetUsecase) CreateAsset(ctx context.Context, req *assetV1.CreateAssetRequest) error {
	return uc.repo.CreateAsset(ctx, req)
}

func (uc *AssetUsecase) DeleteAsset(ctx context.Context, req *assetV1.DeleteAssetRequest) error {
	return uc.repo.DeleteAsset(ctx, req)
}

func (uc *AssetUsecase) UpdateAsset(ctx context.Context, req *assetV1.UpdateAseetRequest) error {
	return uc.repo.UpdateAsset(ctx, req)
}

func (uc *AssetUsecase) UserAssetAlias(ctx context.Context, req *assetV1.UserAssetAliasRequest) error {
	return uc.repo.UserAssetAlias(ctx, req)
}

func (uc *AssetUsecase) UserAssetFavorite(ctx context.Context, req *assetV1.UserAssetFavoriteRequest) error {
	return uc.repo.UserAssetFavorite(ctx, req)
}

func (uc *AssetUsecase) ListUserWorkSpace(ctx context.Context) ([]UserSpace, error) {
	return uc.repo.ListUserWorkSpace(ctx)
}

func (uc *AssetUsecase) ListAssetRoute(ctx context.Context, originationId string) ([]*assetV1.ListAssetRouteReply_Route, error) {
	userInfo, err := pkg.GetUserInfo(ctx)
	if err != nil {
		return nil, err
	}
	var outs = []*assetV1.ListAssetRouteReply_Route{}
	ssoId := int64(userInfo.Uid)
	// 获取最近top5的资产
	outTop, err := uc.repo.GetTop5Asset(ctx, ssoId, originationId)
	if err != nil {
		return nil, err
	}
	if len(outTop) > 0 {
		outs = append(outs, &assetV1.ListAssetRouteReply_Route{
			Key:      "common",
			Title:    "最近常用top5",
			Children: outTop,
		})
	}
	// 用户收藏资产
	outFavorite, favoriteAsset, err := uc.repo.GetFavoriteAsset(ctx, ssoId, originationId)
	if err != nil {
		return nil, err
	}
	if len(outFavorite) > 0 {
		outs = append(outs, &assetV1.ListAssetRouteReply_Route{
			Key:      "favorite",
			Title:    "收藏栏",
			Children: outFavorite,
		})
	}
	type MenuData struct {
		Name   string `json:"name"`
		IpList string `json:"ipList"`
	}
	menu, err := uc.repo.GetUserTermMenu(ctx, ssoId)
	// 用户自定义栏
	if menu != "" {
		menuList := strings.Split(menu, "|")
		for i, menuStr := range menuList {
			var menuData MenuData
			err := json.Unmarshal([]byte(menuStr), &menuData)
			if err != nil {
				return nil, errors.New("list menu error: " + err.Error())
			}
			r := new(assetV1.ListAssetRouteReply_Route)
			r.Key = menuData.Name
			r.Title = menuData.Name
			ipList := strings.Split(menuData.IpList, ",")
			for _, ipInfo := range ipList {
				ip := strings.Split(ipInfo, "_")[1]
				expireStatus, err := uc.repo.CheckAssetPermExpireStatus(ctx, ssoId, ip)
				if expireStatus || err != nil {
					continue
				}
				child := new(assetV1.ListAssetRouteReply_Children)
				child.Key = ipInfo + "_tag-" + strconv.Itoa(i)
				child.Title = ip
				child.Favorite = favoriteAsset.Contains(ip)
				r.Children = append(r.Children, child)
			}
			if len(r.Children) >= 1 {
				outs = append(outs, r)
			}
		}
	}

	// 各个机房的资产
	outUserIdcs, err := uc.repo.GetUserIdcAsset(ctx, ssoId, originationId)
	if err != nil {
		return nil, err
	}

	for _, v := range outUserIdcs {
		ips := strings.Split(v.Ips, ",")
		var childrens []*assetV1.ListAssetRouteReply_Children
		for _, ip := range ips {
			childrens = append(childrens, &assetV1.ListAssetRouteReply_Children{
				Key:            v.Idc + "_" + ip,
				Title:          ip,
				Favorite:       favoriteAsset.Contains(ip),
				Ip:             ip,
				OrganizationId: originationId,
			})
		}
		outs = append(outs, &assetV1.ListAssetRouteReply_Route{
			Key:      v.Idc,
			Title:    v.Idc,
			Children: childrens,
		})
	}
	//获取用户资产别名
	outAlias, err := uc.repo.GetAssetAlias(ctx, ssoId)
	if err != nil {
		return nil, err
	}
	for index, out := range outs {
		for indexj, children := range out.Children {
			if alias, ok := outAlias[children.Title]; ok {
				outs[index].Children[indexj].Title = alias
			}
		}
	}

	return outs, nil
}

func (uc *AssetUsecase) GetAssetProxyGateway(ctx context.Context, ip, organizationId string) (AssetProxyGateway, error) {
	assetProxyGateway, err := uc.repo.GetAssetProxyGateway(ctx, ip, organizationId)
	return assetProxyGateway, err
}

func (uc *AssetUsecase) GetAssetAdminUserByOrganizationId(ctx context.Context, organizationId string) (AssetAdminUser, error) {
	adminUser, err := uc.repo.GetAssetAdminUserByOrganizationId(ctx, organizationId)
	return adminUser, err
}

func (uc *AssetUsecase) GetAssetListByIp(ctx context.Context, req *assetV1.GetAssetListByIpRequest) ([]Asset, int64, error) {
	return uc.repo.GetAssetListByIp(ctx, req)
}

// 代理

func (uc *AssetUsecase) GetAssetProxyGatewaySelect(ctx context.Context) ([]AssetProxyGateway, error) {
	return uc.repo.GetAssetProxyGatewaySelect(ctx)
}

func (uc *AssetUsecase) GetAssetProxyGatewayList(ctx context.Context, req *assetV1.GetAssetProxyGatewayListRequest) ([]AssetProxyGateway, int64, error) {
	return uc.repo.GetAssetProxyGatewayList(ctx, req)
}

func (uc *AssetUsecase) CreateAssetProxyGateway(ctx context.Context, req *assetV1.CreateAssetProxyGatewayRequest) error {
	return uc.repo.CreateAssetProxyGateway(ctx, req)
}

func (uc *AssetUsecase) DeleteAssetProxyGateway(ctx context.Context, req *assetV1.DeleteAssetProxyGatewayRequest) error {
	return uc.repo.DeleteAssetProxyGateway(ctx, req)
}

func (uc *AssetUsecase) UpdateAssetProxyGateway(ctx context.Context, req *assetV1.UpdateAssetProxyGatewayRequest) error {
	return uc.repo.UpdateAssetProxyGateway(ctx, req)
}

// 管理员账户
func (uc *AssetUsecase) GetAssetAdminUserList(ctx context.Context, req *assetV1.GetAssetAdminUserListRequest) ([]AssetAdminUser, int64, error) {
	return uc.repo.GetAssetAdminUserList(ctx, req)
}

func (uc *AssetUsecase) CreateAssetAdminUser(ctx context.Context, req *assetV1.CreateAssetAdminUserRequest) error {
	return uc.repo.CreateAssetAdminUser(ctx, req)
}

func (uc *AssetUsecase) DeleteAssetAdminUser(ctx context.Context, req *assetV1.DeleteAssetAdminUserRequest) error {
	return uc.repo.DeleteAssetAdminUser(ctx, req)
}

func (uc *AssetUsecase) UpdateAssetAdminUser(ctx context.Context, req *assetV1.UpdateAssetAdminUserRequest) error {
	return uc.repo.UpdateAssetAdminUser(ctx, req)
}
