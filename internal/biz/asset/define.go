package biz

import (
	"time"
)

type Asset struct {
	Id             int
	Uuid           string
	CreatedAt      time.Time
	UpdatedAt      time.Time
	Name           string
	Idc            string
	AssetIp        string
	Active         string
	Platform       string
	Protocol       string
	AssetSource    string
	Os             string
	OrganizationId string
	Comment        string
	CreateUser     string
	ProxyGatewayId int
}

type AssetProxyGateway struct {
	Id             int
	CreatedAt      time.Time
	UpdatedAt      time.Time
	OrganizationId string
	Name           string
	Ip             string
	Port           int64
	Idc            string
	Username       string
	Password       string
	PrivateKey     string
	Comment        string
}

type AssetAdminUser struct {
	Id             int
	CreatedAt      time.Time
	UpdatedAt      time.Time
	OrganizationId string
	Username       string
	Password       string
	PrivateKey     string
	Comment        string
}

type UserAsset struct {
	Idc string
	Ips string
}
type AssetConnectCount struct {
	Ip     string `gorm:"column:ip"`
	ConnectCount int    `gorm:"column:connect_count"`
}

type UserSpace struct {
	Key   string
	Label string
}
