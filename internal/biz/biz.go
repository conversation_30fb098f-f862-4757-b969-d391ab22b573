package biz

import (
	"github.com/google/wire"
	aiBiz "gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/internal/biz/ai"
	assetBiz "gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/internal/biz/asset"
	permBiz "gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/internal/biz/perm"
	userBiz "gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/internal/biz/user"
)

// ProviderSet is biz providers.
var ProviderSet = wire.NewSet(userBiz.NewUserUsecase, aiBiz.NewAiUsecase, assetBiz.NewAssetUsecase, permBiz.NewPermUsecase)
