// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
	biz2 "gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/internal/biz/ai"
	biz3 "gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/internal/biz/asset"
	biz4 "gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/internal/biz/perm"
	"gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/internal/biz/user"
	"gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/internal/client"
	"gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/internal/conf"
	"gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/internal/data"
	"gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/internal/server"
	service2 "gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/internal/service/ai"
	service3 "gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/internal/service/asset"
	service4 "gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/internal/service/perm"
	"gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/internal/service/user"
)

import (
	_ "go.uber.org/automaxprocs"
)

// Injectors from wire.go:

// wireApp init kratos application.
func wireApp(confServer *conf.Server, confData *conf.Data, logger log.Logger, sso *conf.Sso, otherInfo *conf.OtherInfo, emailConfig *conf.EmailConfig, wecomConfig *conf.WecomConfig) (*kratos.App, func(), error) {
	db := data.NewDB(confData)
	redisClient := data.NewRedis(confData)
	bootstrap := newBootstrap(confServer, confData, sso, otherInfo, emailConfig, wecomConfig)
	dataData, cleanup, err := data.NewData(db, redisClient, bootstrap, logger)
	if err != nil {
		return nil, nil, err
	}
	clientClient := client.NewClient()
	userRepo := data.NewUserRepo(dataData, logger, sso, clientClient, otherInfo, emailConfig, wecomConfig)
	userUsecase := biz.NewUserUsecase(userRepo, logger)
	userService := service.NewUserService(userUsecase, logger)
	aiRepo := data.NewAiRepo(dataData, logger)
	aiUsecase := biz2.NewAiUsecase(aiRepo, logger)
	aiService := service2.NewAiService(aiUsecase, logger)
	assetRepo := data.NewAssetRepo(dataData, logger, otherInfo)
	assetUsecase := biz3.NewAssetUsecase(assetRepo, logger)
	assetService := service3.NewAssetService(assetUsecase, logger)
	permRepo := data.NewPermRepo(dataData, logger)
	permUsecase := biz4.NewPermUsecase(permRepo, userUsecase, assetUsecase, logger)
	permService := service4.NewPermService(permUsecase, logger)
	httpServer := server.NewHTTPServer(confServer, userService, aiService, assetService, permService, logger, sso, otherInfo)
	app := newApp(logger, httpServer)
	return app, func() {
		cleanup()
	}, nil
}

// wire.go:

// newBootstrap creates a Bootstrap configuration from individual components
func newBootstrap(server2 *conf.Server, data2 *conf.Data, sso *conf.Sso, otherInfo *conf.OtherInfo, emailConfig *conf.EmailConfig, wecomConfig *conf.WecomConfig) *conf.Bootstrap {
	return &conf.Bootstrap{
		Server:      server2,
		Data:        data2,
		Sso:         sso,
		OtherInfo:   otherInfo,
		EmailConfig: emailConfig,
		WecomConfig: wecomConfig,
	}
}
