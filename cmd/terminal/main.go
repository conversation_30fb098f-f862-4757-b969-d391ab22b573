package main

import (
	"flag"
	"fmt"
	"os"

	"gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/internal/conf"

	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware/tracing"
	"github.com/go-kratos/kratos/v2/transport/http"
	ml "gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/pkg/middleware/log"
	cm "gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/pkg/util/common"

	_ "go.uber.org/automaxprocs"
)

// go build -ldflags "-X main.Version=x.y.z"
var (
	// Name is the name of the compiled software.
	Name string
	// Version is the version of the compiled software.
	Version string
	// flagconf is the config flag.

	id, _ = os.Hostname()
)

func newApp(logger log.Logger, hs *http.Server) *kratos.App {
	return kratos.New(
		kratos.ID(id),
		kratos.Name(Name),
		kratos.Version(Version),
		kratos.Metadata(map[string]string{}),
		kratos.Logger(logger),
		kratos.Server(
			hs,
		),
	)
}

func main() {
	flag.Parse()
	zaplogger := ml.NewLoger()
	logger := log.With(zaplogger,
		"ts", log.DefaultTimestamp,
		"caller", log.DefaultCaller,
		"service.id", id,
		"service.name", Name,
		"service.version", Version,
		"trace.id", tracing.TraceID(),
		"span.id", tracing.SpanID(),
	)

	var bc conf.Bootstrap
	if err := cm.ConfigInit(&bc); err != nil {
		panic(err)
	}
	fmt.Println(bc.OtherInfo)

	app, cleanup, err := wireApp(bc.Server, bc.Data, logger, bc.Sso, bc.OtherInfo, bc.EmailConfig, bc.WecomConfig)
	if err != nil {
		panic(err)
	}
	defer cleanup()

	// start and wait for stop signal
	if err := app.Run(); err != nil {
		panic(err)
	}
}
