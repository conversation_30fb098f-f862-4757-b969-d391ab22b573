//go:build wireinject
// +build wireinject

// The build tag makes sure the stub is not built in the final build.

package main

import (
	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/wire"
	"gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/internal/biz"
	"gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/internal/client"
	"gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/internal/conf"
	"gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/internal/data"
	"gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/internal/server"
	"gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/internal/service"
)

// newBootstrap creates a Bootstrap configuration from individual components
func newBootstrap(server *conf.Server, data *conf.Data, sso *conf.Sso, otherInfo *conf.OtherInfo, emailConfig *conf.EmailConfig, wecomConfig *conf.WecomConfig) *conf.Bootstrap {
	return &conf.Bootstrap{
		Server:      server,
		Data:        data,
		Sso:         sso,
		OtherInfo:   otherInfo,
		EmailConfig: emailConfig,
		WecomConfig: wecomConfig,
	}
}

// wireApp init kratos application.
func wireApp(*conf.Server, *conf.Data, log.Logger, *conf.Sso, *conf.OtherInfo, *conf.EmailConfig, *conf.WecomConfig) (*kratos.App, func(), error) {
	panic(wire.Build(server.ProviderSet, data.ProviderSet, biz.ProviderSet, service.ProviderSet, client.ProviderSet, newBootstrap, newApp))
}
