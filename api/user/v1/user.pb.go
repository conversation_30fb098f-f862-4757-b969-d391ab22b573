// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: user/v1/user.proto

package v1

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type HelloRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HelloRequest) Reset() {
	*x = HelloRequest{}
	mi := &file_user_v1_user_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HelloRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HelloRequest) ProtoMessage() {}

func (x *HelloRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HelloRequest.ProtoReflect.Descriptor instead.
func (*HelloRequest) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{0}
}

func (x *HelloRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type HelloReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Message       string                 `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HelloReply) Reset() {
	*x = HelloReply{}
	mi := &file_user_v1_user_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HelloReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HelloReply) ProtoMessage() {}

func (x *HelloReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HelloReply.ProtoReflect.Descriptor instead.
func (*HelloReply) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{1}
}

func (x *HelloReply) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type EmptyRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EmptyRequest) Reset() {
	*x = EmptyRequest{}
	mi := &file_user_v1_user_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EmptyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmptyRequest) ProtoMessage() {}

func (x *EmptyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmptyRequest.ProtoReflect.Descriptor instead.
func (*EmptyRequest) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{2}
}

type CommonReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CommonReply) Reset() {
	*x = CommonReply{}
	mi := &file_user_v1_user_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CommonReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommonReply) ProtoMessage() {}

func (x *CommonReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommonReply.ProtoReflect.Descriptor instead.
func (*CommonReply) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{3}
}

func (x *CommonReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CommonReply) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type UserLoginPwdRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Username      string                 `protobuf:"bytes,1,opt,name=username,proto3" json:"username,omitempty"`
	Password      string                 `protobuf:"bytes,2,opt,name=password,proto3" json:"password,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserLoginPwdRequest) Reset() {
	*x = UserLoginPwdRequest{}
	mi := &file_user_v1_user_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserLoginPwdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserLoginPwdRequest) ProtoMessage() {}

func (x *UserLoginPwdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserLoginPwdRequest.ProtoReflect.Descriptor instead.
func (*UserLoginPwdRequest) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{4}
}

func (x *UserLoginPwdRequest) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *UserLoginPwdRequest) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

type UserLoginReply struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	Token            string                 `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`
	Name             string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Avatar           string                 `protobuf:"bytes,3,opt,name=avatar,proto3" json:"avatar,omitempty"`
	Email            string                 `protobuf:"bytes,4,opt,name=email,proto3" json:"email,omitempty"`
	Uid              int64                  `protobuf:"varint,5,opt,name=uid,proto3" json:"uid,omitempty"`
	RegistrationType int64                  `protobuf:"varint,6,opt,name=registrationType,proto3" json:"registrationType,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *UserLoginReply) Reset() {
	*x = UserLoginReply{}
	mi := &file_user_v1_user_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserLoginReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserLoginReply) ProtoMessage() {}

func (x *UserLoginReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserLoginReply.ProtoReflect.Descriptor instead.
func (*UserLoginReply) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{5}
}

func (x *UserLoginReply) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *UserLoginReply) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UserLoginReply) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *UserLoginReply) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *UserLoginReply) GetUid() int64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

func (x *UserLoginReply) GetRegistrationType() int64 {
	if x != nil {
		return x.RegistrationType
	}
	return 0
}

type UserLoginEmailVerificationRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Email         string                 `protobuf:"bytes,1,opt,name=email,proto3" json:"email,omitempty"`
	Code          string                 `protobuf:"bytes,2,opt,name=code,proto3" json:"code,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserLoginEmailVerificationRequest) Reset() {
	*x = UserLoginEmailVerificationRequest{}
	mi := &file_user_v1_user_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserLoginEmailVerificationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserLoginEmailVerificationRequest) ProtoMessage() {}

func (x *UserLoginEmailVerificationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserLoginEmailVerificationRequest.ProtoReflect.Descriptor instead.
func (*UserLoginEmailVerificationRequest) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{6}
}

func (x *UserLoginEmailVerificationRequest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *UserLoginEmailVerificationRequest) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

type UserLoginEmailSendCodeRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Email         string                 `protobuf:"bytes,1,opt,name=email,proto3" json:"email,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserLoginEmailSendCodeRequest) Reset() {
	*x = UserLoginEmailSendCodeRequest{}
	mi := &file_user_v1_user_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserLoginEmailSendCodeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserLoginEmailSendCodeRequest) ProtoMessage() {}

func (x *UserLoginEmailSendCodeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserLoginEmailSendCodeRequest.ProtoReflect.Descriptor instead.
func (*UserLoginEmailSendCodeRequest) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{7}
}

func (x *UserLoginEmailSendCodeRequest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

type GetUserByCtxReply struct {
	state                 protoimpl.MessageState `protogen:"open.v1"`
	Uid                   int64                  `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Email                 string                 `protobuf:"bytes,2,opt,name=email,proto3" json:"email,omitempty"`
	Name                  string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Avatar                string                 `protobuf:"bytes,4,opt,name=avatar,proto3" json:"avatar,omitempty"`
	EnName                string                 `protobuf:"bytes,5,opt,name=enName,proto3" json:"enName,omitempty"`
	Mobile                string                 `protobuf:"bytes,6,opt,name=mobile,proto3" json:"mobile,omitempty"`
	SecondaryOrganization string                 `protobuf:"bytes,7,opt,name=secondaryOrganization,proto3" json:"secondaryOrganization,omitempty"`
	TertiaryOrganization  string                 `protobuf:"bytes,8,opt,name=tertiaryOrganization,proto3" json:"tertiaryOrganization,omitempty"`
	Team                  string                 `protobuf:"bytes,9,opt,name=team,proto3" json:"team,omitempty"`
	RegistrationType      int64                  `protobuf:"varint,10,opt,name=registrationType,proto3" json:"registrationType,omitempty"`
	Ip                    string                 `protobuf:"bytes,11,opt,name=ip,proto3" json:"ip,omitempty"`
	MacAddress            string                 `protobuf:"bytes,12,opt,name=macAddress,proto3" json:"macAddress,omitempty"`
	Username              string                 `protobuf:"bytes,13,opt,name=username,proto3" json:"username,omitempty"`
	Key                   string                 `protobuf:"bytes,14,opt,name=key,proto3" json:"key,omitempty"`
	Subscription          string                 `protobuf:"bytes,15,opt,name=subscription,proto3" json:"subscription,omitempty"`
	Models                []string               `protobuf:"bytes,16,rep,name=models,proto3" json:"models,omitempty"`
	LlmGatewayAddr        string                 `protobuf:"bytes,17,opt,name=llmGatewayAddr,proto3" json:"llmGatewayAddr,omitempty"`
	Expires               string                 `protobuf:"bytes,18,opt,name=expires,proto3" json:"expires,omitempty"`
	Ratio                 float32                `protobuf:"fixed32,19,opt,name=ratio,proto3" json:"ratio,omitempty"`
	BudgetResetAt         string                 `protobuf:"bytes,20,opt,name=budgetResetAt,proto3" json:"budgetResetAt,omitempty"`
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *GetUserByCtxReply) Reset() {
	*x = GetUserByCtxReply{}
	mi := &file_user_v1_user_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserByCtxReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserByCtxReply) ProtoMessage() {}

func (x *GetUserByCtxReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserByCtxReply.ProtoReflect.Descriptor instead.
func (*GetUserByCtxReply) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{8}
}

func (x *GetUserByCtxReply) GetUid() int64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

func (x *GetUserByCtxReply) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *GetUserByCtxReply) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GetUserByCtxReply) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *GetUserByCtxReply) GetEnName() string {
	if x != nil {
		return x.EnName
	}
	return ""
}

func (x *GetUserByCtxReply) GetMobile() string {
	if x != nil {
		return x.Mobile
	}
	return ""
}

func (x *GetUserByCtxReply) GetSecondaryOrganization() string {
	if x != nil {
		return x.SecondaryOrganization
	}
	return ""
}

func (x *GetUserByCtxReply) GetTertiaryOrganization() string {
	if x != nil {
		return x.TertiaryOrganization
	}
	return ""
}

func (x *GetUserByCtxReply) GetTeam() string {
	if x != nil {
		return x.Team
	}
	return ""
}

func (x *GetUserByCtxReply) GetRegistrationType() int64 {
	if x != nil {
		return x.RegistrationType
	}
	return 0
}

func (x *GetUserByCtxReply) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *GetUserByCtxReply) GetMacAddress() string {
	if x != nil {
		return x.MacAddress
	}
	return ""
}

func (x *GetUserByCtxReply) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *GetUserByCtxReply) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *GetUserByCtxReply) GetSubscription() string {
	if x != nil {
		return x.Subscription
	}
	return ""
}

func (x *GetUserByCtxReply) GetModels() []string {
	if x != nil {
		return x.Models
	}
	return nil
}

func (x *GetUserByCtxReply) GetLlmGatewayAddr() string {
	if x != nil {
		return x.LlmGatewayAddr
	}
	return ""
}

func (x *GetUserByCtxReply) GetExpires() string {
	if x != nil {
		return x.Expires
	}
	return ""
}

func (x *GetUserByCtxReply) GetRatio() float32 {
	if x != nil {
		return x.Ratio
	}
	return 0
}

func (x *GetUserByCtxReply) GetBudgetResetAt() string {
	if x != nil {
		return x.BudgetResetAt
	}
	return ""
}

type GetUserTermConfigReply struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	Id                 int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Uid                int64                  `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	FontSize           int64                  `protobuf:"varint,3,opt,name=fontSize,proto3" json:"fontSize,omitempty"`
	CursorStyle        string                 `protobuf:"bytes,4,opt,name=cursorStyle,proto3" json:"cursorStyle,omitempty"`
	ScrollBack         int64                  `protobuf:"varint,5,opt,name=scrollBack,proto3" json:"scrollBack,omitempty"`
	Language           string                 `protobuf:"bytes,6,opt,name=language,proto3" json:"language,omitempty"`
	AliasStatus        int64                  `protobuf:"varint,7,opt,name=aliasStatus,proto3" json:"aliasStatus,omitempty"`
	AutoCompleteStatus int64                  `protobuf:"varint,8,opt,name=autoCompleteStatus,proto3" json:"autoCompleteStatus,omitempty"`
	QuickVimStatus     int64                  `protobuf:"varint,9,opt,name=quickVimStatus,proto3" json:"quickVimStatus,omitempty"`
	CommonVimStatus    int64                  `protobuf:"varint,10,opt,name=commonVimStatus,proto3" json:"commonVimStatus,omitempty"`
	HighlightStatus    int64                  `protobuf:"varint,11,opt,name=highlightStatus,proto3" json:"highlightStatus,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *GetUserTermConfigReply) Reset() {
	*x = GetUserTermConfigReply{}
	mi := &file_user_v1_user_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserTermConfigReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserTermConfigReply) ProtoMessage() {}

func (x *GetUserTermConfigReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserTermConfigReply.ProtoReflect.Descriptor instead.
func (*GetUserTermConfigReply) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{9}
}

func (x *GetUserTermConfigReply) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetUserTermConfigReply) GetUid() int64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

func (x *GetUserTermConfigReply) GetFontSize() int64 {
	if x != nil {
		return x.FontSize
	}
	return 0
}

func (x *GetUserTermConfigReply) GetCursorStyle() string {
	if x != nil {
		return x.CursorStyle
	}
	return ""
}

func (x *GetUserTermConfigReply) GetScrollBack() int64 {
	if x != nil {
		return x.ScrollBack
	}
	return 0
}

func (x *GetUserTermConfigReply) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *GetUserTermConfigReply) GetAliasStatus() int64 {
	if x != nil {
		return x.AliasStatus
	}
	return 0
}

func (x *GetUserTermConfigReply) GetAutoCompleteStatus() int64 {
	if x != nil {
		return x.AutoCompleteStatus
	}
	return 0
}

func (x *GetUserTermConfigReply) GetQuickVimStatus() int64 {
	if x != nil {
		return x.QuickVimStatus
	}
	return 0
}

func (x *GetUserTermConfigReply) GetCommonVimStatus() int64 {
	if x != nil {
		return x.CommonVimStatus
	}
	return 0
}

func (x *GetUserTermConfigReply) GetHighlightStatus() int64 {
	if x != nil {
		return x.HighlightStatus
	}
	return 0
}

type UpdateUserTermConfigRequest struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	Id                 int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Uid                int64                  `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	FontSize           int64                  `protobuf:"varint,3,opt,name=fontSize,proto3" json:"fontSize,omitempty"`
	CursorStyle        string                 `protobuf:"bytes,4,opt,name=cursorStyle,proto3" json:"cursorStyle,omitempty"`
	ScrollBack         int64                  `protobuf:"varint,5,opt,name=scrollBack,proto3" json:"scrollBack,omitempty"`
	Language           string                 `protobuf:"bytes,6,opt,name=language,proto3" json:"language,omitempty"`
	AliasStatus        int64                  `protobuf:"varint,7,opt,name=aliasStatus,proto3" json:"aliasStatus,omitempty"`
	AutoCompleteStatus int64                  `protobuf:"varint,8,opt,name=autoCompleteStatus,proto3" json:"autoCompleteStatus,omitempty"`
	QuickVimStatus     int64                  `protobuf:"varint,9,opt,name=quickVimStatus,proto3" json:"quickVimStatus,omitempty"`
	CommonVimStatus    int64                  `protobuf:"varint,10,opt,name=commonVimStatus,proto3" json:"commonVimStatus,omitempty"`
	HighlightStatus    int64                  `protobuf:"varint,11,opt,name=highlightStatus,proto3" json:"highlightStatus,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *UpdateUserTermConfigRequest) Reset() {
	*x = UpdateUserTermConfigRequest{}
	mi := &file_user_v1_user_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateUserTermConfigRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserTermConfigRequest) ProtoMessage() {}

func (x *UpdateUserTermConfigRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserTermConfigRequest.ProtoReflect.Descriptor instead.
func (*UpdateUserTermConfigRequest) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{10}
}

func (x *UpdateUserTermConfigRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateUserTermConfigRequest) GetUid() int64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

func (x *UpdateUserTermConfigRequest) GetFontSize() int64 {
	if x != nil {
		return x.FontSize
	}
	return 0
}

func (x *UpdateUserTermConfigRequest) GetCursorStyle() string {
	if x != nil {
		return x.CursorStyle
	}
	return ""
}

func (x *UpdateUserTermConfigRequest) GetScrollBack() int64 {
	if x != nil {
		return x.ScrollBack
	}
	return 0
}

func (x *UpdateUserTermConfigRequest) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *UpdateUserTermConfigRequest) GetAliasStatus() int64 {
	if x != nil {
		return x.AliasStatus
	}
	return 0
}

func (x *UpdateUserTermConfigRequest) GetAutoCompleteStatus() int64 {
	if x != nil {
		return x.AutoCompleteStatus
	}
	return 0
}

func (x *UpdateUserTermConfigRequest) GetQuickVimStatus() int64 {
	if x != nil {
		return x.QuickVimStatus
	}
	return 0
}

func (x *UpdateUserTermConfigRequest) GetCommonVimStatus() int64 {
	if x != nil {
		return x.CommonVimStatus
	}
	return 0
}

func (x *UpdateUserTermConfigRequest) GetHighlightStatus() int64 {
	if x != nil {
		return x.HighlightStatus
	}
	return 0
}

type ListUserQuickCommandRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PageNo        int32                  `protobuf:"varint,1,opt,name=pageNo,proto3" json:"pageNo,omitempty"`
	PageSize      int32                  `protobuf:"varint,2,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
	SearchText    string                 `protobuf:"bytes,3,opt,name=searchText,proto3" json:"searchText,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListUserQuickCommandRequest) Reset() {
	*x = ListUserQuickCommandRequest{}
	mi := &file_user_v1_user_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListUserQuickCommandRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListUserQuickCommandRequest) ProtoMessage() {}

func (x *ListUserQuickCommandRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListUserQuickCommandRequest.ProtoReflect.Descriptor instead.
func (*ListUserQuickCommandRequest) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{11}
}

func (x *ListUserQuickCommandRequest) GetPageNo() int32 {
	if x != nil {
		return x.PageNo
	}
	return 0
}

func (x *ListUserQuickCommandRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListUserQuickCommandRequest) GetSearchText() string {
	if x != nil {
		return x.SearchText
	}
	return ""
}

type UserQuickCommand struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Alias         string                 `protobuf:"bytes,2,opt,name=alias,proto3" json:"alias,omitempty"`
	Command       string                 `protobuf:"bytes,3,opt,name=command,proto3" json:"command,omitempty"`
	Comment       string                 `protobuf:"bytes,4,opt,name=comment,proto3" json:"comment,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserQuickCommand) Reset() {
	*x = UserQuickCommand{}
	mi := &file_user_v1_user_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserQuickCommand) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserQuickCommand) ProtoMessage() {}

func (x *UserQuickCommand) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserQuickCommand.ProtoReflect.Descriptor instead.
func (*UserQuickCommand) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{12}
}

func (x *UserQuickCommand) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UserQuickCommand) GetAlias() string {
	if x != nil {
		return x.Alias
	}
	return ""
}

func (x *UserQuickCommand) GetCommand() string {
	if x != nil {
		return x.Command
	}
	return ""
}

func (x *UserQuickCommand) GetComment() string {
	if x != nil {
		return x.Comment
	}
	return ""
}

type ListUserQuickCommandReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          []*UserQuickCommand    `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
	PageNo        int32                  `protobuf:"varint,2,opt,name=pageNo,proto3" json:"pageNo,omitempty"`
	PageSize      int32                  `protobuf:"varint,3,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
	TotalCount    int32                  `protobuf:"varint,4,opt,name=totalCount,proto3" json:"totalCount,omitempty"`
	TotalPage     int32                  `protobuf:"varint,5,opt,name=totalPage,proto3" json:"totalPage,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListUserQuickCommandReply) Reset() {
	*x = ListUserQuickCommandReply{}
	mi := &file_user_v1_user_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListUserQuickCommandReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListUserQuickCommandReply) ProtoMessage() {}

func (x *ListUserQuickCommandReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListUserQuickCommandReply.ProtoReflect.Descriptor instead.
func (*ListUserQuickCommandReply) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{13}
}

func (x *ListUserQuickCommandReply) GetData() []*UserQuickCommand {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *ListUserQuickCommandReply) GetPageNo() int32 {
	if x != nil {
		return x.PageNo
	}
	return 0
}

func (x *ListUserQuickCommandReply) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListUserQuickCommandReply) GetTotalCount() int32 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

func (x *ListUserQuickCommandReply) GetTotalPage() int32 {
	if x != nil {
		return x.TotalPage
	}
	return 0
}

type CreateUserQuickCommandRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Alias         string                 `protobuf:"bytes,1,opt,name=alias,proto3" json:"alias,omitempty"`
	Command       string                 `protobuf:"bytes,2,opt,name=command,proto3" json:"command,omitempty"`
	Comment       string                 `protobuf:"bytes,3,opt,name=comment,proto3" json:"comment,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateUserQuickCommandRequest) Reset() {
	*x = CreateUserQuickCommandRequest{}
	mi := &file_user_v1_user_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateUserQuickCommandRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateUserQuickCommandRequest) ProtoMessage() {}

func (x *CreateUserQuickCommandRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateUserQuickCommandRequest.ProtoReflect.Descriptor instead.
func (*CreateUserQuickCommandRequest) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{14}
}

func (x *CreateUserQuickCommandRequest) GetAlias() string {
	if x != nil {
		return x.Alias
	}
	return ""
}

func (x *CreateUserQuickCommandRequest) GetCommand() string {
	if x != nil {
		return x.Command
	}
	return ""
}

func (x *CreateUserQuickCommandRequest) GetComment() string {
	if x != nil {
		return x.Comment
	}
	return ""
}

type UpdateUserQuickCommandRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Alias         string                 `protobuf:"bytes,2,opt,name=alias,proto3" json:"alias,omitempty"`
	Command       string                 `protobuf:"bytes,3,opt,name=command,proto3" json:"command,omitempty"`
	Comment       string                 `protobuf:"bytes,4,opt,name=comment,proto3" json:"comment,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateUserQuickCommandRequest) Reset() {
	*x = UpdateUserQuickCommandRequest{}
	mi := &file_user_v1_user_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateUserQuickCommandRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserQuickCommandRequest) ProtoMessage() {}

func (x *UpdateUserQuickCommandRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserQuickCommandRequest.ProtoReflect.Descriptor instead.
func (*UpdateUserQuickCommandRequest) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{15}
}

func (x *UpdateUserQuickCommandRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateUserQuickCommandRequest) GetAlias() string {
	if x != nil {
		return x.Alias
	}
	return ""
}

func (x *UpdateUserQuickCommandRequest) GetCommand() string {
	if x != nil {
		return x.Command
	}
	return ""
}

func (x *UpdateUserQuickCommandRequest) GetComment() string {
	if x != nil {
		return x.Comment
	}
	return ""
}

type DeleteUserQuickCommandRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteUserQuickCommandRequest) Reset() {
	*x = DeleteUserQuickCommandRequest{}
	mi := &file_user_v1_user_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteUserQuickCommandRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteUserQuickCommandRequest) ProtoMessage() {}

func (x *DeleteUserQuickCommandRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteUserQuickCommandRequest.ProtoReflect.Descriptor instead.
func (*DeleteUserQuickCommandRequest) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{16}
}

func (x *DeleteUserQuickCommandRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type GetUserQuickCommandRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserQuickCommandRequest) Reset() {
	*x = GetUserQuickCommandRequest{}
	mi := &file_user_v1_user_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserQuickCommandRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserQuickCommandRequest) ProtoMessage() {}

func (x *GetUserQuickCommandRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserQuickCommandRequest.ProtoReflect.Descriptor instead.
func (*GetUserQuickCommandRequest) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{17}
}

func (x *GetUserQuickCommandRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type GetUserQuickCommandReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Alias         string                 `protobuf:"bytes,2,opt,name=alias,proto3" json:"alias,omitempty"`
	Command       string                 `protobuf:"bytes,3,opt,name=command,proto3" json:"command,omitempty"`
	Comment       string                 `protobuf:"bytes,4,opt,name=comment,proto3" json:"comment,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserQuickCommandReply) Reset() {
	*x = GetUserQuickCommandReply{}
	mi := &file_user_v1_user_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserQuickCommandReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserQuickCommandReply) ProtoMessage() {}

func (x *GetUserQuickCommandReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserQuickCommandReply.ProtoReflect.Descriptor instead.
func (*GetUserQuickCommandReply) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{18}
}

func (x *GetUserQuickCommandReply) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetUserQuickCommandReply) GetAlias() string {
	if x != nil {
		return x.Alias
	}
	return ""
}

func (x *GetUserQuickCommandReply) GetCommand() string {
	if x != nil {
		return x.Command
	}
	return ""
}

func (x *GetUserQuickCommandReply) GetComment() string {
	if x != nil {
		return x.Comment
	}
	return ""
}

type CheckUserDeviceRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Ip            string                 `protobuf:"bytes,1,opt,name=ip,proto3" json:"ip,omitempty"`
	MacAddress    string                 `protobuf:"bytes,2,opt,name=macAddress,proto3" json:"macAddress,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CheckUserDeviceRequest) Reset() {
	*x = CheckUserDeviceRequest{}
	mi := &file_user_v1_user_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckUserDeviceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckUserDeviceRequest) ProtoMessage() {}

func (x *CheckUserDeviceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckUserDeviceRequest.ProtoReflect.Descriptor instead.
func (*CheckUserDeviceRequest) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{19}
}

func (x *CheckUserDeviceRequest) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *CheckUserDeviceRequest) GetMacAddress() string {
	if x != nil {
		return x.MacAddress
	}
	return ""
}

type CheckUserDeviceReply struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Ip             string                 `protobuf:"bytes,1,opt,name=ip,proto3" json:"ip,omitempty"`
	MacAddress     string                 `protobuf:"bytes,2,opt,name=macAddress,proto3" json:"macAddress,omitempty"`
	IsOfficeDevice bool                   `protobuf:"varint,3,opt,name=isOfficeDevice,proto3" json:"isOfficeDevice,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *CheckUserDeviceReply) Reset() {
	*x = CheckUserDeviceReply{}
	mi := &file_user_v1_user_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckUserDeviceReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckUserDeviceReply) ProtoMessage() {}

func (x *CheckUserDeviceReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckUserDeviceReply.ProtoReflect.Descriptor instead.
func (*CheckUserDeviceReply) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{20}
}

func (x *CheckUserDeviceReply) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *CheckUserDeviceReply) GetMacAddress() string {
	if x != nil {
		return x.MacAddress
	}
	return ""
}

func (x *CheckUserDeviceReply) GetIsOfficeDevice() bool {
	if x != nil {
		return x.IsOfficeDevice
	}
	return false
}

type GetUserInfoByEmailRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Email          string                 `protobuf:"bytes,1,opt,name=email,proto3" json:"email,omitempty"`
	OrganizationId string                 `protobuf:"bytes,2,opt,name=organizationId,proto3" json:"organizationId,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *GetUserInfoByEmailRequest) Reset() {
	*x = GetUserInfoByEmailRequest{}
	mi := &file_user_v1_user_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserInfoByEmailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserInfoByEmailRequest) ProtoMessage() {}

func (x *GetUserInfoByEmailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserInfoByEmailRequest.ProtoReflect.Descriptor instead.
func (*GetUserInfoByEmailRequest) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{21}
}

func (x *GetUserInfoByEmailRequest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *GetUserInfoByEmailRequest) GetOrganizationId() string {
	if x != nil {
		return x.OrganizationId
	}
	return ""
}

type GetUserInfoByEmailReply struct {
	state         protoimpl.MessageState          `protogen:"open.v1"`
	Data          []*GetUserInfoByEmailReply_User `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserInfoByEmailReply) Reset() {
	*x = GetUserInfoByEmailReply{}
	mi := &file_user_v1_user_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserInfoByEmailReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserInfoByEmailReply) ProtoMessage() {}

func (x *GetUserInfoByEmailReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserInfoByEmailReply.ProtoReflect.Descriptor instead.
func (*GetUserInfoByEmailReply) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{22}
}

func (x *GetUserInfoByEmailReply) GetData() []*GetUserInfoByEmailReply_User {
	if x != nil {
		return x.Data
	}
	return nil
}

type UpdateUserPwdRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Password      string                 `protobuf:"bytes,1,opt,name=password,proto3" json:"password,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateUserPwdRequest) Reset() {
	*x = UpdateUserPwdRequest{}
	mi := &file_user_v1_user_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateUserPwdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserPwdRequest) ProtoMessage() {}

func (x *UpdateUserPwdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserPwdRequest.ProtoReflect.Descriptor instead.
func (*UpdateUserPwdRequest) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{23}
}

func (x *UpdateUserPwdRequest) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

type UpdateUserRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Username      string                 `protobuf:"bytes,2,opt,name=username,proto3" json:"username,omitempty"`
	Mobile        string                 `protobuf:"bytes,3,opt,name=mobile,proto3" json:"mobile,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateUserRequest) Reset() {
	*x = UpdateUserRequest{}
	mi := &file_user_v1_user_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserRequest) ProtoMessage() {}

func (x *UpdateUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserRequest.ProtoReflect.Descriptor instead.
func (*UpdateUserRequest) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{24}
}

func (x *UpdateUserRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdateUserRequest) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *UpdateUserRequest) GetMobile() string {
	if x != nil {
		return x.Mobile
	}
	return ""
}

type GetUserInfoByEmailReply_User struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Id             int32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name           string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Email          string                 `protobuf:"bytes,3,opt,name=email,proto3" json:"email,omitempty"`
	OrganizationId string                 `protobuf:"bytes,4,opt,name=organizationId,proto3" json:"organizationId,omitempty"`
	Uid            int64                  `protobuf:"varint,5,opt,name=uid,proto3" json:"uid,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *GetUserInfoByEmailReply_User) Reset() {
	*x = GetUserInfoByEmailReply_User{}
	mi := &file_user_v1_user_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserInfoByEmailReply_User) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserInfoByEmailReply_User) ProtoMessage() {}

func (x *GetUserInfoByEmailReply_User) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserInfoByEmailReply_User.ProtoReflect.Descriptor instead.
func (*GetUserInfoByEmailReply_User) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{22, 0}
}

func (x *GetUserInfoByEmailReply_User) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetUserInfoByEmailReply_User) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GetUserInfoByEmailReply_User) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *GetUserInfoByEmailReply_User) GetOrganizationId() string {
	if x != nil {
		return x.OrganizationId
	}
	return ""
}

func (x *GetUserInfoByEmailReply_User) GetUid() int64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

var File_user_v1_user_proto protoreflect.FileDescriptor

const file_user_v1_user_proto_rawDesc = "" +
	"\n" +
	"\x12user/v1/user.proto\x12\auser.v1\x1a\x1cgoogle/api/annotations.proto\x1a\x1bgoogle/protobuf/empty.proto\"\"\n" +
	"\fHelloRequest\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\"&\n" +
	"\n" +
	"HelloReply\x12\x18\n" +
	"\amessage\x18\x01 \x01(\tR\amessage\"\x0e\n" +
	"\fEmptyRequest\";\n" +
	"\vCommonReply\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"M\n" +
	"\x13UserLoginPwdRequest\x12\x1a\n" +
	"\busername\x18\x01 \x01(\tR\busername\x12\x1a\n" +
	"\bpassword\x18\x02 \x01(\tR\bpassword\"\xa6\x01\n" +
	"\x0eUserLoginReply\x12\x14\n" +
	"\x05token\x18\x01 \x01(\tR\x05token\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x16\n" +
	"\x06avatar\x18\x03 \x01(\tR\x06avatar\x12\x14\n" +
	"\x05email\x18\x04 \x01(\tR\x05email\x12\x10\n" +
	"\x03uid\x18\x05 \x01(\x03R\x03uid\x12*\n" +
	"\x10registrationType\x18\x06 \x01(\x03R\x10registrationType\"M\n" +
	"!UserLoginEmailVerificationRequest\x12\x14\n" +
	"\x05email\x18\x01 \x01(\tR\x05email\x12\x12\n" +
	"\x04code\x18\x02 \x01(\tR\x04code\"5\n" +
	"\x1dUserLoginEmailSendCodeRequest\x12\x14\n" +
	"\x05email\x18\x01 \x01(\tR\x05email\"\xd9\x04\n" +
	"\x11GetUserByCtxReply\x12\x10\n" +
	"\x03uid\x18\x01 \x01(\x03R\x03uid\x12\x14\n" +
	"\x05email\x18\x02 \x01(\tR\x05email\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x12\x16\n" +
	"\x06avatar\x18\x04 \x01(\tR\x06avatar\x12\x16\n" +
	"\x06enName\x18\x05 \x01(\tR\x06enName\x12\x16\n" +
	"\x06mobile\x18\x06 \x01(\tR\x06mobile\x124\n" +
	"\x15secondaryOrganization\x18\a \x01(\tR\x15secondaryOrganization\x122\n" +
	"\x14tertiaryOrganization\x18\b \x01(\tR\x14tertiaryOrganization\x12\x12\n" +
	"\x04team\x18\t \x01(\tR\x04team\x12*\n" +
	"\x10registrationType\x18\n" +
	" \x01(\x03R\x10registrationType\x12\x0e\n" +
	"\x02ip\x18\v \x01(\tR\x02ip\x12\x1e\n" +
	"\n" +
	"macAddress\x18\f \x01(\tR\n" +
	"macAddress\x12\x1a\n" +
	"\busername\x18\r \x01(\tR\busername\x12\x10\n" +
	"\x03key\x18\x0e \x01(\tR\x03key\x12\"\n" +
	"\fsubscription\x18\x0f \x01(\tR\fsubscription\x12\x16\n" +
	"\x06models\x18\x10 \x03(\tR\x06models\x12&\n" +
	"\x0ellmGatewayAddr\x18\x11 \x01(\tR\x0ellmGatewayAddr\x12\x18\n" +
	"\aexpires\x18\x12 \x01(\tR\aexpires\x12\x14\n" +
	"\x05ratio\x18\x13 \x01(\x02R\x05ratio\x12$\n" +
	"\rbudgetResetAt\x18\x14 \x01(\tR\rbudgetResetAt\"\x82\x03\n" +
	"\x16GetUserTermConfigReply\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x10\n" +
	"\x03uid\x18\x02 \x01(\x03R\x03uid\x12\x1a\n" +
	"\bfontSize\x18\x03 \x01(\x03R\bfontSize\x12 \n" +
	"\vcursorStyle\x18\x04 \x01(\tR\vcursorStyle\x12\x1e\n" +
	"\n" +
	"scrollBack\x18\x05 \x01(\x03R\n" +
	"scrollBack\x12\x1a\n" +
	"\blanguage\x18\x06 \x01(\tR\blanguage\x12 \n" +
	"\valiasStatus\x18\a \x01(\x03R\valiasStatus\x12.\n" +
	"\x12autoCompleteStatus\x18\b \x01(\x03R\x12autoCompleteStatus\x12&\n" +
	"\x0equickVimStatus\x18\t \x01(\x03R\x0equickVimStatus\x12(\n" +
	"\x0fcommonVimStatus\x18\n" +
	" \x01(\x03R\x0fcommonVimStatus\x12(\n" +
	"\x0fhighlightStatus\x18\v \x01(\x03R\x0fhighlightStatus\"\x87\x03\n" +
	"\x1bUpdateUserTermConfigRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x10\n" +
	"\x03uid\x18\x02 \x01(\x03R\x03uid\x12\x1a\n" +
	"\bfontSize\x18\x03 \x01(\x03R\bfontSize\x12 \n" +
	"\vcursorStyle\x18\x04 \x01(\tR\vcursorStyle\x12\x1e\n" +
	"\n" +
	"scrollBack\x18\x05 \x01(\x03R\n" +
	"scrollBack\x12\x1a\n" +
	"\blanguage\x18\x06 \x01(\tR\blanguage\x12 \n" +
	"\valiasStatus\x18\a \x01(\x03R\valiasStatus\x12.\n" +
	"\x12autoCompleteStatus\x18\b \x01(\x03R\x12autoCompleteStatus\x12&\n" +
	"\x0equickVimStatus\x18\t \x01(\x03R\x0equickVimStatus\x12(\n" +
	"\x0fcommonVimStatus\x18\n" +
	" \x01(\x03R\x0fcommonVimStatus\x12(\n" +
	"\x0fhighlightStatus\x18\v \x01(\x03R\x0fhighlightStatus\"q\n" +
	"\x1bListUserQuickCommandRequest\x12\x16\n" +
	"\x06pageNo\x18\x01 \x01(\x05R\x06pageNo\x12\x1a\n" +
	"\bpageSize\x18\x02 \x01(\x05R\bpageSize\x12\x1e\n" +
	"\n" +
	"searchText\x18\x03 \x01(\tR\n" +
	"searchText\"l\n" +
	"\x10UserQuickCommand\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x14\n" +
	"\x05alias\x18\x02 \x01(\tR\x05alias\x12\x18\n" +
	"\acommand\x18\x03 \x01(\tR\acommand\x12\x18\n" +
	"\acomment\x18\x04 \x01(\tR\acomment\"\xbc\x01\n" +
	"\x19ListUserQuickCommandReply\x12-\n" +
	"\x04data\x18\x01 \x03(\v2\x19.user.v1.UserQuickCommandR\x04data\x12\x16\n" +
	"\x06pageNo\x18\x02 \x01(\x05R\x06pageNo\x12\x1a\n" +
	"\bpageSize\x18\x03 \x01(\x05R\bpageSize\x12\x1e\n" +
	"\n" +
	"totalCount\x18\x04 \x01(\x05R\n" +
	"totalCount\x12\x1c\n" +
	"\ttotalPage\x18\x05 \x01(\x05R\ttotalPage\"i\n" +
	"\x1dCreateUserQuickCommandRequest\x12\x14\n" +
	"\x05alias\x18\x01 \x01(\tR\x05alias\x12\x18\n" +
	"\acommand\x18\x02 \x01(\tR\acommand\x12\x18\n" +
	"\acomment\x18\x03 \x01(\tR\acomment\"y\n" +
	"\x1dUpdateUserQuickCommandRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x14\n" +
	"\x05alias\x18\x02 \x01(\tR\x05alias\x12\x18\n" +
	"\acommand\x18\x03 \x01(\tR\acommand\x12\x18\n" +
	"\acomment\x18\x04 \x01(\tR\acomment\"/\n" +
	"\x1dDeleteUserQuickCommandRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\",\n" +
	"\x1aGetUserQuickCommandRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\"t\n" +
	"\x18GetUserQuickCommandReply\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x14\n" +
	"\x05alias\x18\x02 \x01(\tR\x05alias\x12\x18\n" +
	"\acommand\x18\x03 \x01(\tR\acommand\x12\x18\n" +
	"\acomment\x18\x04 \x01(\tR\acomment\"H\n" +
	"\x16CheckUserDeviceRequest\x12\x0e\n" +
	"\x02ip\x18\x01 \x01(\tR\x02ip\x12\x1e\n" +
	"\n" +
	"macAddress\x18\x02 \x01(\tR\n" +
	"macAddress\"n\n" +
	"\x14CheckUserDeviceReply\x12\x0e\n" +
	"\x02ip\x18\x01 \x01(\tR\x02ip\x12\x1e\n" +
	"\n" +
	"macAddress\x18\x02 \x01(\tR\n" +
	"macAddress\x12&\n" +
	"\x0eisOfficeDevice\x18\x03 \x01(\bR\x0eisOfficeDevice\"Y\n" +
	"\x19GetUserInfoByEmailRequest\x12\x14\n" +
	"\x05email\x18\x01 \x01(\tR\x05email\x12&\n" +
	"\x0eorganizationId\x18\x02 \x01(\tR\x0eorganizationId\"\xd0\x01\n" +
	"\x17GetUserInfoByEmailReply\x129\n" +
	"\x04data\x18\x01 \x03(\v2%.user.v1.GetUserInfoByEmailReply.UserR\x04data\x1az\n" +
	"\x04User\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x05R\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x14\n" +
	"\x05email\x18\x03 \x01(\tR\x05email\x12&\n" +
	"\x0eorganizationId\x18\x04 \x01(\tR\x0eorganizationId\x12\x10\n" +
	"\x03uid\x18\x05 \x01(\x03R\x03uid\"2\n" +
	"\x14UpdateUserPwdRequest\x12\x1a\n" +
	"\bpassword\x18\x01 \x01(\tR\bpassword\"[\n" +
	"\x11UpdateUserRequest\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x1a\n" +
	"\busername\x18\x02 \x01(\tR\busername\x12\x16\n" +
	"\x06mobile\x18\x03 \x01(\tR\x06mobile2\xb6\x10\n" +
	"\x04User\x12H\n" +
	"\bSayHello\x12\x15.user.v1.HelloRequest\x1a\x13.user.v1.HelloReply\"\x10\x82\xd3\xe4\x93\x02\n" +
	"\x12\b/v1/user\x12d\n" +
	"\fUserLoginPwd\x12\x1c.user.v1.UserLoginPwdRequest\x1a\x17.user.v1.UserLoginReply\"\x1d\x82\xd3\xe4\x93\x02\x17:\x01*\"\x12/v1/user/login-pwd\x12\x82\x01\n" +
	"\x1aUserLoginEmailVerification\x12*.user.v1.UserLoginEmailVerificationRequest\x1a\x17.user.v1.UserLoginReply\"\x1f\x82\xd3\xe4\x93\x02\x19:\x01*\"\x14/v1/user/login-email\x12\x81\x01\n" +
	"\x16UserLoginEmailSendCode\x12&.user.v1.UserLoginEmailSendCodeRequest\x1a\x14.user.v1.CommonReply\")\x82\xd3\xe4\x93\x02#:\x01*\"\x1e/v1/user/login-email/send-code\x12Z\n" +
	"\fUserLoginSSO\x12\x15.user.v1.EmptyRequest\x1a\x17.user.v1.UserLoginReply\"\x1a\x82\xd3\xe4\x93\x02\x14\x12\x12/v1/user/login-sso\x12W\n" +
	"\fUserLoginOut\x12\x15.user.v1.EmptyRequest\x1a\x14.user.v1.CommonReply\"\x1a\x82\xd3\xe4\x93\x02\x14\x12\x12/v1/user/login-out\x12b\n" +
	"\rUpdateUserPwd\x12\x1d.user.v1.UpdateUserPwdRequest\x1a\x14.user.v1.CommonReply\"\x1c\x82\xd3\xe4\x93\x02\x16:\x01*\"\x11/v1/user/password\x12X\n" +
	"\fGetUserByCtx\x12\x15.user.v1.EmptyRequest\x1a\x1a.user.v1.GetUserByCtxReply\"\x15\x82\xd3\xe4\x93\x02\x0f\x12\r/v1/user/info\x12^\n" +
	"\n" +
	"UpdateUser\x12\x1a.user.v1.UpdateUserRequest\x1a\x1a.user.v1.GetUserByCtxReply\"\x18\x82\xd3\xe4\x93\x02\x12:\x01*\"\r/v1/user/info\x12s\n" +
	"\x0fCheckUserDevice\x12\x1f.user.v1.CheckUserDeviceRequest\x1a\x1d.user.v1.CheckUserDeviceReply\" \x82\xd3\xe4\x93\x02\x1a:\x01*\"\x15/v1/user/check-device\x12i\n" +
	"\x11GetUserTermConfig\x12\x15.user.v1.EmptyRequest\x1a\x1f.user.v1.GetUserTermConfigReply\"\x1c\x82\xd3\xe4\x93\x02\x16\x12\x14/v1/user/term-config\x12s\n" +
	"\x14UpdateUserTermConfig\x12$.user.v1.UpdateUserTermConfigRequest\x1a\x14.user.v1.CommonReply\"\x1f\x82\xd3\xe4\x93\x02\x19:\x01*\x1a\x14/v1/user/term-config\x12\x80\x01\n" +
	"\x14ListUserQuickCommand\x12$.user.v1.ListUserQuickCommandRequest\x1a\".user.v1.ListUserQuickCommandReply\"\x1e\x82\xd3\xe4\x93\x02\x18\x12\x16/v1/user/quick-command\x12y\n" +
	"\x16CreateUserQuickCommand\x12&.user.v1.CreateUserQuickCommandRequest\x1a\x14.user.v1.CommonReply\"!\x82\xd3\xe4\x93\x02\x1b:\x01*\"\x16/v1/user/quick-command\x12\x82\x01\n" +
	"\x13GetUserQuickCommand\x12#.user.v1.GetUserQuickCommandRequest\x1a!.user.v1.GetUserQuickCommandReply\"#\x82\xd3\xe4\x93\x02\x1d\x12\x1b/v1/user/quick-command/info\x12y\n" +
	"\x16UpdateUserQuickCommand\x12&.user.v1.UpdateUserQuickCommandRequest\x1a\x14.user.v1.CommonReply\"!\x82\xd3\xe4\x93\x02\x1b:\x01*\x1a\x16/v1/user/quick-command\x12v\n" +
	"\x16DeleteUserQuickCommand\x12&.user.v1.DeleteUserQuickCommandRequest\x1a\x14.user.v1.CommonReply\"\x1e\x82\xd3\xe4\x93\x02\x18*\x16/v1/user/quick-command\x12r\n" +
	"\x12GetUserInfoByEmail\x12\".user.v1.GetUserInfoByEmailRequest\x1a .user.v1.GetUserInfoByEmailReply\"\x16\x82\xd3\xe4\x93\x02\x10\x12\x0e/v1/user/users\x12c\n" +
	"\x12BatchFlushUserInfo\x12\x16.google.protobuf.Empty\x1a\x14.user.v1.CommonReply\"\x1f\x82\xd3\xe4\x93\x02\x19:\x01*\"\x14/v1/user/flush_usersBi\n" +
	"\x16dev.kratos.api.user.v1B\vUserProtoV1P\<EMAIL>/NOC/ctm/chaterm/chaterm_backend/api/user/v1;v1b\x06proto3"

var (
	file_user_v1_user_proto_rawDescOnce sync.Once
	file_user_v1_user_proto_rawDescData []byte
)

func file_user_v1_user_proto_rawDescGZIP() []byte {
	file_user_v1_user_proto_rawDescOnce.Do(func() {
		file_user_v1_user_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_user_v1_user_proto_rawDesc), len(file_user_v1_user_proto_rawDesc)))
	})
	return file_user_v1_user_proto_rawDescData
}

var file_user_v1_user_proto_msgTypes = make([]protoimpl.MessageInfo, 26)
var file_user_v1_user_proto_goTypes = []any{
	(*HelloRequest)(nil),                      // 0: user.v1.HelloRequest
	(*HelloReply)(nil),                        // 1: user.v1.HelloReply
	(*EmptyRequest)(nil),                      // 2: user.v1.EmptyRequest
	(*CommonReply)(nil),                       // 3: user.v1.CommonReply
	(*UserLoginPwdRequest)(nil),               // 4: user.v1.UserLoginPwdRequest
	(*UserLoginReply)(nil),                    // 5: user.v1.UserLoginReply
	(*UserLoginEmailVerificationRequest)(nil), // 6: user.v1.UserLoginEmailVerificationRequest
	(*UserLoginEmailSendCodeRequest)(nil),     // 7: user.v1.UserLoginEmailSendCodeRequest
	(*GetUserByCtxReply)(nil),                 // 8: user.v1.GetUserByCtxReply
	(*GetUserTermConfigReply)(nil),            // 9: user.v1.GetUserTermConfigReply
	(*UpdateUserTermConfigRequest)(nil),       // 10: user.v1.UpdateUserTermConfigRequest
	(*ListUserQuickCommandRequest)(nil),       // 11: user.v1.ListUserQuickCommandRequest
	(*UserQuickCommand)(nil),                  // 12: user.v1.UserQuickCommand
	(*ListUserQuickCommandReply)(nil),         // 13: user.v1.ListUserQuickCommandReply
	(*CreateUserQuickCommandRequest)(nil),     // 14: user.v1.CreateUserQuickCommandRequest
	(*UpdateUserQuickCommandRequest)(nil),     // 15: user.v1.UpdateUserQuickCommandRequest
	(*DeleteUserQuickCommandRequest)(nil),     // 16: user.v1.DeleteUserQuickCommandRequest
	(*GetUserQuickCommandRequest)(nil),        // 17: user.v1.GetUserQuickCommandRequest
	(*GetUserQuickCommandReply)(nil),          // 18: user.v1.GetUserQuickCommandReply
	(*CheckUserDeviceRequest)(nil),            // 19: user.v1.CheckUserDeviceRequest
	(*CheckUserDeviceReply)(nil),              // 20: user.v1.CheckUserDeviceReply
	(*GetUserInfoByEmailRequest)(nil),         // 21: user.v1.GetUserInfoByEmailRequest
	(*GetUserInfoByEmailReply)(nil),           // 22: user.v1.GetUserInfoByEmailReply
	(*UpdateUserPwdRequest)(nil),              // 23: user.v1.UpdateUserPwdRequest
	(*UpdateUserRequest)(nil),                 // 24: user.v1.UpdateUserRequest
	(*GetUserInfoByEmailReply_User)(nil),      // 25: user.v1.GetUserInfoByEmailReply.User
	(*emptypb.Empty)(nil),                     // 26: google.protobuf.Empty
}
var file_user_v1_user_proto_depIdxs = []int32{
	12, // 0: user.v1.ListUserQuickCommandReply.data:type_name -> user.v1.UserQuickCommand
	25, // 1: user.v1.GetUserInfoByEmailReply.data:type_name -> user.v1.GetUserInfoByEmailReply.User
	0,  // 2: user.v1.User.SayHello:input_type -> user.v1.HelloRequest
	4,  // 3: user.v1.User.UserLoginPwd:input_type -> user.v1.UserLoginPwdRequest
	6,  // 4: user.v1.User.UserLoginEmailVerification:input_type -> user.v1.UserLoginEmailVerificationRequest
	7,  // 5: user.v1.User.UserLoginEmailSendCode:input_type -> user.v1.UserLoginEmailSendCodeRequest
	2,  // 6: user.v1.User.UserLoginSSO:input_type -> user.v1.EmptyRequest
	2,  // 7: user.v1.User.UserLoginOut:input_type -> user.v1.EmptyRequest
	23, // 8: user.v1.User.UpdateUserPwd:input_type -> user.v1.UpdateUserPwdRequest
	2,  // 9: user.v1.User.GetUserByCtx:input_type -> user.v1.EmptyRequest
	24, // 10: user.v1.User.UpdateUser:input_type -> user.v1.UpdateUserRequest
	19, // 11: user.v1.User.CheckUserDevice:input_type -> user.v1.CheckUserDeviceRequest
	2,  // 12: user.v1.User.GetUserTermConfig:input_type -> user.v1.EmptyRequest
	10, // 13: user.v1.User.UpdateUserTermConfig:input_type -> user.v1.UpdateUserTermConfigRequest
	11, // 14: user.v1.User.ListUserQuickCommand:input_type -> user.v1.ListUserQuickCommandRequest
	14, // 15: user.v1.User.CreateUserQuickCommand:input_type -> user.v1.CreateUserQuickCommandRequest
	17, // 16: user.v1.User.GetUserQuickCommand:input_type -> user.v1.GetUserQuickCommandRequest
	15, // 17: user.v1.User.UpdateUserQuickCommand:input_type -> user.v1.UpdateUserQuickCommandRequest
	16, // 18: user.v1.User.DeleteUserQuickCommand:input_type -> user.v1.DeleteUserQuickCommandRequest
	21, // 19: user.v1.User.GetUserInfoByEmail:input_type -> user.v1.GetUserInfoByEmailRequest
	26, // 20: user.v1.User.BatchFlushUserInfo:input_type -> google.protobuf.Empty
	1,  // 21: user.v1.User.SayHello:output_type -> user.v1.HelloReply
	5,  // 22: user.v1.User.UserLoginPwd:output_type -> user.v1.UserLoginReply
	5,  // 23: user.v1.User.UserLoginEmailVerification:output_type -> user.v1.UserLoginReply
	3,  // 24: user.v1.User.UserLoginEmailSendCode:output_type -> user.v1.CommonReply
	5,  // 25: user.v1.User.UserLoginSSO:output_type -> user.v1.UserLoginReply
	3,  // 26: user.v1.User.UserLoginOut:output_type -> user.v1.CommonReply
	3,  // 27: user.v1.User.UpdateUserPwd:output_type -> user.v1.CommonReply
	8,  // 28: user.v1.User.GetUserByCtx:output_type -> user.v1.GetUserByCtxReply
	8,  // 29: user.v1.User.UpdateUser:output_type -> user.v1.GetUserByCtxReply
	20, // 30: user.v1.User.CheckUserDevice:output_type -> user.v1.CheckUserDeviceReply
	9,  // 31: user.v1.User.GetUserTermConfig:output_type -> user.v1.GetUserTermConfigReply
	3,  // 32: user.v1.User.UpdateUserTermConfig:output_type -> user.v1.CommonReply
	13, // 33: user.v1.User.ListUserQuickCommand:output_type -> user.v1.ListUserQuickCommandReply
	3,  // 34: user.v1.User.CreateUserQuickCommand:output_type -> user.v1.CommonReply
	18, // 35: user.v1.User.GetUserQuickCommand:output_type -> user.v1.GetUserQuickCommandReply
	3,  // 36: user.v1.User.UpdateUserQuickCommand:output_type -> user.v1.CommonReply
	3,  // 37: user.v1.User.DeleteUserQuickCommand:output_type -> user.v1.CommonReply
	22, // 38: user.v1.User.GetUserInfoByEmail:output_type -> user.v1.GetUserInfoByEmailReply
	3,  // 39: user.v1.User.BatchFlushUserInfo:output_type -> user.v1.CommonReply
	21, // [21:40] is the sub-list for method output_type
	2,  // [2:21] is the sub-list for method input_type
	2,  // [2:2] is the sub-list for extension type_name
	2,  // [2:2] is the sub-list for extension extendee
	0,  // [0:2] is the sub-list for field type_name
}

func init() { file_user_v1_user_proto_init() }
func file_user_v1_user_proto_init() {
	if File_user_v1_user_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_user_v1_user_proto_rawDesc), len(file_user_v1_user_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   26,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_user_v1_user_proto_goTypes,
		DependencyIndexes: file_user_v1_user_proto_depIdxs,
		MessageInfos:      file_user_v1_user_proto_msgTypes,
	}.Build()
	File_user_v1_user_proto = out.File
	file_user_v1_user_proto_goTypes = nil
	file_user_v1_user_proto_depIdxs = nil
}
