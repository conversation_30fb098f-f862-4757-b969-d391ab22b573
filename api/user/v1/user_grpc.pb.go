// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: user/v1/user.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	User_SayHello_FullMethodName                   = "/user.v1.User/SayHello"
	User_UserLoginPwd_FullMethodName               = "/user.v1.User/UserLoginPwd"
	User_UserLoginEmailVerification_FullMethodName = "/user.v1.User/UserLoginEmailVerification"
	User_UserLoginEmailSendCode_FullMethodName     = "/user.v1.User/UserLoginEmailSendCode"
	User_UserLoginSSO_FullMethodName               = "/user.v1.User/UserLoginSSO"
	User_UserLoginOut_FullMethodName               = "/user.v1.User/UserLoginOut"
	User_UpdateUserPwd_FullMethodName              = "/user.v1.User/UpdateUserPwd"
	User_GetUserByCtx_FullMethodName               = "/user.v1.User/GetUserByCtx"
	User_UpdateUser_FullMethodName                 = "/user.v1.User/UpdateUser"
	User_CheckUserDevice_FullMethodName            = "/user.v1.User/CheckUserDevice"
	User_GetUserTermConfig_FullMethodName          = "/user.v1.User/GetUserTermConfig"
	User_UpdateUserTermConfig_FullMethodName       = "/user.v1.User/UpdateUserTermConfig"
	User_ListUserQuickCommand_FullMethodName       = "/user.v1.User/ListUserQuickCommand"
	User_CreateUserQuickCommand_FullMethodName     = "/user.v1.User/CreateUserQuickCommand"
	User_GetUserQuickCommand_FullMethodName        = "/user.v1.User/GetUserQuickCommand"
	User_UpdateUserQuickCommand_FullMethodName     = "/user.v1.User/UpdateUserQuickCommand"
	User_DeleteUserQuickCommand_FullMethodName     = "/user.v1.User/DeleteUserQuickCommand"
	User_GetUserInfoByEmail_FullMethodName         = "/user.v1.User/GetUserInfoByEmail"
	User_BatchFlushUserInfo_FullMethodName         = "/user.v1.User/BatchFlushUserInfo"
)

// UserClient is the client API for User service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// User智能堡垒机终端
type UserClient interface {
	// Sends a greeting
	SayHello(ctx context.Context, in *HelloRequest, opts ...grpc.CallOption) (*HelloReply, error)
	// 用户登录 - 账号密码
	UserLoginPwd(ctx context.Context, in *UserLoginPwdRequest, opts ...grpc.CallOption) (*UserLoginReply, error)
	// 用户登录 - 邮箱验证码
	UserLoginEmailVerification(ctx context.Context, in *UserLoginEmailVerificationRequest, opts ...grpc.CallOption) (*UserLoginReply, error)
	// 用户登录 - 发送邮箱验证码
	UserLoginEmailSendCode(ctx context.Context, in *UserLoginEmailSendCodeRequest, opts ...grpc.CallOption) (*CommonReply, error)
	// 用户登录 - sso
	UserLoginSSO(ctx context.Context, in *EmptyRequest, opts ...grpc.CallOption) (*UserLoginReply, error)
	// 用户注销
	UserLoginOut(ctx context.Context, in *EmptyRequest, opts ...grpc.CallOption) (*CommonReply, error)
	// 用户密码更新
	UpdateUserPwd(ctx context.Context, in *UpdateUserPwdRequest, opts ...grpc.CallOption) (*CommonReply, error)
	// 获取用户信息
	GetUserByCtx(ctx context.Context, in *EmptyRequest, opts ...grpc.CallOption) (*GetUserByCtxReply, error)
	// 更新用户信息
	UpdateUser(ctx context.Context, in *UpdateUserRequest, opts ...grpc.CallOption) (*GetUserByCtxReply, error)
	// 校验用户设备
	CheckUserDevice(ctx context.Context, in *CheckUserDeviceRequest, opts ...grpc.CallOption) (*CheckUserDeviceReply, error)
	// 获取用户配置
	GetUserTermConfig(ctx context.Context, in *EmptyRequest, opts ...grpc.CallOption) (*GetUserTermConfigReply, error)
	// 更新用户配置
	UpdateUserTermConfig(ctx context.Context, in *UpdateUserTermConfigRequest, opts ...grpc.CallOption) (*CommonReply, error)
	ListUserQuickCommand(ctx context.Context, in *ListUserQuickCommandRequest, opts ...grpc.CallOption) (*ListUserQuickCommandReply, error)
	CreateUserQuickCommand(ctx context.Context, in *CreateUserQuickCommandRequest, opts ...grpc.CallOption) (*CommonReply, error)
	GetUserQuickCommand(ctx context.Context, in *GetUserQuickCommandRequest, opts ...grpc.CallOption) (*GetUserQuickCommandReply, error)
	UpdateUserQuickCommand(ctx context.Context, in *UpdateUserQuickCommandRequest, opts ...grpc.CallOption) (*CommonReply, error)
	DeleteUserQuickCommand(ctx context.Context, in *DeleteUserQuickCommandRequest, opts ...grpc.CallOption) (*CommonReply, error)
	// 获取用户信息
	GetUserInfoByEmail(ctx context.Context, in *GetUserInfoByEmailRequest, opts ...grpc.CallOption) (*GetUserInfoByEmailReply, error)
	BatchFlushUserInfo(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*CommonReply, error)
}

type userClient struct {
	cc grpc.ClientConnInterface
}

func NewUserClient(cc grpc.ClientConnInterface) UserClient {
	return &userClient{cc}
}

func (c *userClient) SayHello(ctx context.Context, in *HelloRequest, opts ...grpc.CallOption) (*HelloReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(HelloReply)
	err := c.cc.Invoke(ctx, User_SayHello_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userClient) UserLoginPwd(ctx context.Context, in *UserLoginPwdRequest, opts ...grpc.CallOption) (*UserLoginReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UserLoginReply)
	err := c.cc.Invoke(ctx, User_UserLoginPwd_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userClient) UserLoginEmailVerification(ctx context.Context, in *UserLoginEmailVerificationRequest, opts ...grpc.CallOption) (*UserLoginReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UserLoginReply)
	err := c.cc.Invoke(ctx, User_UserLoginEmailVerification_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userClient) UserLoginEmailSendCode(ctx context.Context, in *UserLoginEmailSendCodeRequest, opts ...grpc.CallOption) (*CommonReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CommonReply)
	err := c.cc.Invoke(ctx, User_UserLoginEmailSendCode_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userClient) UserLoginSSO(ctx context.Context, in *EmptyRequest, opts ...grpc.CallOption) (*UserLoginReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UserLoginReply)
	err := c.cc.Invoke(ctx, User_UserLoginSSO_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userClient) UserLoginOut(ctx context.Context, in *EmptyRequest, opts ...grpc.CallOption) (*CommonReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CommonReply)
	err := c.cc.Invoke(ctx, User_UserLoginOut_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userClient) UpdateUserPwd(ctx context.Context, in *UpdateUserPwdRequest, opts ...grpc.CallOption) (*CommonReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CommonReply)
	err := c.cc.Invoke(ctx, User_UpdateUserPwd_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userClient) GetUserByCtx(ctx context.Context, in *EmptyRequest, opts ...grpc.CallOption) (*GetUserByCtxReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetUserByCtxReply)
	err := c.cc.Invoke(ctx, User_GetUserByCtx_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userClient) UpdateUser(ctx context.Context, in *UpdateUserRequest, opts ...grpc.CallOption) (*GetUserByCtxReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetUserByCtxReply)
	err := c.cc.Invoke(ctx, User_UpdateUser_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userClient) CheckUserDevice(ctx context.Context, in *CheckUserDeviceRequest, opts ...grpc.CallOption) (*CheckUserDeviceReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CheckUserDeviceReply)
	err := c.cc.Invoke(ctx, User_CheckUserDevice_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userClient) GetUserTermConfig(ctx context.Context, in *EmptyRequest, opts ...grpc.CallOption) (*GetUserTermConfigReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetUserTermConfigReply)
	err := c.cc.Invoke(ctx, User_GetUserTermConfig_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userClient) UpdateUserTermConfig(ctx context.Context, in *UpdateUserTermConfigRequest, opts ...grpc.CallOption) (*CommonReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CommonReply)
	err := c.cc.Invoke(ctx, User_UpdateUserTermConfig_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userClient) ListUserQuickCommand(ctx context.Context, in *ListUserQuickCommandRequest, opts ...grpc.CallOption) (*ListUserQuickCommandReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListUserQuickCommandReply)
	err := c.cc.Invoke(ctx, User_ListUserQuickCommand_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userClient) CreateUserQuickCommand(ctx context.Context, in *CreateUserQuickCommandRequest, opts ...grpc.CallOption) (*CommonReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CommonReply)
	err := c.cc.Invoke(ctx, User_CreateUserQuickCommand_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userClient) GetUserQuickCommand(ctx context.Context, in *GetUserQuickCommandRequest, opts ...grpc.CallOption) (*GetUserQuickCommandReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetUserQuickCommandReply)
	err := c.cc.Invoke(ctx, User_GetUserQuickCommand_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userClient) UpdateUserQuickCommand(ctx context.Context, in *UpdateUserQuickCommandRequest, opts ...grpc.CallOption) (*CommonReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CommonReply)
	err := c.cc.Invoke(ctx, User_UpdateUserQuickCommand_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userClient) DeleteUserQuickCommand(ctx context.Context, in *DeleteUserQuickCommandRequest, opts ...grpc.CallOption) (*CommonReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CommonReply)
	err := c.cc.Invoke(ctx, User_DeleteUserQuickCommand_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userClient) GetUserInfoByEmail(ctx context.Context, in *GetUserInfoByEmailRequest, opts ...grpc.CallOption) (*GetUserInfoByEmailReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetUserInfoByEmailReply)
	err := c.cc.Invoke(ctx, User_GetUserInfoByEmail_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userClient) BatchFlushUserInfo(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*CommonReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CommonReply)
	err := c.cc.Invoke(ctx, User_BatchFlushUserInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// UserServer is the server API for User service.
// All implementations must embed UnimplementedUserServer
// for forward compatibility.
//
// User智能堡垒机终端
type UserServer interface {
	// Sends a greeting
	SayHello(context.Context, *HelloRequest) (*HelloReply, error)
	// 用户登录 - 账号密码
	UserLoginPwd(context.Context, *UserLoginPwdRequest) (*UserLoginReply, error)
	// 用户登录 - 邮箱验证码
	UserLoginEmailVerification(context.Context, *UserLoginEmailVerificationRequest) (*UserLoginReply, error)
	// 用户登录 - 发送邮箱验证码
	UserLoginEmailSendCode(context.Context, *UserLoginEmailSendCodeRequest) (*CommonReply, error)
	// 用户登录 - sso
	UserLoginSSO(context.Context, *EmptyRequest) (*UserLoginReply, error)
	// 用户注销
	UserLoginOut(context.Context, *EmptyRequest) (*CommonReply, error)
	// 用户密码更新
	UpdateUserPwd(context.Context, *UpdateUserPwdRequest) (*CommonReply, error)
	// 获取用户信息
	GetUserByCtx(context.Context, *EmptyRequest) (*GetUserByCtxReply, error)
	// 更新用户信息
	UpdateUser(context.Context, *UpdateUserRequest) (*GetUserByCtxReply, error)
	// 校验用户设备
	CheckUserDevice(context.Context, *CheckUserDeviceRequest) (*CheckUserDeviceReply, error)
	// 获取用户配置
	GetUserTermConfig(context.Context, *EmptyRequest) (*GetUserTermConfigReply, error)
	// 更新用户配置
	UpdateUserTermConfig(context.Context, *UpdateUserTermConfigRequest) (*CommonReply, error)
	ListUserQuickCommand(context.Context, *ListUserQuickCommandRequest) (*ListUserQuickCommandReply, error)
	CreateUserQuickCommand(context.Context, *CreateUserQuickCommandRequest) (*CommonReply, error)
	GetUserQuickCommand(context.Context, *GetUserQuickCommandRequest) (*GetUserQuickCommandReply, error)
	UpdateUserQuickCommand(context.Context, *UpdateUserQuickCommandRequest) (*CommonReply, error)
	DeleteUserQuickCommand(context.Context, *DeleteUserQuickCommandRequest) (*CommonReply, error)
	// 获取用户信息
	GetUserInfoByEmail(context.Context, *GetUserInfoByEmailRequest) (*GetUserInfoByEmailReply, error)
	BatchFlushUserInfo(context.Context, *emptypb.Empty) (*CommonReply, error)
	mustEmbedUnimplementedUserServer()
}

// UnimplementedUserServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedUserServer struct{}

func (UnimplementedUserServer) SayHello(context.Context, *HelloRequest) (*HelloReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SayHello not implemented")
}
func (UnimplementedUserServer) UserLoginPwd(context.Context, *UserLoginPwdRequest) (*UserLoginReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserLoginPwd not implemented")
}
func (UnimplementedUserServer) UserLoginEmailVerification(context.Context, *UserLoginEmailVerificationRequest) (*UserLoginReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserLoginEmailVerification not implemented")
}
func (UnimplementedUserServer) UserLoginEmailSendCode(context.Context, *UserLoginEmailSendCodeRequest) (*CommonReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserLoginEmailSendCode not implemented")
}
func (UnimplementedUserServer) UserLoginSSO(context.Context, *EmptyRequest) (*UserLoginReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserLoginSSO not implemented")
}
func (UnimplementedUserServer) UserLoginOut(context.Context, *EmptyRequest) (*CommonReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserLoginOut not implemented")
}
func (UnimplementedUserServer) UpdateUserPwd(context.Context, *UpdateUserPwdRequest) (*CommonReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateUserPwd not implemented")
}
func (UnimplementedUserServer) GetUserByCtx(context.Context, *EmptyRequest) (*GetUserByCtxReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserByCtx not implemented")
}
func (UnimplementedUserServer) UpdateUser(context.Context, *UpdateUserRequest) (*GetUserByCtxReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateUser not implemented")
}
func (UnimplementedUserServer) CheckUserDevice(context.Context, *CheckUserDeviceRequest) (*CheckUserDeviceReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckUserDevice not implemented")
}
func (UnimplementedUserServer) GetUserTermConfig(context.Context, *EmptyRequest) (*GetUserTermConfigReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserTermConfig not implemented")
}
func (UnimplementedUserServer) UpdateUserTermConfig(context.Context, *UpdateUserTermConfigRequest) (*CommonReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateUserTermConfig not implemented")
}
func (UnimplementedUserServer) ListUserQuickCommand(context.Context, *ListUserQuickCommandRequest) (*ListUserQuickCommandReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListUserQuickCommand not implemented")
}
func (UnimplementedUserServer) CreateUserQuickCommand(context.Context, *CreateUserQuickCommandRequest) (*CommonReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateUserQuickCommand not implemented")
}
func (UnimplementedUserServer) GetUserQuickCommand(context.Context, *GetUserQuickCommandRequest) (*GetUserQuickCommandReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserQuickCommand not implemented")
}
func (UnimplementedUserServer) UpdateUserQuickCommand(context.Context, *UpdateUserQuickCommandRequest) (*CommonReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateUserQuickCommand not implemented")
}
func (UnimplementedUserServer) DeleteUserQuickCommand(context.Context, *DeleteUserQuickCommandRequest) (*CommonReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteUserQuickCommand not implemented")
}
func (UnimplementedUserServer) GetUserInfoByEmail(context.Context, *GetUserInfoByEmailRequest) (*GetUserInfoByEmailReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserInfoByEmail not implemented")
}
func (UnimplementedUserServer) BatchFlushUserInfo(context.Context, *emptypb.Empty) (*CommonReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchFlushUserInfo not implemented")
}
func (UnimplementedUserServer) mustEmbedUnimplementedUserServer() {}
func (UnimplementedUserServer) testEmbeddedByValue()              {}

// UnsafeUserServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to UserServer will
// result in compilation errors.
type UnsafeUserServer interface {
	mustEmbedUnimplementedUserServer()
}

func RegisterUserServer(s grpc.ServiceRegistrar, srv UserServer) {
	// If the following call pancis, it indicates UnimplementedUserServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&User_ServiceDesc, srv)
}

func _User_SayHello_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HelloRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServer).SayHello(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: User_SayHello_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServer).SayHello(ctx, req.(*HelloRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _User_UserLoginPwd_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserLoginPwdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServer).UserLoginPwd(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: User_UserLoginPwd_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServer).UserLoginPwd(ctx, req.(*UserLoginPwdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _User_UserLoginEmailVerification_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserLoginEmailVerificationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServer).UserLoginEmailVerification(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: User_UserLoginEmailVerification_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServer).UserLoginEmailVerification(ctx, req.(*UserLoginEmailVerificationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _User_UserLoginEmailSendCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserLoginEmailSendCodeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServer).UserLoginEmailSendCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: User_UserLoginEmailSendCode_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServer).UserLoginEmailSendCode(ctx, req.(*UserLoginEmailSendCodeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _User_UserLoginSSO_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EmptyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServer).UserLoginSSO(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: User_UserLoginSSO_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServer).UserLoginSSO(ctx, req.(*EmptyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _User_UserLoginOut_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EmptyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServer).UserLoginOut(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: User_UserLoginOut_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServer).UserLoginOut(ctx, req.(*EmptyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _User_UpdateUserPwd_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateUserPwdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServer).UpdateUserPwd(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: User_UpdateUserPwd_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServer).UpdateUserPwd(ctx, req.(*UpdateUserPwdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _User_GetUserByCtx_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EmptyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServer).GetUserByCtx(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: User_GetUserByCtx_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServer).GetUserByCtx(ctx, req.(*EmptyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _User_UpdateUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServer).UpdateUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: User_UpdateUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServer).UpdateUser(ctx, req.(*UpdateUserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _User_CheckUserDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckUserDeviceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServer).CheckUserDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: User_CheckUserDevice_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServer).CheckUserDevice(ctx, req.(*CheckUserDeviceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _User_GetUserTermConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EmptyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServer).GetUserTermConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: User_GetUserTermConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServer).GetUserTermConfig(ctx, req.(*EmptyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _User_UpdateUserTermConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateUserTermConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServer).UpdateUserTermConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: User_UpdateUserTermConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServer).UpdateUserTermConfig(ctx, req.(*UpdateUserTermConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _User_ListUserQuickCommand_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListUserQuickCommandRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServer).ListUserQuickCommand(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: User_ListUserQuickCommand_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServer).ListUserQuickCommand(ctx, req.(*ListUserQuickCommandRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _User_CreateUserQuickCommand_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateUserQuickCommandRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServer).CreateUserQuickCommand(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: User_CreateUserQuickCommand_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServer).CreateUserQuickCommand(ctx, req.(*CreateUserQuickCommandRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _User_GetUserQuickCommand_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserQuickCommandRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServer).GetUserQuickCommand(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: User_GetUserQuickCommand_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServer).GetUserQuickCommand(ctx, req.(*GetUserQuickCommandRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _User_UpdateUserQuickCommand_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateUserQuickCommandRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServer).UpdateUserQuickCommand(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: User_UpdateUserQuickCommand_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServer).UpdateUserQuickCommand(ctx, req.(*UpdateUserQuickCommandRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _User_DeleteUserQuickCommand_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteUserQuickCommandRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServer).DeleteUserQuickCommand(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: User_DeleteUserQuickCommand_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServer).DeleteUserQuickCommand(ctx, req.(*DeleteUserQuickCommandRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _User_GetUserInfoByEmail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserInfoByEmailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServer).GetUserInfoByEmail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: User_GetUserInfoByEmail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServer).GetUserInfoByEmail(ctx, req.(*GetUserInfoByEmailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _User_BatchFlushUserInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServer).BatchFlushUserInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: User_BatchFlushUserInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServer).BatchFlushUserInfo(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

// User_ServiceDesc is the grpc.ServiceDesc for User service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var User_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "user.v1.User",
	HandlerType: (*UserServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SayHello",
			Handler:    _User_SayHello_Handler,
		},
		{
			MethodName: "UserLoginPwd",
			Handler:    _User_UserLoginPwd_Handler,
		},
		{
			MethodName: "UserLoginEmailVerification",
			Handler:    _User_UserLoginEmailVerification_Handler,
		},
		{
			MethodName: "UserLoginEmailSendCode",
			Handler:    _User_UserLoginEmailSendCode_Handler,
		},
		{
			MethodName: "UserLoginSSO",
			Handler:    _User_UserLoginSSO_Handler,
		},
		{
			MethodName: "UserLoginOut",
			Handler:    _User_UserLoginOut_Handler,
		},
		{
			MethodName: "UpdateUserPwd",
			Handler:    _User_UpdateUserPwd_Handler,
		},
		{
			MethodName: "GetUserByCtx",
			Handler:    _User_GetUserByCtx_Handler,
		},
		{
			MethodName: "UpdateUser",
			Handler:    _User_UpdateUser_Handler,
		},
		{
			MethodName: "CheckUserDevice",
			Handler:    _User_CheckUserDevice_Handler,
		},
		{
			MethodName: "GetUserTermConfig",
			Handler:    _User_GetUserTermConfig_Handler,
		},
		{
			MethodName: "UpdateUserTermConfig",
			Handler:    _User_UpdateUserTermConfig_Handler,
		},
		{
			MethodName: "ListUserQuickCommand",
			Handler:    _User_ListUserQuickCommand_Handler,
		},
		{
			MethodName: "CreateUserQuickCommand",
			Handler:    _User_CreateUserQuickCommand_Handler,
		},
		{
			MethodName: "GetUserQuickCommand",
			Handler:    _User_GetUserQuickCommand_Handler,
		},
		{
			MethodName: "UpdateUserQuickCommand",
			Handler:    _User_UpdateUserQuickCommand_Handler,
		},
		{
			MethodName: "DeleteUserQuickCommand",
			Handler:    _User_DeleteUserQuickCommand_Handler,
		},
		{
			MethodName: "GetUserInfoByEmail",
			Handler:    _User_GetUserInfoByEmail_Handler,
		},
		{
			MethodName: "BatchFlushUserInfo",
			Handler:    _User_BatchFlushUserInfo_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "user/v1/user.proto",
}
