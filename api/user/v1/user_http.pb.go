// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             v5.29.3
// source: user/v1/user.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationUserBatchFlushUserInfo = "/user.v1.User/BatchFlushUserInfo"
const OperationUserCheckUserDevice = "/user.v1.User/CheckUserDevice"
const OperationUserCreateUserQuickCommand = "/user.v1.User/CreateUserQuickCommand"
const OperationUserDeleteUserQuickCommand = "/user.v1.User/DeleteUserQuickCommand"
const OperationUserGetUserByCtx = "/user.v1.User/GetUserByCtx"
const OperationUserGetUserInfoByEmail = "/user.v1.User/GetUserInfoByEmail"
const OperationUserGetUserQuickCommand = "/user.v1.User/GetUserQuickCommand"
const OperationUserGetUserTermConfig = "/user.v1.User/GetUserTermConfig"
const OperationUserListUserQuickCommand = "/user.v1.User/ListUserQuickCommand"
const OperationUserSayHello = "/user.v1.User/SayHello"
const OperationUserUpdateUser = "/user.v1.User/UpdateUser"
const OperationUserUpdateUserPwd = "/user.v1.User/UpdateUserPwd"
const OperationUserUpdateUserQuickCommand = "/user.v1.User/UpdateUserQuickCommand"
const OperationUserUpdateUserTermConfig = "/user.v1.User/UpdateUserTermConfig"
const OperationUserUserLoginEmailSendCode = "/user.v1.User/UserLoginEmailSendCode"
const OperationUserUserLoginEmailVerification = "/user.v1.User/UserLoginEmailVerification"
const OperationUserUserLoginOut = "/user.v1.User/UserLoginOut"
const OperationUserUserLoginPwd = "/user.v1.User/UserLoginPwd"
const OperationUserUserLoginSSO = "/user.v1.User/UserLoginSSO"

type UserHTTPServer interface {
	BatchFlushUserInfo(context.Context, *emptypb.Empty) (*CommonReply, error)
	// CheckUserDevice 校验用户设备
	CheckUserDevice(context.Context, *CheckUserDeviceRequest) (*CheckUserDeviceReply, error)
	CreateUserQuickCommand(context.Context, *CreateUserQuickCommandRequest) (*CommonReply, error)
	DeleteUserQuickCommand(context.Context, *DeleteUserQuickCommandRequest) (*CommonReply, error)
	// GetUserByCtx 获取用户信息
	GetUserByCtx(context.Context, *EmptyRequest) (*GetUserByCtxReply, error)
	// GetUserInfoByEmail 获取用户信息
	GetUserInfoByEmail(context.Context, *GetUserInfoByEmailRequest) (*GetUserInfoByEmailReply, error)
	GetUserQuickCommand(context.Context, *GetUserQuickCommandRequest) (*GetUserQuickCommandReply, error)
	// GetUserTermConfig 获取用户配置
	GetUserTermConfig(context.Context, *EmptyRequest) (*GetUserTermConfigReply, error)
	ListUserQuickCommand(context.Context, *ListUserQuickCommandRequest) (*ListUserQuickCommandReply, error)
	// SayHello Sends a greeting
	SayHello(context.Context, *HelloRequest) (*HelloReply, error)
	// UpdateUser 更新用户信息
	UpdateUser(context.Context, *UpdateUserRequest) (*GetUserByCtxReply, error)
	// UpdateUserPwd 用户密码更新
	UpdateUserPwd(context.Context, *UpdateUserPwdRequest) (*CommonReply, error)
	UpdateUserQuickCommand(context.Context, *UpdateUserQuickCommandRequest) (*CommonReply, error)
	// UpdateUserTermConfig 更新用户配置
	UpdateUserTermConfig(context.Context, *UpdateUserTermConfigRequest) (*CommonReply, error)
	// UserLoginEmailSendCode 用户登录 - 发送邮箱验证码
	UserLoginEmailSendCode(context.Context, *UserLoginEmailSendCodeRequest) (*CommonReply, error)
	// UserLoginEmailVerification 用户登录 - 邮箱验证码
	UserLoginEmailVerification(context.Context, *UserLoginEmailVerificationRequest) (*UserLoginReply, error)
	// UserLoginOut 用户注销
	UserLoginOut(context.Context, *EmptyRequest) (*CommonReply, error)
	// UserLoginPwd 用户登录 - 账号密码
	UserLoginPwd(context.Context, *UserLoginPwdRequest) (*UserLoginReply, error)
	// UserLoginSSO 用户登录 - sso
	UserLoginSSO(context.Context, *EmptyRequest) (*UserLoginReply, error)
}

func RegisterUserHTTPServer(s *http.Server, srv UserHTTPServer) {
	r := s.Route("/")
	r.GET("/v1/user", _User_SayHello0_HTTP_Handler(srv))
	r.POST("/v1/user/login-pwd", _User_UserLoginPwd0_HTTP_Handler(srv))
	r.POST("/v1/user/login-email", _User_UserLoginEmailVerification0_HTTP_Handler(srv))
	r.POST("/v1/user/login-email/send-code", _User_UserLoginEmailSendCode0_HTTP_Handler(srv))
	r.GET("/v1/user/login-sso", _User_UserLoginSSO0_HTTP_Handler(srv))
	r.GET("/v1/user/login-out", _User_UserLoginOut0_HTTP_Handler(srv))
	r.POST("/v1/user/password", _User_UpdateUserPwd0_HTTP_Handler(srv))
	r.GET("/v1/user/info", _User_GetUserByCtx0_HTTP_Handler(srv))
	r.POST("/v1/user/info", _User_UpdateUser0_HTTP_Handler(srv))
	r.POST("/v1/user/check-device", _User_CheckUserDevice0_HTTP_Handler(srv))
	r.GET("/v1/user/term-config", _User_GetUserTermConfig0_HTTP_Handler(srv))
	r.PUT("/v1/user/term-config", _User_UpdateUserTermConfig0_HTTP_Handler(srv))
	r.GET("/v1/user/quick-command", _User_ListUserQuickCommand0_HTTP_Handler(srv))
	r.POST("/v1/user/quick-command", _User_CreateUserQuickCommand0_HTTP_Handler(srv))
	r.GET("/v1/user/quick-command/info", _User_GetUserQuickCommand0_HTTP_Handler(srv))
	r.PUT("/v1/user/quick-command", _User_UpdateUserQuickCommand0_HTTP_Handler(srv))
	r.DELETE("/v1/user/quick-command", _User_DeleteUserQuickCommand0_HTTP_Handler(srv))
	r.GET("/v1/user/users", _User_GetUserInfoByEmail0_HTTP_Handler(srv))
	r.POST("/v1/user/flush_users", _User_BatchFlushUserInfo0_HTTP_Handler(srv))
}

func _User_SayHello0_HTTP_Handler(srv UserHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in HelloRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserSayHello)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SayHello(ctx, req.(*HelloRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*HelloReply)
		return ctx.Result(200, reply)
	}
}

func _User_UserLoginPwd0_HTTP_Handler(srv UserHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UserLoginPwdRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserUserLoginPwd)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UserLoginPwd(ctx, req.(*UserLoginPwdRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*UserLoginReply)
		return ctx.Result(200, reply)
	}
}

func _User_UserLoginEmailVerification0_HTTP_Handler(srv UserHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UserLoginEmailVerificationRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserUserLoginEmailVerification)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UserLoginEmailVerification(ctx, req.(*UserLoginEmailVerificationRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*UserLoginReply)
		return ctx.Result(200, reply)
	}
}

func _User_UserLoginEmailSendCode0_HTTP_Handler(srv UserHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UserLoginEmailSendCodeRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserUserLoginEmailSendCode)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UserLoginEmailSendCode(ctx, req.(*UserLoginEmailSendCodeRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CommonReply)
		return ctx.Result(200, reply)
	}
}

func _User_UserLoginSSO0_HTTP_Handler(srv UserHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in EmptyRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserUserLoginSSO)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UserLoginSSO(ctx, req.(*EmptyRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*UserLoginReply)
		return ctx.Result(200, reply)
	}
}

func _User_UserLoginOut0_HTTP_Handler(srv UserHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in EmptyRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserUserLoginOut)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UserLoginOut(ctx, req.(*EmptyRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CommonReply)
		return ctx.Result(200, reply)
	}
}

func _User_UpdateUserPwd0_HTTP_Handler(srv UserHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdateUserPwdRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserUpdateUserPwd)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateUserPwd(ctx, req.(*UpdateUserPwdRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CommonReply)
		return ctx.Result(200, reply)
	}
}

func _User_GetUserByCtx0_HTTP_Handler(srv UserHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in EmptyRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserGetUserByCtx)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetUserByCtx(ctx, req.(*EmptyRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetUserByCtxReply)
		return ctx.Result(200, reply)
	}
}

func _User_UpdateUser0_HTTP_Handler(srv UserHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdateUserRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserUpdateUser)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateUser(ctx, req.(*UpdateUserRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetUserByCtxReply)
		return ctx.Result(200, reply)
	}
}

func _User_CheckUserDevice0_HTTP_Handler(srv UserHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CheckUserDeviceRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserCheckUserDevice)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CheckUserDevice(ctx, req.(*CheckUserDeviceRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CheckUserDeviceReply)
		return ctx.Result(200, reply)
	}
}

func _User_GetUserTermConfig0_HTTP_Handler(srv UserHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in EmptyRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserGetUserTermConfig)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetUserTermConfig(ctx, req.(*EmptyRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetUserTermConfigReply)
		return ctx.Result(200, reply)
	}
}

func _User_UpdateUserTermConfig0_HTTP_Handler(srv UserHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdateUserTermConfigRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserUpdateUserTermConfig)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateUserTermConfig(ctx, req.(*UpdateUserTermConfigRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CommonReply)
		return ctx.Result(200, reply)
	}
}

func _User_ListUserQuickCommand0_HTTP_Handler(srv UserHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListUserQuickCommandRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserListUserQuickCommand)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListUserQuickCommand(ctx, req.(*ListUserQuickCommandRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListUserQuickCommandReply)
		return ctx.Result(200, reply)
	}
}

func _User_CreateUserQuickCommand0_HTTP_Handler(srv UserHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateUserQuickCommandRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserCreateUserQuickCommand)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateUserQuickCommand(ctx, req.(*CreateUserQuickCommandRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CommonReply)
		return ctx.Result(200, reply)
	}
}

func _User_GetUserQuickCommand0_HTTP_Handler(srv UserHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetUserQuickCommandRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserGetUserQuickCommand)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetUserQuickCommand(ctx, req.(*GetUserQuickCommandRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetUserQuickCommandReply)
		return ctx.Result(200, reply)
	}
}

func _User_UpdateUserQuickCommand0_HTTP_Handler(srv UserHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdateUserQuickCommandRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserUpdateUserQuickCommand)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateUserQuickCommand(ctx, req.(*UpdateUserQuickCommandRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CommonReply)
		return ctx.Result(200, reply)
	}
}

func _User_DeleteUserQuickCommand0_HTTP_Handler(srv UserHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteUserQuickCommandRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserDeleteUserQuickCommand)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteUserQuickCommand(ctx, req.(*DeleteUserQuickCommandRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CommonReply)
		return ctx.Result(200, reply)
	}
}

func _User_GetUserInfoByEmail0_HTTP_Handler(srv UserHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetUserInfoByEmailRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserGetUserInfoByEmail)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetUserInfoByEmail(ctx, req.(*GetUserInfoByEmailRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetUserInfoByEmailReply)
		return ctx.Result(200, reply)
	}
}

func _User_BatchFlushUserInfo0_HTTP_Handler(srv UserHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in emptypb.Empty
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserBatchFlushUserInfo)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.BatchFlushUserInfo(ctx, req.(*emptypb.Empty))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CommonReply)
		return ctx.Result(200, reply)
	}
}

type UserHTTPClient interface {
	BatchFlushUserInfo(ctx context.Context, req *emptypb.Empty, opts ...http.CallOption) (rsp *CommonReply, err error)
	CheckUserDevice(ctx context.Context, req *CheckUserDeviceRequest, opts ...http.CallOption) (rsp *CheckUserDeviceReply, err error)
	CreateUserQuickCommand(ctx context.Context, req *CreateUserQuickCommandRequest, opts ...http.CallOption) (rsp *CommonReply, err error)
	DeleteUserQuickCommand(ctx context.Context, req *DeleteUserQuickCommandRequest, opts ...http.CallOption) (rsp *CommonReply, err error)
	GetUserByCtx(ctx context.Context, req *EmptyRequest, opts ...http.CallOption) (rsp *GetUserByCtxReply, err error)
	GetUserInfoByEmail(ctx context.Context, req *GetUserInfoByEmailRequest, opts ...http.CallOption) (rsp *GetUserInfoByEmailReply, err error)
	GetUserQuickCommand(ctx context.Context, req *GetUserQuickCommandRequest, opts ...http.CallOption) (rsp *GetUserQuickCommandReply, err error)
	GetUserTermConfig(ctx context.Context, req *EmptyRequest, opts ...http.CallOption) (rsp *GetUserTermConfigReply, err error)
	ListUserQuickCommand(ctx context.Context, req *ListUserQuickCommandRequest, opts ...http.CallOption) (rsp *ListUserQuickCommandReply, err error)
	SayHello(ctx context.Context, req *HelloRequest, opts ...http.CallOption) (rsp *HelloReply, err error)
	UpdateUser(ctx context.Context, req *UpdateUserRequest, opts ...http.CallOption) (rsp *GetUserByCtxReply, err error)
	UpdateUserPwd(ctx context.Context, req *UpdateUserPwdRequest, opts ...http.CallOption) (rsp *CommonReply, err error)
	UpdateUserQuickCommand(ctx context.Context, req *UpdateUserQuickCommandRequest, opts ...http.CallOption) (rsp *CommonReply, err error)
	UpdateUserTermConfig(ctx context.Context, req *UpdateUserTermConfigRequest, opts ...http.CallOption) (rsp *CommonReply, err error)
	UserLoginEmailSendCode(ctx context.Context, req *UserLoginEmailSendCodeRequest, opts ...http.CallOption) (rsp *CommonReply, err error)
	UserLoginEmailVerification(ctx context.Context, req *UserLoginEmailVerificationRequest, opts ...http.CallOption) (rsp *UserLoginReply, err error)
	UserLoginOut(ctx context.Context, req *EmptyRequest, opts ...http.CallOption) (rsp *CommonReply, err error)
	UserLoginPwd(ctx context.Context, req *UserLoginPwdRequest, opts ...http.CallOption) (rsp *UserLoginReply, err error)
	UserLoginSSO(ctx context.Context, req *EmptyRequest, opts ...http.CallOption) (rsp *UserLoginReply, err error)
}

type UserHTTPClientImpl struct {
	cc *http.Client
}

func NewUserHTTPClient(client *http.Client) UserHTTPClient {
	return &UserHTTPClientImpl{client}
}

func (c *UserHTTPClientImpl) BatchFlushUserInfo(ctx context.Context, in *emptypb.Empty, opts ...http.CallOption) (*CommonReply, error) {
	var out CommonReply
	pattern := "/v1/user/flush_users"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationUserBatchFlushUserInfo))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *UserHTTPClientImpl) CheckUserDevice(ctx context.Context, in *CheckUserDeviceRequest, opts ...http.CallOption) (*CheckUserDeviceReply, error) {
	var out CheckUserDeviceReply
	pattern := "/v1/user/check-device"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationUserCheckUserDevice))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *UserHTTPClientImpl) CreateUserQuickCommand(ctx context.Context, in *CreateUserQuickCommandRequest, opts ...http.CallOption) (*CommonReply, error) {
	var out CommonReply
	pattern := "/v1/user/quick-command"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationUserCreateUserQuickCommand))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *UserHTTPClientImpl) DeleteUserQuickCommand(ctx context.Context, in *DeleteUserQuickCommandRequest, opts ...http.CallOption) (*CommonReply, error) {
	var out CommonReply
	pattern := "/v1/user/quick-command"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationUserDeleteUserQuickCommand))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *UserHTTPClientImpl) GetUserByCtx(ctx context.Context, in *EmptyRequest, opts ...http.CallOption) (*GetUserByCtxReply, error) {
	var out GetUserByCtxReply
	pattern := "/v1/user/info"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationUserGetUserByCtx))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *UserHTTPClientImpl) GetUserInfoByEmail(ctx context.Context, in *GetUserInfoByEmailRequest, opts ...http.CallOption) (*GetUserInfoByEmailReply, error) {
	var out GetUserInfoByEmailReply
	pattern := "/v1/user/users"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationUserGetUserInfoByEmail))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *UserHTTPClientImpl) GetUserQuickCommand(ctx context.Context, in *GetUserQuickCommandRequest, opts ...http.CallOption) (*GetUserQuickCommandReply, error) {
	var out GetUserQuickCommandReply
	pattern := "/v1/user/quick-command/info"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationUserGetUserQuickCommand))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *UserHTTPClientImpl) GetUserTermConfig(ctx context.Context, in *EmptyRequest, opts ...http.CallOption) (*GetUserTermConfigReply, error) {
	var out GetUserTermConfigReply
	pattern := "/v1/user/term-config"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationUserGetUserTermConfig))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *UserHTTPClientImpl) ListUserQuickCommand(ctx context.Context, in *ListUserQuickCommandRequest, opts ...http.CallOption) (*ListUserQuickCommandReply, error) {
	var out ListUserQuickCommandReply
	pattern := "/v1/user/quick-command"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationUserListUserQuickCommand))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *UserHTTPClientImpl) SayHello(ctx context.Context, in *HelloRequest, opts ...http.CallOption) (*HelloReply, error) {
	var out HelloReply
	pattern := "/v1/user"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationUserSayHello))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *UserHTTPClientImpl) UpdateUser(ctx context.Context, in *UpdateUserRequest, opts ...http.CallOption) (*GetUserByCtxReply, error) {
	var out GetUserByCtxReply
	pattern := "/v1/user/info"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationUserUpdateUser))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *UserHTTPClientImpl) UpdateUserPwd(ctx context.Context, in *UpdateUserPwdRequest, opts ...http.CallOption) (*CommonReply, error) {
	var out CommonReply
	pattern := "/v1/user/password"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationUserUpdateUserPwd))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *UserHTTPClientImpl) UpdateUserQuickCommand(ctx context.Context, in *UpdateUserQuickCommandRequest, opts ...http.CallOption) (*CommonReply, error) {
	var out CommonReply
	pattern := "/v1/user/quick-command"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationUserUpdateUserQuickCommand))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *UserHTTPClientImpl) UpdateUserTermConfig(ctx context.Context, in *UpdateUserTermConfigRequest, opts ...http.CallOption) (*CommonReply, error) {
	var out CommonReply
	pattern := "/v1/user/term-config"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationUserUpdateUserTermConfig))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *UserHTTPClientImpl) UserLoginEmailSendCode(ctx context.Context, in *UserLoginEmailSendCodeRequest, opts ...http.CallOption) (*CommonReply, error) {
	var out CommonReply
	pattern := "/v1/user/login-email/send-code"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationUserUserLoginEmailSendCode))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *UserHTTPClientImpl) UserLoginEmailVerification(ctx context.Context, in *UserLoginEmailVerificationRequest, opts ...http.CallOption) (*UserLoginReply, error) {
	var out UserLoginReply
	pattern := "/v1/user/login-email"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationUserUserLoginEmailVerification))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *UserHTTPClientImpl) UserLoginOut(ctx context.Context, in *EmptyRequest, opts ...http.CallOption) (*CommonReply, error) {
	var out CommonReply
	pattern := "/v1/user/login-out"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationUserUserLoginOut))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *UserHTTPClientImpl) UserLoginPwd(ctx context.Context, in *UserLoginPwdRequest, opts ...http.CallOption) (*UserLoginReply, error) {
	var out UserLoginReply
	pattern := "/v1/user/login-pwd"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationUserUserLoginPwd))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *UserHTTPClientImpl) UserLoginSSO(ctx context.Context, in *EmptyRequest, opts ...http.CallOption) (*UserLoginReply, error) {
	var out UserLoginReply
	pattern := "/v1/user/login-sso"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationUserUserLoginSSO))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
