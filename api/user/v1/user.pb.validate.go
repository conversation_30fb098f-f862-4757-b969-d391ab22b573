// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: user/v1/user.proto

package v1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on HelloRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *HelloRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HelloRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in HelloRequestMultiError, or
// nil if none found.
func (m *HelloRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *HelloRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	if len(errors) > 0 {
		return HelloRequestMultiError(errors)
	}

	return nil
}

// HelloRequestMultiError is an error wrapping multiple validation errors
// returned by HelloRequest.ValidateAll() if the designated constraints aren't met.
type HelloRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HelloRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HelloRequestMultiError) AllErrors() []error { return m }

// HelloRequestValidationError is the validation error returned by
// HelloRequest.Validate if the designated constraints aren't met.
type HelloRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HelloRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HelloRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HelloRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HelloRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HelloRequestValidationError) ErrorName() string { return "HelloRequestValidationError" }

// Error satisfies the builtin error interface
func (e HelloRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHelloRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HelloRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HelloRequestValidationError{}

// Validate checks the field values on HelloReply with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *HelloReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HelloReply with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in HelloReplyMultiError, or
// nil if none found.
func (m *HelloReply) ValidateAll() error {
	return m.validate(true)
}

func (m *HelloReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Message

	if len(errors) > 0 {
		return HelloReplyMultiError(errors)
	}

	return nil
}

// HelloReplyMultiError is an error wrapping multiple validation errors
// returned by HelloReply.ValidateAll() if the designated constraints aren't met.
type HelloReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HelloReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HelloReplyMultiError) AllErrors() []error { return m }

// HelloReplyValidationError is the validation error returned by
// HelloReply.Validate if the designated constraints aren't met.
type HelloReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HelloReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HelloReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HelloReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HelloReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HelloReplyValidationError) ErrorName() string { return "HelloReplyValidationError" }

// Error satisfies the builtin error interface
func (e HelloReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHelloReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HelloReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HelloReplyValidationError{}

// Validate checks the field values on EmptyRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *EmptyRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EmptyRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in EmptyRequestMultiError, or
// nil if none found.
func (m *EmptyRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *EmptyRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return EmptyRequestMultiError(errors)
	}

	return nil
}

// EmptyRequestMultiError is an error wrapping multiple validation errors
// returned by EmptyRequest.ValidateAll() if the designated constraints aren't met.
type EmptyRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EmptyRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EmptyRequestMultiError) AllErrors() []error { return m }

// EmptyRequestValidationError is the validation error returned by
// EmptyRequest.Validate if the designated constraints aren't met.
type EmptyRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EmptyRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EmptyRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EmptyRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EmptyRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EmptyRequestValidationError) ErrorName() string { return "EmptyRequestValidationError" }

// Error satisfies the builtin error interface
func (e EmptyRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEmptyRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EmptyRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EmptyRequestValidationError{}

// Validate checks the field values on CommonReply with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CommonReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CommonReply with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CommonReplyMultiError, or
// nil if none found.
func (m *CommonReply) ValidateAll() error {
	return m.validate(true)
}

func (m *CommonReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Message

	if len(errors) > 0 {
		return CommonReplyMultiError(errors)
	}

	return nil
}

// CommonReplyMultiError is an error wrapping multiple validation errors
// returned by CommonReply.ValidateAll() if the designated constraints aren't met.
type CommonReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CommonReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CommonReplyMultiError) AllErrors() []error { return m }

// CommonReplyValidationError is the validation error returned by
// CommonReply.Validate if the designated constraints aren't met.
type CommonReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CommonReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CommonReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CommonReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CommonReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CommonReplyValidationError) ErrorName() string { return "CommonReplyValidationError" }

// Error satisfies the builtin error interface
func (e CommonReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCommonReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CommonReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CommonReplyValidationError{}

// Validate checks the field values on UserLoginPwdRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UserLoginPwdRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UserLoginPwdRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UserLoginPwdRequestMultiError, or nil if none found.
func (m *UserLoginPwdRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UserLoginPwdRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Username

	// no validation rules for Password

	if len(errors) > 0 {
		return UserLoginPwdRequestMultiError(errors)
	}

	return nil
}

// UserLoginPwdRequestMultiError is an error wrapping multiple validation
// errors returned by UserLoginPwdRequest.ValidateAll() if the designated
// constraints aren't met.
type UserLoginPwdRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UserLoginPwdRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UserLoginPwdRequestMultiError) AllErrors() []error { return m }

// UserLoginPwdRequestValidationError is the validation error returned by
// UserLoginPwdRequest.Validate if the designated constraints aren't met.
type UserLoginPwdRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UserLoginPwdRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UserLoginPwdRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UserLoginPwdRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UserLoginPwdRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UserLoginPwdRequestValidationError) ErrorName() string {
	return "UserLoginPwdRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UserLoginPwdRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUserLoginPwdRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UserLoginPwdRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UserLoginPwdRequestValidationError{}

// Validate checks the field values on UserLoginReply with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UserLoginReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UserLoginReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in UserLoginReplyMultiError,
// or nil if none found.
func (m *UserLoginReply) ValidateAll() error {
	return m.validate(true)
}

func (m *UserLoginReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Token

	// no validation rules for Name

	// no validation rules for Avatar

	// no validation rules for Email

	// no validation rules for Uid

	// no validation rules for RegistrationType

	if len(errors) > 0 {
		return UserLoginReplyMultiError(errors)
	}

	return nil
}

// UserLoginReplyMultiError is an error wrapping multiple validation errors
// returned by UserLoginReply.ValidateAll() if the designated constraints
// aren't met.
type UserLoginReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UserLoginReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UserLoginReplyMultiError) AllErrors() []error { return m }

// UserLoginReplyValidationError is the validation error returned by
// UserLoginReply.Validate if the designated constraints aren't met.
type UserLoginReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UserLoginReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UserLoginReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UserLoginReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UserLoginReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UserLoginReplyValidationError) ErrorName() string { return "UserLoginReplyValidationError" }

// Error satisfies the builtin error interface
func (e UserLoginReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUserLoginReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UserLoginReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UserLoginReplyValidationError{}

// Validate checks the field values on UserLoginEmailVerificationRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *UserLoginEmailVerificationRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UserLoginEmailVerificationRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// UserLoginEmailVerificationRequestMultiError, or nil if none found.
func (m *UserLoginEmailVerificationRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UserLoginEmailVerificationRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Email

	// no validation rules for Code

	if len(errors) > 0 {
		return UserLoginEmailVerificationRequestMultiError(errors)
	}

	return nil
}

// UserLoginEmailVerificationRequestMultiError is an error wrapping multiple
// validation errors returned by
// UserLoginEmailVerificationRequest.ValidateAll() if the designated
// constraints aren't met.
type UserLoginEmailVerificationRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UserLoginEmailVerificationRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UserLoginEmailVerificationRequestMultiError) AllErrors() []error { return m }

// UserLoginEmailVerificationRequestValidationError is the validation error
// returned by UserLoginEmailVerificationRequest.Validate if the designated
// constraints aren't met.
type UserLoginEmailVerificationRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UserLoginEmailVerificationRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UserLoginEmailVerificationRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UserLoginEmailVerificationRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UserLoginEmailVerificationRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UserLoginEmailVerificationRequestValidationError) ErrorName() string {
	return "UserLoginEmailVerificationRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UserLoginEmailVerificationRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUserLoginEmailVerificationRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UserLoginEmailVerificationRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UserLoginEmailVerificationRequestValidationError{}

// Validate checks the field values on UserLoginEmailSendCodeRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UserLoginEmailSendCodeRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UserLoginEmailSendCodeRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// UserLoginEmailSendCodeRequestMultiError, or nil if none found.
func (m *UserLoginEmailSendCodeRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UserLoginEmailSendCodeRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Email

	if len(errors) > 0 {
		return UserLoginEmailSendCodeRequestMultiError(errors)
	}

	return nil
}

// UserLoginEmailSendCodeRequestMultiError is an error wrapping multiple
// validation errors returned by UserLoginEmailSendCodeRequest.ValidateAll()
// if the designated constraints aren't met.
type UserLoginEmailSendCodeRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UserLoginEmailSendCodeRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UserLoginEmailSendCodeRequestMultiError) AllErrors() []error { return m }

// UserLoginEmailSendCodeRequestValidationError is the validation error
// returned by UserLoginEmailSendCodeRequest.Validate if the designated
// constraints aren't met.
type UserLoginEmailSendCodeRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UserLoginEmailSendCodeRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UserLoginEmailSendCodeRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UserLoginEmailSendCodeRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UserLoginEmailSendCodeRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UserLoginEmailSendCodeRequestValidationError) ErrorName() string {
	return "UserLoginEmailSendCodeRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UserLoginEmailSendCodeRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUserLoginEmailSendCodeRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UserLoginEmailSendCodeRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UserLoginEmailSendCodeRequestValidationError{}

// Validate checks the field values on GetUserByCtxReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetUserByCtxReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetUserByCtxReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetUserByCtxReplyMultiError, or nil if none found.
func (m *GetUserByCtxReply) ValidateAll() error {
	return m.validate(true)
}

func (m *GetUserByCtxReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Uid

	// no validation rules for Email

	// no validation rules for Name

	// no validation rules for Avatar

	// no validation rules for EnName

	// no validation rules for Mobile

	// no validation rules for SecondaryOrganization

	// no validation rules for TertiaryOrganization

	// no validation rules for Team

	// no validation rules for RegistrationType

	// no validation rules for Ip

	// no validation rules for MacAddress

	// no validation rules for Username

	// no validation rules for Key

	// no validation rules for Subscription

	// no validation rules for LlmGatewayAddr

	// no validation rules for Expires

	// no validation rules for Ratio

	// no validation rules for BudgetResetAt

	if len(errors) > 0 {
		return GetUserByCtxReplyMultiError(errors)
	}

	return nil
}

// GetUserByCtxReplyMultiError is an error wrapping multiple validation errors
// returned by GetUserByCtxReply.ValidateAll() if the designated constraints
// aren't met.
type GetUserByCtxReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetUserByCtxReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetUserByCtxReplyMultiError) AllErrors() []error { return m }

// GetUserByCtxReplyValidationError is the validation error returned by
// GetUserByCtxReply.Validate if the designated constraints aren't met.
type GetUserByCtxReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetUserByCtxReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetUserByCtxReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetUserByCtxReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetUserByCtxReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetUserByCtxReplyValidationError) ErrorName() string {
	return "GetUserByCtxReplyValidationError"
}

// Error satisfies the builtin error interface
func (e GetUserByCtxReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetUserByCtxReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetUserByCtxReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetUserByCtxReplyValidationError{}

// Validate checks the field values on GetUserTermConfigReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetUserTermConfigReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetUserTermConfigReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetUserTermConfigReplyMultiError, or nil if none found.
func (m *GetUserTermConfigReply) ValidateAll() error {
	return m.validate(true)
}

func (m *GetUserTermConfigReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Uid

	// no validation rules for FontSize

	// no validation rules for CursorStyle

	// no validation rules for ScrollBack

	// no validation rules for Language

	// no validation rules for AliasStatus

	// no validation rules for AutoCompleteStatus

	// no validation rules for QuickVimStatus

	// no validation rules for CommonVimStatus

	// no validation rules for HighlightStatus

	if len(errors) > 0 {
		return GetUserTermConfigReplyMultiError(errors)
	}

	return nil
}

// GetUserTermConfigReplyMultiError is an error wrapping multiple validation
// errors returned by GetUserTermConfigReply.ValidateAll() if the designated
// constraints aren't met.
type GetUserTermConfigReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetUserTermConfigReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetUserTermConfigReplyMultiError) AllErrors() []error { return m }

// GetUserTermConfigReplyValidationError is the validation error returned by
// GetUserTermConfigReply.Validate if the designated constraints aren't met.
type GetUserTermConfigReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetUserTermConfigReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetUserTermConfigReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetUserTermConfigReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetUserTermConfigReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetUserTermConfigReplyValidationError) ErrorName() string {
	return "GetUserTermConfigReplyValidationError"
}

// Error satisfies the builtin error interface
func (e GetUserTermConfigReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetUserTermConfigReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetUserTermConfigReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetUserTermConfigReplyValidationError{}

// Validate checks the field values on UpdateUserTermConfigRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateUserTermConfigRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateUserTermConfigRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateUserTermConfigRequestMultiError, or nil if none found.
func (m *UpdateUserTermConfigRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateUserTermConfigRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Uid

	// no validation rules for FontSize

	// no validation rules for CursorStyle

	// no validation rules for ScrollBack

	// no validation rules for Language

	// no validation rules for AliasStatus

	// no validation rules for AutoCompleteStatus

	// no validation rules for QuickVimStatus

	// no validation rules for CommonVimStatus

	// no validation rules for HighlightStatus

	if len(errors) > 0 {
		return UpdateUserTermConfigRequestMultiError(errors)
	}

	return nil
}

// UpdateUserTermConfigRequestMultiError is an error wrapping multiple
// validation errors returned by UpdateUserTermConfigRequest.ValidateAll() if
// the designated constraints aren't met.
type UpdateUserTermConfigRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateUserTermConfigRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateUserTermConfigRequestMultiError) AllErrors() []error { return m }

// UpdateUserTermConfigRequestValidationError is the validation error returned
// by UpdateUserTermConfigRequest.Validate if the designated constraints
// aren't met.
type UpdateUserTermConfigRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateUserTermConfigRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateUserTermConfigRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateUserTermConfigRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateUserTermConfigRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateUserTermConfigRequestValidationError) ErrorName() string {
	return "UpdateUserTermConfigRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateUserTermConfigRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateUserTermConfigRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateUserTermConfigRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateUserTermConfigRequestValidationError{}

// Validate checks the field values on ListUserQuickCommandRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListUserQuickCommandRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListUserQuickCommandRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListUserQuickCommandRequestMultiError, or nil if none found.
func (m *ListUserQuickCommandRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListUserQuickCommandRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PageNo

	// no validation rules for PageSize

	// no validation rules for SearchText

	if len(errors) > 0 {
		return ListUserQuickCommandRequestMultiError(errors)
	}

	return nil
}

// ListUserQuickCommandRequestMultiError is an error wrapping multiple
// validation errors returned by ListUserQuickCommandRequest.ValidateAll() if
// the designated constraints aren't met.
type ListUserQuickCommandRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListUserQuickCommandRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListUserQuickCommandRequestMultiError) AllErrors() []error { return m }

// ListUserQuickCommandRequestValidationError is the validation error returned
// by ListUserQuickCommandRequest.Validate if the designated constraints
// aren't met.
type ListUserQuickCommandRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListUserQuickCommandRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListUserQuickCommandRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListUserQuickCommandRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListUserQuickCommandRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListUserQuickCommandRequestValidationError) ErrorName() string {
	return "ListUserQuickCommandRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListUserQuickCommandRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListUserQuickCommandRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListUserQuickCommandRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListUserQuickCommandRequestValidationError{}

// Validate checks the field values on UserQuickCommand with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UserQuickCommand) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UserQuickCommand with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UserQuickCommandMultiError, or nil if none found.
func (m *UserQuickCommand) ValidateAll() error {
	return m.validate(true)
}

func (m *UserQuickCommand) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Alias

	// no validation rules for Command

	// no validation rules for Comment

	if len(errors) > 0 {
		return UserQuickCommandMultiError(errors)
	}

	return nil
}

// UserQuickCommandMultiError is an error wrapping multiple validation errors
// returned by UserQuickCommand.ValidateAll() if the designated constraints
// aren't met.
type UserQuickCommandMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UserQuickCommandMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UserQuickCommandMultiError) AllErrors() []error { return m }

// UserQuickCommandValidationError is the validation error returned by
// UserQuickCommand.Validate if the designated constraints aren't met.
type UserQuickCommandValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UserQuickCommandValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UserQuickCommandValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UserQuickCommandValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UserQuickCommandValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UserQuickCommandValidationError) ErrorName() string { return "UserQuickCommandValidationError" }

// Error satisfies the builtin error interface
func (e UserQuickCommandValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUserQuickCommand.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UserQuickCommandValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UserQuickCommandValidationError{}

// Validate checks the field values on ListUserQuickCommandReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListUserQuickCommandReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListUserQuickCommandReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListUserQuickCommandReplyMultiError, or nil if none found.
func (m *ListUserQuickCommandReply) ValidateAll() error {
	return m.validate(true)
}

func (m *ListUserQuickCommandReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetData() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListUserQuickCommandReplyValidationError{
						field:  fmt.Sprintf("Data[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListUserQuickCommandReplyValidationError{
						field:  fmt.Sprintf("Data[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListUserQuickCommandReplyValidationError{
					field:  fmt.Sprintf("Data[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for PageNo

	// no validation rules for PageSize

	// no validation rules for TotalCount

	// no validation rules for TotalPage

	if len(errors) > 0 {
		return ListUserQuickCommandReplyMultiError(errors)
	}

	return nil
}

// ListUserQuickCommandReplyMultiError is an error wrapping multiple validation
// errors returned by ListUserQuickCommandReply.ValidateAll() if the
// designated constraints aren't met.
type ListUserQuickCommandReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListUserQuickCommandReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListUserQuickCommandReplyMultiError) AllErrors() []error { return m }

// ListUserQuickCommandReplyValidationError is the validation error returned by
// ListUserQuickCommandReply.Validate if the designated constraints aren't met.
type ListUserQuickCommandReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListUserQuickCommandReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListUserQuickCommandReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListUserQuickCommandReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListUserQuickCommandReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListUserQuickCommandReplyValidationError) ErrorName() string {
	return "ListUserQuickCommandReplyValidationError"
}

// Error satisfies the builtin error interface
func (e ListUserQuickCommandReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListUserQuickCommandReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListUserQuickCommandReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListUserQuickCommandReplyValidationError{}

// Validate checks the field values on CreateUserQuickCommandRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateUserQuickCommandRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateUserQuickCommandRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CreateUserQuickCommandRequestMultiError, or nil if none found.
func (m *CreateUserQuickCommandRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateUserQuickCommandRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Alias

	// no validation rules for Command

	// no validation rules for Comment

	if len(errors) > 0 {
		return CreateUserQuickCommandRequestMultiError(errors)
	}

	return nil
}

// CreateUserQuickCommandRequestMultiError is an error wrapping multiple
// validation errors returned by CreateUserQuickCommandRequest.ValidateAll()
// if the designated constraints aren't met.
type CreateUserQuickCommandRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateUserQuickCommandRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateUserQuickCommandRequestMultiError) AllErrors() []error { return m }

// CreateUserQuickCommandRequestValidationError is the validation error
// returned by CreateUserQuickCommandRequest.Validate if the designated
// constraints aren't met.
type CreateUserQuickCommandRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateUserQuickCommandRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateUserQuickCommandRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateUserQuickCommandRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateUserQuickCommandRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateUserQuickCommandRequestValidationError) ErrorName() string {
	return "CreateUserQuickCommandRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateUserQuickCommandRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateUserQuickCommandRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateUserQuickCommandRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateUserQuickCommandRequestValidationError{}

// Validate checks the field values on UpdateUserQuickCommandRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateUserQuickCommandRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateUserQuickCommandRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// UpdateUserQuickCommandRequestMultiError, or nil if none found.
func (m *UpdateUserQuickCommandRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateUserQuickCommandRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Alias

	// no validation rules for Command

	// no validation rules for Comment

	if len(errors) > 0 {
		return UpdateUserQuickCommandRequestMultiError(errors)
	}

	return nil
}

// UpdateUserQuickCommandRequestMultiError is an error wrapping multiple
// validation errors returned by UpdateUserQuickCommandRequest.ValidateAll()
// if the designated constraints aren't met.
type UpdateUserQuickCommandRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateUserQuickCommandRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateUserQuickCommandRequestMultiError) AllErrors() []error { return m }

// UpdateUserQuickCommandRequestValidationError is the validation error
// returned by UpdateUserQuickCommandRequest.Validate if the designated
// constraints aren't met.
type UpdateUserQuickCommandRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateUserQuickCommandRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateUserQuickCommandRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateUserQuickCommandRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateUserQuickCommandRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateUserQuickCommandRequestValidationError) ErrorName() string {
	return "UpdateUserQuickCommandRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateUserQuickCommandRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateUserQuickCommandRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateUserQuickCommandRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateUserQuickCommandRequestValidationError{}

// Validate checks the field values on DeleteUserQuickCommandRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteUserQuickCommandRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteUserQuickCommandRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// DeleteUserQuickCommandRequestMultiError, or nil if none found.
func (m *DeleteUserQuickCommandRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteUserQuickCommandRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return DeleteUserQuickCommandRequestMultiError(errors)
	}

	return nil
}

// DeleteUserQuickCommandRequestMultiError is an error wrapping multiple
// validation errors returned by DeleteUserQuickCommandRequest.ValidateAll()
// if the designated constraints aren't met.
type DeleteUserQuickCommandRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteUserQuickCommandRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteUserQuickCommandRequestMultiError) AllErrors() []error { return m }

// DeleteUserQuickCommandRequestValidationError is the validation error
// returned by DeleteUserQuickCommandRequest.Validate if the designated
// constraints aren't met.
type DeleteUserQuickCommandRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteUserQuickCommandRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteUserQuickCommandRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteUserQuickCommandRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteUserQuickCommandRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteUserQuickCommandRequestValidationError) ErrorName() string {
	return "DeleteUserQuickCommandRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteUserQuickCommandRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteUserQuickCommandRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteUserQuickCommandRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteUserQuickCommandRequestValidationError{}

// Validate checks the field values on GetUserQuickCommandRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetUserQuickCommandRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetUserQuickCommandRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetUserQuickCommandRequestMultiError, or nil if none found.
func (m *GetUserQuickCommandRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetUserQuickCommandRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return GetUserQuickCommandRequestMultiError(errors)
	}

	return nil
}

// GetUserQuickCommandRequestMultiError is an error wrapping multiple
// validation errors returned by GetUserQuickCommandRequest.ValidateAll() if
// the designated constraints aren't met.
type GetUserQuickCommandRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetUserQuickCommandRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetUserQuickCommandRequestMultiError) AllErrors() []error { return m }

// GetUserQuickCommandRequestValidationError is the validation error returned
// by GetUserQuickCommandRequest.Validate if the designated constraints aren't met.
type GetUserQuickCommandRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetUserQuickCommandRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetUserQuickCommandRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetUserQuickCommandRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetUserQuickCommandRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetUserQuickCommandRequestValidationError) ErrorName() string {
	return "GetUserQuickCommandRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetUserQuickCommandRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetUserQuickCommandRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetUserQuickCommandRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetUserQuickCommandRequestValidationError{}

// Validate checks the field values on GetUserQuickCommandReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetUserQuickCommandReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetUserQuickCommandReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetUserQuickCommandReplyMultiError, or nil if none found.
func (m *GetUserQuickCommandReply) ValidateAll() error {
	return m.validate(true)
}

func (m *GetUserQuickCommandReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Alias

	// no validation rules for Command

	// no validation rules for Comment

	if len(errors) > 0 {
		return GetUserQuickCommandReplyMultiError(errors)
	}

	return nil
}

// GetUserQuickCommandReplyMultiError is an error wrapping multiple validation
// errors returned by GetUserQuickCommandReply.ValidateAll() if the designated
// constraints aren't met.
type GetUserQuickCommandReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetUserQuickCommandReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetUserQuickCommandReplyMultiError) AllErrors() []error { return m }

// GetUserQuickCommandReplyValidationError is the validation error returned by
// GetUserQuickCommandReply.Validate if the designated constraints aren't met.
type GetUserQuickCommandReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetUserQuickCommandReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetUserQuickCommandReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetUserQuickCommandReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetUserQuickCommandReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetUserQuickCommandReplyValidationError) ErrorName() string {
	return "GetUserQuickCommandReplyValidationError"
}

// Error satisfies the builtin error interface
func (e GetUserQuickCommandReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetUserQuickCommandReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetUserQuickCommandReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetUserQuickCommandReplyValidationError{}

// Validate checks the field values on CheckUserDeviceRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CheckUserDeviceRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckUserDeviceRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CheckUserDeviceRequestMultiError, or nil if none found.
func (m *CheckUserDeviceRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckUserDeviceRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Ip

	// no validation rules for MacAddress

	if len(errors) > 0 {
		return CheckUserDeviceRequestMultiError(errors)
	}

	return nil
}

// CheckUserDeviceRequestMultiError is an error wrapping multiple validation
// errors returned by CheckUserDeviceRequest.ValidateAll() if the designated
// constraints aren't met.
type CheckUserDeviceRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckUserDeviceRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckUserDeviceRequestMultiError) AllErrors() []error { return m }

// CheckUserDeviceRequestValidationError is the validation error returned by
// CheckUserDeviceRequest.Validate if the designated constraints aren't met.
type CheckUserDeviceRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckUserDeviceRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckUserDeviceRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckUserDeviceRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckUserDeviceRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckUserDeviceRequestValidationError) ErrorName() string {
	return "CheckUserDeviceRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CheckUserDeviceRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckUserDeviceRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckUserDeviceRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckUserDeviceRequestValidationError{}

// Validate checks the field values on CheckUserDeviceReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CheckUserDeviceReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckUserDeviceReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CheckUserDeviceReplyMultiError, or nil if none found.
func (m *CheckUserDeviceReply) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckUserDeviceReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Ip

	// no validation rules for MacAddress

	// no validation rules for IsOfficeDevice

	if len(errors) > 0 {
		return CheckUserDeviceReplyMultiError(errors)
	}

	return nil
}

// CheckUserDeviceReplyMultiError is an error wrapping multiple validation
// errors returned by CheckUserDeviceReply.ValidateAll() if the designated
// constraints aren't met.
type CheckUserDeviceReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckUserDeviceReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckUserDeviceReplyMultiError) AllErrors() []error { return m }

// CheckUserDeviceReplyValidationError is the validation error returned by
// CheckUserDeviceReply.Validate if the designated constraints aren't met.
type CheckUserDeviceReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckUserDeviceReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckUserDeviceReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckUserDeviceReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckUserDeviceReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckUserDeviceReplyValidationError) ErrorName() string {
	return "CheckUserDeviceReplyValidationError"
}

// Error satisfies the builtin error interface
func (e CheckUserDeviceReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckUserDeviceReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckUserDeviceReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckUserDeviceReplyValidationError{}

// Validate checks the field values on GetUserInfoByEmailRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetUserInfoByEmailRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetUserInfoByEmailRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetUserInfoByEmailRequestMultiError, or nil if none found.
func (m *GetUserInfoByEmailRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetUserInfoByEmailRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Email

	// no validation rules for OrganizationId

	if len(errors) > 0 {
		return GetUserInfoByEmailRequestMultiError(errors)
	}

	return nil
}

// GetUserInfoByEmailRequestMultiError is an error wrapping multiple validation
// errors returned by GetUserInfoByEmailRequest.ValidateAll() if the
// designated constraints aren't met.
type GetUserInfoByEmailRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetUserInfoByEmailRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetUserInfoByEmailRequestMultiError) AllErrors() []error { return m }

// GetUserInfoByEmailRequestValidationError is the validation error returned by
// GetUserInfoByEmailRequest.Validate if the designated constraints aren't met.
type GetUserInfoByEmailRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetUserInfoByEmailRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetUserInfoByEmailRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetUserInfoByEmailRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetUserInfoByEmailRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetUserInfoByEmailRequestValidationError) ErrorName() string {
	return "GetUserInfoByEmailRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetUserInfoByEmailRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetUserInfoByEmailRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetUserInfoByEmailRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetUserInfoByEmailRequestValidationError{}

// Validate checks the field values on GetUserInfoByEmailReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetUserInfoByEmailReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetUserInfoByEmailReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetUserInfoByEmailReplyMultiError, or nil if none found.
func (m *GetUserInfoByEmailReply) ValidateAll() error {
	return m.validate(true)
}

func (m *GetUserInfoByEmailReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetData() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetUserInfoByEmailReplyValidationError{
						field:  fmt.Sprintf("Data[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetUserInfoByEmailReplyValidationError{
						field:  fmt.Sprintf("Data[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetUserInfoByEmailReplyValidationError{
					field:  fmt.Sprintf("Data[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetUserInfoByEmailReplyMultiError(errors)
	}

	return nil
}

// GetUserInfoByEmailReplyMultiError is an error wrapping multiple validation
// errors returned by GetUserInfoByEmailReply.ValidateAll() if the designated
// constraints aren't met.
type GetUserInfoByEmailReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetUserInfoByEmailReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetUserInfoByEmailReplyMultiError) AllErrors() []error { return m }

// GetUserInfoByEmailReplyValidationError is the validation error returned by
// GetUserInfoByEmailReply.Validate if the designated constraints aren't met.
type GetUserInfoByEmailReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetUserInfoByEmailReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetUserInfoByEmailReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetUserInfoByEmailReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetUserInfoByEmailReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetUserInfoByEmailReplyValidationError) ErrorName() string {
	return "GetUserInfoByEmailReplyValidationError"
}

// Error satisfies the builtin error interface
func (e GetUserInfoByEmailReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetUserInfoByEmailReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetUserInfoByEmailReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetUserInfoByEmailReplyValidationError{}

// Validate checks the field values on UpdateUserPwdRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateUserPwdRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateUserPwdRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateUserPwdRequestMultiError, or nil if none found.
func (m *UpdateUserPwdRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateUserPwdRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Password

	if len(errors) > 0 {
		return UpdateUserPwdRequestMultiError(errors)
	}

	return nil
}

// UpdateUserPwdRequestMultiError is an error wrapping multiple validation
// errors returned by UpdateUserPwdRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateUserPwdRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateUserPwdRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateUserPwdRequestMultiError) AllErrors() []error { return m }

// UpdateUserPwdRequestValidationError is the validation error returned by
// UpdateUserPwdRequest.Validate if the designated constraints aren't met.
type UpdateUserPwdRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateUserPwdRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateUserPwdRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateUserPwdRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateUserPwdRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateUserPwdRequestValidationError) ErrorName() string {
	return "UpdateUserPwdRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateUserPwdRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateUserPwdRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateUserPwdRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateUserPwdRequestValidationError{}

// Validate checks the field values on UpdateUserRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UpdateUserRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateUserRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateUserRequestMultiError, or nil if none found.
func (m *UpdateUserRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateUserRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Username

	// no validation rules for Mobile

	if len(errors) > 0 {
		return UpdateUserRequestMultiError(errors)
	}

	return nil
}

// UpdateUserRequestMultiError is an error wrapping multiple validation errors
// returned by UpdateUserRequest.ValidateAll() if the designated constraints
// aren't met.
type UpdateUserRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateUserRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateUserRequestMultiError) AllErrors() []error { return m }

// UpdateUserRequestValidationError is the validation error returned by
// UpdateUserRequest.Validate if the designated constraints aren't met.
type UpdateUserRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateUserRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateUserRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateUserRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateUserRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateUserRequestValidationError) ErrorName() string {
	return "UpdateUserRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateUserRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateUserRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateUserRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateUserRequestValidationError{}

// Validate checks the field values on GetUserInfoByEmailReply_User with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetUserInfoByEmailReply_User) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetUserInfoByEmailReply_User with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetUserInfoByEmailReply_UserMultiError, or nil if none found.
func (m *GetUserInfoByEmailReply_User) ValidateAll() error {
	return m.validate(true)
}

func (m *GetUserInfoByEmailReply_User) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Name

	// no validation rules for Email

	// no validation rules for OrganizationId

	// no validation rules for Uid

	if len(errors) > 0 {
		return GetUserInfoByEmailReply_UserMultiError(errors)
	}

	return nil
}

// GetUserInfoByEmailReply_UserMultiError is an error wrapping multiple
// validation errors returned by GetUserInfoByEmailReply_User.ValidateAll() if
// the designated constraints aren't met.
type GetUserInfoByEmailReply_UserMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetUserInfoByEmailReply_UserMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetUserInfoByEmailReply_UserMultiError) AllErrors() []error { return m }

// GetUserInfoByEmailReply_UserValidationError is the validation error returned
// by GetUserInfoByEmailReply_User.Validate if the designated constraints
// aren't met.
type GetUserInfoByEmailReply_UserValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetUserInfoByEmailReply_UserValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetUserInfoByEmailReply_UserValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetUserInfoByEmailReply_UserValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetUserInfoByEmailReply_UserValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetUserInfoByEmailReply_UserValidationError) ErrorName() string {
	return "GetUserInfoByEmailReply_UserValidationError"
}

// Error satisfies the builtin error interface
func (e GetUserInfoByEmailReply_UserValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetUserInfoByEmailReply_User.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetUserInfoByEmailReply_UserValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetUserInfoByEmailReply_UserValidationError{}
