syntax = "proto3";

package user.v1;

import "google/api/annotations.proto";
import "google/protobuf/empty.proto";

option go_package = "gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/api/user/v1;v1";
option java_multiple_files = true;
option java_package = "dev.kratos.api.user.v1";
option java_outer_classname = "UserProtoV1";

// User智能堡垒机终端
service User {
  // Sends a greeting
  rpc SayHello (HelloRequest) returns (HelloReply) {
    option (google.api.http) = {
      get: "/v1/user"
    };
  }
  // 用户登录 - 账号密码
  rpc UserLoginPwd (UserLoginPwdRequest) returns (UserLoginReply) {
    option (google.api.http) = {
      post: "/v1/user/login-pwd"
      body: "*"
    };
  }
  // 用户登录 - 邮箱验证码
  rpc UserLoginEmailVerification (UserLoginEmailVerificationRequest) returns (UserLoginReply) {
    option (google.api.http) = {
      post: "/v1/user/login-email"
      body: "*"
    };
  }
  // 用户登录 - 发送邮箱验证码
  rpc UserLoginEmailSendCode (UserLoginEmailSendCodeRequest) returns (CommonReply) {
    option (google.api.http) = {
      post: "/v1/user/login-email/send-code"
      body: "*"
    };
  }
  // 用户登录 - sso
  rpc UserLoginSSO (EmptyRequest) returns (UserLoginReply) {
    option (google.api.http) = {
      get: "/v1/user/login-sso"
    };
  }
  // 用户注销
  rpc UserLoginOut (EmptyRequest) returns (CommonReply) {
    option (google.api.http) = {
      get: "/v1/user/login-out"
    };
  }
  // 用户密码更新
  rpc UpdateUserPwd(UpdateUserPwdRequest) returns (CommonReply) {
    option (google.api.http) = {
      post: "/v1/user/password"
      body: "*"
    };
  }
  // 获取用户信息
  rpc GetUserByCtx (EmptyRequest) returns (GetUserByCtxReply) {
    option (google.api.http) = {
      get: "/v1/user/info"
    };
  }
  // 更新用户信息
  rpc UpdateUser (UpdateUserRequest) returns (GetUserByCtxReply) {
    option (google.api.http) = {
      post: "/v1/user/info",
      body: "*"
    };
  }
  // 校验用户设备
  rpc CheckUserDevice (CheckUserDeviceRequest) returns (CheckUserDeviceReply) {
    option (google.api.http) = {
      post: "/v1/user/check-device",
      body: "*"
    };
  }
  // 获取用户配置
  rpc GetUserTermConfig(EmptyRequest) returns (GetUserTermConfigReply) {
    option (google.api.http) = {
      get: "/v1/user/term-config"
    };
  }
  // 更新用户配置
  rpc UpdateUserTermConfig(UpdateUserTermConfigRequest) returns (CommonReply) {
    option (google.api.http) = {
      put: "/v1/user/term-config"
      body: "*"
    };
  }
  rpc ListUserQuickCommand(ListUserQuickCommandRequest) returns (ListUserQuickCommandReply) {
    option (google.api.http) = {
      get: "/v1/user/quick-command"
    };
  }
  rpc CreateUserQuickCommand(CreateUserQuickCommandRequest) returns (CommonReply) {
    option (google.api.http) = {
      post: "/v1/user/quick-command"
      body: "*"
    };
  }
  rpc GetUserQuickCommand(GetUserQuickCommandRequest) returns (GetUserQuickCommandReply) {
    option (google.api.http) = {
      get: "/v1/user/quick-command/info"
    };
  }
  rpc UpdateUserQuickCommand(UpdateUserQuickCommandRequest) returns (CommonReply) {
    option (google.api.http) = {
      put: "/v1/user/quick-command"
      body: "*"
    };
  }
  rpc DeleteUserQuickCommand(DeleteUserQuickCommandRequest) returns (CommonReply) {
    option (google.api.http) = {
      delete: "/v1/user/quick-command"
    };
  }

  // 获取用户信息
  rpc GetUserInfoByEmail(GetUserInfoByEmailRequest) returns (GetUserInfoByEmailReply) {
    option (google.api.http) = {
      get: "/v1/user/users"
    };
  }
  rpc BatchFlushUserInfo(google.protobuf.Empty) returns (CommonReply) {
    option (google.api.http) = {
      post: "/v1/user/flush_users"
      body: "*"
    };
  }
}

message HelloRequest {
  string name = 1;
}

message HelloReply {
  string message = 1;
}

message EmptyRequest {
}

message CommonReply {
  int32 code = 1;
  string message = 2;
}

message UserLoginPwdRequest {
  string username = 1;
  string password = 2;
}

message UserLoginReply {
  string token = 1;
  string name = 2;
  string avatar = 3;
  string email = 4;
  int64 uid = 5;
  int64 registrationType = 6;
}

message UserLoginEmailVerificationRequest {
  string email = 1;
  string code = 2;
}

message UserLoginEmailSendCodeRequest {
  string email = 1;
}

message GetUserByCtxReply {
  int64 uid = 1;
  string email = 2;
  string name = 3;
  string avatar = 4;
  string enName = 5;
  string mobile = 6;
  string secondaryOrganization = 7;
  string tertiaryOrganization = 8;
  string team = 9;
  int64 registrationType = 10;
  string ip = 11;
  string macAddress = 12;
  string username = 13;
  string key = 14;
  string subscription = 15;
  repeated string models = 16;
  string llmGatewayAddr = 17;
  string expires = 18;
  float ratio = 19;
  string budgetResetAt =20;
}

message GetUserTermConfigReply {
  int64 id = 1;
  int64 uid = 2;
  int64 fontSize = 3;
  string cursorStyle = 4;
  int64 scrollBack = 5;
  string language = 6;
  int64 aliasStatus = 7;
  int64 autoCompleteStatus = 8;
  int64 quickVimStatus = 9;
  int64 commonVimStatus = 10;
  int64 highlightStatus = 11;
}

message UpdateUserTermConfigRequest {
  int64 id = 1;
  int64 uid = 2;
  int64 fontSize = 3;
  string cursorStyle = 4;
  int64 scrollBack = 5;
  string language = 6;
  int64 aliasStatus = 7;
  int64 autoCompleteStatus = 8;
  int64 quickVimStatus = 9;
  int64 commonVimStatus = 10;
  int64 highlightStatus = 11;
}

message ListUserQuickCommandRequest {
  int32 pageNo = 1;
  int32 pageSize = 2;
  string searchText = 3;
}

message UserQuickCommand {
  int64 id = 1;
  string alias = 2;
  string command = 3;
  string comment = 4;
}

message ListUserQuickCommandReply {
  repeated UserQuickCommand data = 1;
  int32 pageNo = 2;
  int32 pageSize = 3;
  int32 totalCount = 4;
  int32 totalPage = 5;
}

message CreateUserQuickCommandRequest {
  string alias = 1;
  string command = 2;
  string comment = 3;
}

message UpdateUserQuickCommandRequest {
  int64 id = 1;
  string alias = 2;
  string command = 3;
  string comment = 4;
}

message DeleteUserQuickCommandRequest {
  int64 id = 1;
}

message GetUserQuickCommandRequest {
  int64 id = 1;
}

message GetUserQuickCommandReply {
  int64 id = 1;
  string alias = 2;
  string command = 3;
  string comment = 4;
}


message CheckUserDeviceRequest {
  string ip = 1;
  string macAddress = 2;
}

message CheckUserDeviceReply {
  string ip = 1;
  string macAddress = 2;
  bool isOfficeDevice = 3;
}

message GetUserInfoByEmailRequest {
  string email = 1;
  string organizationId = 2;
}
message GetUserInfoByEmailReply {
  message User {
    int32 id = 1;
    string name = 2;
    string email = 3;
    string organizationId = 4;
    int64 uid = 5;
  }
  repeated User data = 1;
}

message UpdateUserPwdRequest {
  string password = 1;
}

message UpdateUserRequest {
  string name = 1;
  string username = 2;
  string mobile = 3;
}