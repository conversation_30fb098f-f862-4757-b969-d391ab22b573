// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.2
// - protoc             v4.23.4
// source: ai/v1/ai.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationAiGetConversationDetail = "/ai.v1.Ai/GetConversationDetail"
const OperationAiGetModelList = "/ai.v1.Ai/GetModelList"
const OperationAiGetUserConversationList = "/ai.v1.Ai/GetUserConversationList"

type AiHTTPServer interface {
	GetConversationDetail(context.Context, *GetConversationDetailRequest) (*GetConversationDetailReply, error)
	GetModelList(context.Context, *emptypb.Empty) (*GetModelListReply, error)
	GetUserConversationList(context.Context, *emptypb.Empty) (*GetUserConversationListReply, error)
}

func RegisterAiHTTPServer(s *http.Server, srv AiHTTPServer) {
	r := s.Route("/")
	r.GET("/v1/ai/conversation/model-list", _Ai_GetModelList0_HTTP_Handler(srv))
	r.GET("/v1/ai/conversation/conversation-list", _Ai_GetUserConversationList0_HTTP_Handler(srv))
	r.GET("/v1/ai/conversation/conversation-detail", _Ai_GetConversationDetail0_HTTP_Handler(srv))
}

func _Ai_GetModelList0_HTTP_Handler(srv AiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in emptypb.Empty
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAiGetModelList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetModelList(ctx, req.(*emptypb.Empty))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetModelListReply)
		return ctx.Result(200, reply)
	}
}

func _Ai_GetUserConversationList0_HTTP_Handler(srv AiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in emptypb.Empty
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAiGetUserConversationList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetUserConversationList(ctx, req.(*emptypb.Empty))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetUserConversationListReply)
		return ctx.Result(200, reply)
	}
}

func _Ai_GetConversationDetail0_HTTP_Handler(srv AiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetConversationDetailRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAiGetConversationDetail)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetConversationDetail(ctx, req.(*GetConversationDetailRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetConversationDetailReply)
		return ctx.Result(200, reply)
	}
}

type AiHTTPClient interface {
	GetConversationDetail(ctx context.Context, req *GetConversationDetailRequest, opts ...http.CallOption) (rsp *GetConversationDetailReply, err error)
	GetModelList(ctx context.Context, req *emptypb.Empty, opts ...http.CallOption) (rsp *GetModelListReply, err error)
	GetUserConversationList(ctx context.Context, req *emptypb.Empty, opts ...http.CallOption) (rsp *GetUserConversationListReply, err error)
}

type AiHTTPClientImpl struct {
	cc *http.Client
}

func NewAiHTTPClient(client *http.Client) AiHTTPClient {
	return &AiHTTPClientImpl{client}
}

func (c *AiHTTPClientImpl) GetConversationDetail(ctx context.Context, in *GetConversationDetailRequest, opts ...http.CallOption) (*GetConversationDetailReply, error) {
	var out GetConversationDetailReply
	pattern := "/v1/ai/conversation/conversation-detail"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAiGetConversationDetail))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AiHTTPClientImpl) GetModelList(ctx context.Context, in *emptypb.Empty, opts ...http.CallOption) (*GetModelListReply, error) {
	var out GetModelListReply
	pattern := "/v1/ai/conversation/model-list"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAiGetModelList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AiHTTPClientImpl) GetUserConversationList(ctx context.Context, in *emptypb.Empty, opts ...http.CallOption) (*GetUserConversationListReply, error) {
	var out GetUserConversationListReply
	pattern := "/v1/ai/conversation/conversation-list"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAiGetUserConversationList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
