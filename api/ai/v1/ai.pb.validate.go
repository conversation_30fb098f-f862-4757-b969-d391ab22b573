// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: ai/v1/ai.proto

package v1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on GetConversationDetailRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetConversationDetailRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetConversationDetailRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetConversationDetailRequestMultiError, or nil if none found.
func (m *GetConversationDetailRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetConversationDetailRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetLimit() <= 0 {
		err := GetConversationDetailRequestValidationError{
			field:  "Limit",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetOffset() <= -1 {
		err := GetConversationDetailRequestValidationError{
			field:  "Offset",
			reason: "value must be greater than -1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetConversationId()) != 56 {
		err := GetConversationDetailRequestValidationError{
			field:  "ConversationId",
			reason: "value length must be 56 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)

	}

	if len(errors) > 0 {
		return GetConversationDetailRequestMultiError(errors)
	}

	return nil
}

// GetConversationDetailRequestMultiError is an error wrapping multiple
// validation errors returned by GetConversationDetailRequest.ValidateAll() if
// the designated constraints aren't met.
type GetConversationDetailRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetConversationDetailRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetConversationDetailRequestMultiError) AllErrors() []error { return m }

// GetConversationDetailRequestValidationError is the validation error returned
// by GetConversationDetailRequest.Validate if the designated constraints
// aren't met.
type GetConversationDetailRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetConversationDetailRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetConversationDetailRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetConversationDetailRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetConversationDetailRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetConversationDetailRequestValidationError) ErrorName() string {
	return "GetConversationDetailRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetConversationDetailRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetConversationDetailRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetConversationDetailRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetConversationDetailRequestValidationError{}

// Validate checks the field values on GetConversationDetailReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetConversationDetailReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetConversationDetailReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetConversationDetailReplyMultiError, or nil if none found.
func (m *GetConversationDetailReply) ValidateAll() error {
	return m.validate(true)
}

func (m *GetConversationDetailReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetConversationDetailReplyValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetConversationDetailReplyValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetConversationDetailReplyValidationError{
					field:  fmt.Sprintf("List[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Total

	if len(errors) > 0 {
		return GetConversationDetailReplyMultiError(errors)
	}

	return nil
}

// GetConversationDetailReplyMultiError is an error wrapping multiple
// validation errors returned by GetConversationDetailReply.ValidateAll() if
// the designated constraints aren't met.
type GetConversationDetailReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetConversationDetailReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetConversationDetailReplyMultiError) AllErrors() []error { return m }

// GetConversationDetailReplyValidationError is the validation error returned
// by GetConversationDetailReply.Validate if the designated constraints aren't met.
type GetConversationDetailReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetConversationDetailReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetConversationDetailReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetConversationDetailReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetConversationDetailReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetConversationDetailReplyValidationError) ErrorName() string {
	return "GetConversationDetailReplyValidationError"
}

// Error satisfies the builtin error interface
func (e GetConversationDetailReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetConversationDetailReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetConversationDetailReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetConversationDetailReplyValidationError{}

// Validate checks the field values on GetModelListReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetModelListReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetModelListReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetModelListReplyMultiError, or nil if none found.
func (m *GetModelListReply) ValidateAll() error {
	return m.validate(true)
}

func (m *GetModelListReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetModels() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetModelListReplyValidationError{
						field:  fmt.Sprintf("Models[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetModelListReplyValidationError{
						field:  fmt.Sprintf("Models[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetModelListReplyValidationError{
					field:  fmt.Sprintf("Models[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetModelListReplyMultiError(errors)
	}

	return nil
}

// GetModelListReplyMultiError is an error wrapping multiple validation errors
// returned by GetModelListReply.ValidateAll() if the designated constraints
// aren't met.
type GetModelListReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetModelListReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetModelListReplyMultiError) AllErrors() []error { return m }

// GetModelListReplyValidationError is the validation error returned by
// GetModelListReply.Validate if the designated constraints aren't met.
type GetModelListReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetModelListReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetModelListReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetModelListReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetModelListReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetModelListReplyValidationError) ErrorName() string {
	return "GetModelListReplyValidationError"
}

// Error satisfies the builtin error interface
func (e GetModelListReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetModelListReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetModelListReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetModelListReplyValidationError{}

// Validate checks the field values on GetUserConversationListReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetUserConversationListReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetUserConversationListReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetUserConversationListReplyMultiError, or nil if none found.
func (m *GetUserConversationListReply) ValidateAll() error {
	return m.validate(true)
}

func (m *GetUserConversationListReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetUserConversationListReplyValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetUserConversationListReplyValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetUserConversationListReplyValidationError{
					field:  fmt.Sprintf("List[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetUserConversationListReplyMultiError(errors)
	}

	return nil
}

// GetUserConversationListReplyMultiError is an error wrapping multiple
// validation errors returned by GetUserConversationListReply.ValidateAll() if
// the designated constraints aren't met.
type GetUserConversationListReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetUserConversationListReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetUserConversationListReplyMultiError) AllErrors() []error { return m }

// GetUserConversationListReplyValidationError is the validation error returned
// by GetUserConversationListReply.Validate if the designated constraints
// aren't met.
type GetUserConversationListReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetUserConversationListReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetUserConversationListReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetUserConversationListReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetUserConversationListReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetUserConversationListReplyValidationError) ErrorName() string {
	return "GetUserConversationListReplyValidationError"
}

// Error satisfies the builtin error interface
func (e GetUserConversationListReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetUserConversationListReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetUserConversationListReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetUserConversationListReplyValidationError{}

// Validate checks the field values on GetConversationDetailReply_Detail with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetConversationDetailReply_Detail) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetConversationDetailReply_Detail
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetConversationDetailReply_DetailMultiError, or nil if none found.
func (m *GetConversationDetailReply_Detail) ValidateAll() error {
	return m.validate(true)
}

func (m *GetConversationDetailReply_Detail) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ConversationId

	// no validation rules for CreatedAt

	// no validation rules for SendAt

	// no validation rules for Model

	// no validation rules for ConversateType

	// no validation rules for Content

	if len(errors) > 0 {
		return GetConversationDetailReply_DetailMultiError(errors)
	}

	return nil
}

// GetConversationDetailReply_DetailMultiError is an error wrapping multiple
// validation errors returned by
// GetConversationDetailReply_Detail.ValidateAll() if the designated
// constraints aren't met.
type GetConversationDetailReply_DetailMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetConversationDetailReply_DetailMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetConversationDetailReply_DetailMultiError) AllErrors() []error { return m }

// GetConversationDetailReply_DetailValidationError is the validation error
// returned by GetConversationDetailReply_Detail.Validate if the designated
// constraints aren't met.
type GetConversationDetailReply_DetailValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetConversationDetailReply_DetailValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetConversationDetailReply_DetailValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetConversationDetailReply_DetailValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetConversationDetailReply_DetailValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetConversationDetailReply_DetailValidationError) ErrorName() string {
	return "GetConversationDetailReply_DetailValidationError"
}

// Error satisfies the builtin error interface
func (e GetConversationDetailReply_DetailValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetConversationDetailReply_Detail.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetConversationDetailReply_DetailValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetConversationDetailReply_DetailValidationError{}

// Validate checks the field values on GetModelListReply_Model with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetModelListReply_Model) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetModelListReply_Model with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetModelListReply_ModelMultiError, or nil if none found.
func (m *GetModelListReply_Model) ValidateAll() error {
	return m.validate(true)
}

func (m *GetModelListReply_Model) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Object

	// no validation rules for Provider

	if len(errors) > 0 {
		return GetModelListReply_ModelMultiError(errors)
	}

	return nil
}

// GetModelListReply_ModelMultiError is an error wrapping multiple validation
// errors returned by GetModelListReply_Model.ValidateAll() if the designated
// constraints aren't met.
type GetModelListReply_ModelMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetModelListReply_ModelMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetModelListReply_ModelMultiError) AllErrors() []error { return m }

// GetModelListReply_ModelValidationError is the validation error returned by
// GetModelListReply_Model.Validate if the designated constraints aren't met.
type GetModelListReply_ModelValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetModelListReply_ModelValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetModelListReply_ModelValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetModelListReply_ModelValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetModelListReply_ModelValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetModelListReply_ModelValidationError) ErrorName() string {
	return "GetModelListReply_ModelValidationError"
}

// Error satisfies the builtin error interface
func (e GetModelListReply_ModelValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetModelListReply_Model.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetModelListReply_ModelValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetModelListReply_ModelValidationError{}

// Validate checks the field values on
// GetUserConversationListReply_Conversation with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetUserConversationListReply_Conversation) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetUserConversationListReply_Conversation with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetUserConversationListReply_ConversationMultiError, or nil if none found.
func (m *GetUserConversationListReply_Conversation) ValidateAll() error {
	return m.validate(true)
}

func (m *GetUserConversationListReply_Conversation) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ConversationId

	// no validation rules for CreatedAt

	// no validation rules for UpdatedAt

	// no validation rules for Title

	// no validation rules for Model

	// no validation rules for ConversateType

	if len(errors) > 0 {
		return GetUserConversationListReply_ConversationMultiError(errors)
	}

	return nil
}

// GetUserConversationListReply_ConversationMultiError is an error wrapping
// multiple validation errors returned by
// GetUserConversationListReply_Conversation.ValidateAll() if the designated
// constraints aren't met.
type GetUserConversationListReply_ConversationMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetUserConversationListReply_ConversationMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetUserConversationListReply_ConversationMultiError) AllErrors() []error { return m }

// GetUserConversationListReply_ConversationValidationError is the validation
// error returned by GetUserConversationListReply_Conversation.Validate if the
// designated constraints aren't met.
type GetUserConversationListReply_ConversationValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetUserConversationListReply_ConversationValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetUserConversationListReply_ConversationValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetUserConversationListReply_ConversationValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetUserConversationListReply_ConversationValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetUserConversationListReply_ConversationValidationError) ErrorName() string {
	return "GetUserConversationListReply_ConversationValidationError"
}

// Error satisfies the builtin error interface
func (e GetUserConversationListReply_ConversationValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetUserConversationListReply_Conversation.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetUserConversationListReply_ConversationValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetUserConversationListReply_ConversationValidationError{}
