// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.2
// 	protoc        v4.23.4
// source: ai/v1/ai.proto

package v1

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetConversationDetailRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Limit          int64  `protobuf:"varint,1,opt,name=limit,proto3" json:"limit"`
	Offset         int64  `protobuf:"varint,2,opt,name=offset,proto3" json:"offset"`
	ConversationId string `protobuf:"bytes,3,opt,name=conversationId,proto3" json:"conversationId"`
}

func (x *GetConversationDetailRequest) Reset() {
	*x = GetConversationDetailRequest{}
	mi := &file_ai_v1_ai_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetConversationDetailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetConversationDetailRequest) ProtoMessage() {}

func (x *GetConversationDetailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_ai_v1_ai_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetConversationDetailRequest.ProtoReflect.Descriptor instead.
func (*GetConversationDetailRequest) Descriptor() ([]byte, []int) {
	return file_ai_v1_ai_proto_rawDescGZIP(), []int{0}
}

func (x *GetConversationDetailRequest) GetLimit() int64 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *GetConversationDetailRequest) GetOffset() int64 {
	if x != nil {
		return x.Offset
	}
	return 0
}

func (x *GetConversationDetailRequest) GetConversationId() string {
	if x != nil {
		return x.ConversationId
	}
	return ""
}

type GetConversationDetailReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List  []*GetConversationDetailReply_Detail `protobuf:"bytes,1,rep,name=list,proto3" json:"list"`
	Total int64                                `protobuf:"varint,2,opt,name=total,proto3" json:"total"`
}

func (x *GetConversationDetailReply) Reset() {
	*x = GetConversationDetailReply{}
	mi := &file_ai_v1_ai_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetConversationDetailReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetConversationDetailReply) ProtoMessage() {}

func (x *GetConversationDetailReply) ProtoReflect() protoreflect.Message {
	mi := &file_ai_v1_ai_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetConversationDetailReply.ProtoReflect.Descriptor instead.
func (*GetConversationDetailReply) Descriptor() ([]byte, []int) {
	return file_ai_v1_ai_proto_rawDescGZIP(), []int{1}
}

func (x *GetConversationDetailReply) GetList() []*GetConversationDetailReply_Detail {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *GetConversationDetailReply) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

type GetModelListReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Models []*GetModelListReply_Model `protobuf:"bytes,1,rep,name=models,proto3" json:"models"`
}

func (x *GetModelListReply) Reset() {
	*x = GetModelListReply{}
	mi := &file_ai_v1_ai_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetModelListReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetModelListReply) ProtoMessage() {}

func (x *GetModelListReply) ProtoReflect() protoreflect.Message {
	mi := &file_ai_v1_ai_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetModelListReply.ProtoReflect.Descriptor instead.
func (*GetModelListReply) Descriptor() ([]byte, []int) {
	return file_ai_v1_ai_proto_rawDescGZIP(), []int{2}
}

func (x *GetModelListReply) GetModels() []*GetModelListReply_Model {
	if x != nil {
		return x.Models
	}
	return nil
}

type GetUserConversationListReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*GetUserConversationListReply_Conversation `protobuf:"bytes,1,rep,name=list,proto3" json:"list"`
}

func (x *GetUserConversationListReply) Reset() {
	*x = GetUserConversationListReply{}
	mi := &file_ai_v1_ai_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserConversationListReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserConversationListReply) ProtoMessage() {}

func (x *GetUserConversationListReply) ProtoReflect() protoreflect.Message {
	mi := &file_ai_v1_ai_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserConversationListReply.ProtoReflect.Descriptor instead.
func (*GetUserConversationListReply) Descriptor() ([]byte, []int) {
	return file_ai_v1_ai_proto_rawDescGZIP(), []int{3}
}

func (x *GetUserConversationListReply) GetList() []*GetUserConversationListReply_Conversation {
	if x != nil {
		return x.List
	}
	return nil
}

type GetConversationDetailReply_Detail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ConversationId string `protobuf:"bytes,1,opt,name=conversationId,proto3" json:"conversationId"`
	CreatedAt      uint64 `protobuf:"varint,2,opt,name=createdAt,proto3" json:"createdAt"`
	SendAt         uint64 `protobuf:"varint,3,opt,name=sendAt,proto3" json:"sendAt"`
	Model          string `protobuf:"bytes,5,opt,name=model,proto3" json:"model"`
	ConversateType string `protobuf:"bytes,6,opt,name=conversateType,proto3" json:"conversateType"`
	Content        string `protobuf:"bytes,7,opt,name=content,proto3" json:"content"`
}

func (x *GetConversationDetailReply_Detail) Reset() {
	*x = GetConversationDetailReply_Detail{}
	mi := &file_ai_v1_ai_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetConversationDetailReply_Detail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetConversationDetailReply_Detail) ProtoMessage() {}

func (x *GetConversationDetailReply_Detail) ProtoReflect() protoreflect.Message {
	mi := &file_ai_v1_ai_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetConversationDetailReply_Detail.ProtoReflect.Descriptor instead.
func (*GetConversationDetailReply_Detail) Descriptor() ([]byte, []int) {
	return file_ai_v1_ai_proto_rawDescGZIP(), []int{1, 0}
}

func (x *GetConversationDetailReply_Detail) GetConversationId() string {
	if x != nil {
		return x.ConversationId
	}
	return ""
}

func (x *GetConversationDetailReply_Detail) GetCreatedAt() uint64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *GetConversationDetailReply_Detail) GetSendAt() uint64 {
	if x != nil {
		return x.SendAt
	}
	return 0
}

func (x *GetConversationDetailReply_Detail) GetModel() string {
	if x != nil {
		return x.Model
	}
	return ""
}

func (x *GetConversationDetailReply_Detail) GetConversateType() string {
	if x != nil {
		return x.ConversateType
	}
	return ""
}

func (x *GetConversationDetailReply_Detail) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

type GetModelListReply_Model struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name     string `protobuf:"bytes,1,opt,name=name,proto3" json:"name"`
	Object   string `protobuf:"bytes,2,opt,name=object,proto3" json:"object"`
	Provider string `protobuf:"bytes,3,opt,name=provider,proto3" json:"provider"`
}

func (x *GetModelListReply_Model) Reset() {
	*x = GetModelListReply_Model{}
	mi := &file_ai_v1_ai_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetModelListReply_Model) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetModelListReply_Model) ProtoMessage() {}

func (x *GetModelListReply_Model) ProtoReflect() protoreflect.Message {
	mi := &file_ai_v1_ai_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetModelListReply_Model.ProtoReflect.Descriptor instead.
func (*GetModelListReply_Model) Descriptor() ([]byte, []int) {
	return file_ai_v1_ai_proto_rawDescGZIP(), []int{2, 0}
}

func (x *GetModelListReply_Model) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GetModelListReply_Model) GetObject() string {
	if x != nil {
		return x.Object
	}
	return ""
}

func (x *GetModelListReply_Model) GetProvider() string {
	if x != nil {
		return x.Provider
	}
	return ""
}

type GetUserConversationListReply_Conversation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ConversationId string `protobuf:"bytes,1,opt,name=conversationId,proto3" json:"conversationId"`
	CreatedAt      uint64 `protobuf:"varint,2,opt,name=createdAt,proto3" json:"createdAt"`
	UpdatedAt      uint64 `protobuf:"varint,3,opt,name=updatedAt,proto3" json:"updatedAt"`
	Title          string `protobuf:"bytes,4,opt,name=title,proto3" json:"title"`
	Model          string `protobuf:"bytes,5,opt,name=model,proto3" json:"model"`
	ConversateType string `protobuf:"bytes,6,opt,name=conversateType,proto3" json:"conversateType"`
}

func (x *GetUserConversationListReply_Conversation) Reset() {
	*x = GetUserConversationListReply_Conversation{}
	mi := &file_ai_v1_ai_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserConversationListReply_Conversation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserConversationListReply_Conversation) ProtoMessage() {}

func (x *GetUserConversationListReply_Conversation) ProtoReflect() protoreflect.Message {
	mi := &file_ai_v1_ai_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserConversationListReply_Conversation.ProtoReflect.Descriptor instead.
func (*GetUserConversationListReply_Conversation) Descriptor() ([]byte, []int) {
	return file_ai_v1_ai_proto_rawDescGZIP(), []int{3, 0}
}

func (x *GetUserConversationListReply_Conversation) GetConversationId() string {
	if x != nil {
		return x.ConversationId
	}
	return ""
}

func (x *GetUserConversationListReply_Conversation) GetCreatedAt() uint64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *GetUserConversationListReply_Conversation) GetUpdatedAt() uint64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

func (x *GetUserConversationListReply_Conversation) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *GetUserConversationListReply_Conversation) GetModel() string {
	if x != nil {
		return x.Model
	}
	return ""
}

func (x *GetUserConversationListReply_Conversation) GetConversateType() string {
	if x != nil {
		return x.ConversateType
	}
	return ""
}

var File_ai_v1_ai_proto protoreflect.FileDescriptor

var file_ai_v1_ai_proto_rawDesc = []byte{
	0x0a, 0x0e, 0x61, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x05, 0x61, 0x69, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f,
	0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x99, 0x01, 0x0a, 0x1c,
	0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x05,
	0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x28, 0x0a, 0x06, 0x6f,
	0x66, 0x66, 0x73, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x10, 0xfa, 0x42, 0x0d,
	0x22, 0x0b, 0x20, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x01, 0x52, 0x06, 0x6f,
	0x66, 0x66, 0x73, 0x65, 0x74, 0x12, 0x30, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa,
	0x42, 0x05, 0x72, 0x03, 0x98, 0x01, 0x38, 0x52, 0x0e, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0xb1, 0x02, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x43,
	0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x3c, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x61, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x04,
	0x6c, 0x69, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x1a, 0xbe, 0x01, 0x0a, 0x06, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x26, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x63,
	0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1c, 0x0a,
	0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x73,
	0x65, 0x6e, 0x64, 0x41, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x73, 0x65, 0x6e,
	0x64, 0x41, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x26, 0x0a, 0x0e, 0x63, 0x6f, 0x6e,
	0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0e, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x22, 0x9c, 0x01, 0x0a, 0x11,
	0x47, 0x65, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x12, 0x36, 0x0a, 0x06, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x1e, 0x2e, 0x61, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x52, 0x06, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x1a, 0x4f, 0x0a, 0x05, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x1a,
	0x0a, 0x08, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x22, 0xad, 0x02, 0x0a, 0x1c, 0x47,
	0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x44, 0x0a, 0x04, 0x6c,
	0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x61, 0x69, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x43,
	0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x04, 0x6c, 0x69, 0x73,
	0x74, 0x1a, 0xc6, 0x01, 0x0a, 0x0c, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x26, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x63, 0x6f, 0x6e, 0x76,
	0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x64, 0x41, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x14, 0x0a, 0x05,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x12, 0x26, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x63, 0x6f, 0x6e, 0x76,
	0x65, 0x72, 0x73, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x32, 0x89, 0x03, 0x0a, 0x02, 0x41,
	0x69, 0x12, 0x68, 0x0a, 0x0c, 0x47, 0x65, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4c, 0x69, 0x73,
	0x74, 0x12, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x18, 0x2e, 0x61, 0x69, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x22, 0x26, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x20, 0x12, 0x1e, 0x2f, 0x76, 0x31,
	0x2f, 0x61, 0x69, 0x2f, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2d, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x85, 0x01, 0x0a, 0x17,
	0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a,
	0x23, 0x2e, 0x61, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x43,
	0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x22, 0x2d, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x27, 0x12, 0x25, 0x2f, 0x76,
	0x31, 0x2f, 0x61, 0x69, 0x2f, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2f, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2d, 0x6c,
	0x69, 0x73, 0x74, 0x12, 0x90, 0x01, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x76, 0x65,
	0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x23, 0x2e,
	0x61, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x21, 0x2e, 0x61, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x6f,
	0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x2f, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x29, 0x12, 0x27, 0x2f,
	0x76, 0x31, 0x2f, 0x61, 0x69, 0x2f, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2f, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2d,
	0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x42, 0x39, 0x5a, 0x37, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62,
	0x2e, 0x69, 0x6e, 0x74, 0x73, 0x69, 0x67, 0x2e, 0x6e, 0x65, 0x74, 0x2f, 0x4e, 0x4f, 0x43, 0x2f,
	0x63, 0x74, 0x6d, 0x2f, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x62, 0x61, 0x63,
	0x6b, 0x65, 0x6e, 0x64, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x69, 0x2f, 0x76, 0x31, 0x3b, 0x76,
	0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_ai_v1_ai_proto_rawDescOnce sync.Once
	file_ai_v1_ai_proto_rawDescData = file_ai_v1_ai_proto_rawDesc
)

func file_ai_v1_ai_proto_rawDescGZIP() []byte {
	file_ai_v1_ai_proto_rawDescOnce.Do(func() {
		file_ai_v1_ai_proto_rawDescData = protoimpl.X.CompressGZIP(file_ai_v1_ai_proto_rawDescData)
	})
	return file_ai_v1_ai_proto_rawDescData
}

var file_ai_v1_ai_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_ai_v1_ai_proto_goTypes = []any{
	(*GetConversationDetailRequest)(nil),              // 0: ai.v1.GetConversationDetailRequest
	(*GetConversationDetailReply)(nil),                // 1: ai.v1.GetConversationDetailReply
	(*GetModelListReply)(nil),                         // 2: ai.v1.GetModelListReply
	(*GetUserConversationListReply)(nil),              // 3: ai.v1.GetUserConversationListReply
	(*GetConversationDetailReply_Detail)(nil),         // 4: ai.v1.GetConversationDetailReply.Detail
	(*GetModelListReply_Model)(nil),                   // 5: ai.v1.GetModelListReply.Model
	(*GetUserConversationListReply_Conversation)(nil), // 6: ai.v1.GetUserConversationListReply.Conversation
	(*emptypb.Empty)(nil),                             // 7: google.protobuf.Empty
}
var file_ai_v1_ai_proto_depIdxs = []int32{
	4, // 0: ai.v1.GetConversationDetailReply.list:type_name -> ai.v1.GetConversationDetailReply.Detail
	5, // 1: ai.v1.GetModelListReply.models:type_name -> ai.v1.GetModelListReply.Model
	6, // 2: ai.v1.GetUserConversationListReply.list:type_name -> ai.v1.GetUserConversationListReply.Conversation
	7, // 3: ai.v1.Ai.GetModelList:input_type -> google.protobuf.Empty
	7, // 4: ai.v1.Ai.GetUserConversationList:input_type -> google.protobuf.Empty
	0, // 5: ai.v1.Ai.GetConversationDetail:input_type -> ai.v1.GetConversationDetailRequest
	2, // 6: ai.v1.Ai.GetModelList:output_type -> ai.v1.GetModelListReply
	3, // 7: ai.v1.Ai.GetUserConversationList:output_type -> ai.v1.GetUserConversationListReply
	1, // 8: ai.v1.Ai.GetConversationDetail:output_type -> ai.v1.GetConversationDetailReply
	6, // [6:9] is the sub-list for method output_type
	3, // [3:6] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_ai_v1_ai_proto_init() }
func file_ai_v1_ai_proto_init() {
	if File_ai_v1_ai_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_ai_v1_ai_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_ai_v1_ai_proto_goTypes,
		DependencyIndexes: file_ai_v1_ai_proto_depIdxs,
		MessageInfos:      file_ai_v1_ai_proto_msgTypes,
	}.Build()
	File_ai_v1_ai_proto = out.File
	file_ai_v1_ai_proto_rawDesc = nil
	file_ai_v1_ai_proto_goTypes = nil
	file_ai_v1_ai_proto_depIdxs = nil
}
