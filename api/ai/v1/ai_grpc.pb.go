// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: ai/v1/ai.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Ai_GetModelList_FullMethodName            = "/ai.v1.Ai/GetModelList"
	Ai_GetUserConversationList_FullMethodName = "/ai.v1.Ai/GetUserConversationList"
	Ai_GetConversationDetail_FullMethodName   = "/ai.v1.Ai/GetConversationDetail"
)

// AiClient is the client API for Ai service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AiClient interface {
	GetModelList(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*GetModelListReply, error)
	GetUserConversationList(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*GetUserConversationListReply, error)
	GetConversationDetail(ctx context.Context, in *GetConversationDetailRequest, opts ...grpc.CallOption) (*GetConversationDetailReply, error)
}

type aiClient struct {
	cc grpc.ClientConnInterface
}

func NewAiClient(cc grpc.ClientConnInterface) AiClient {
	return &aiClient{cc}
}

func (c *aiClient) GetModelList(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*GetModelListReply, error) {
	out := new(GetModelListReply)
	err := c.cc.Invoke(ctx, Ai_GetModelList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiClient) GetUserConversationList(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*GetUserConversationListReply, error) {
	out := new(GetUserConversationListReply)
	err := c.cc.Invoke(ctx, Ai_GetUserConversationList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiClient) GetConversationDetail(ctx context.Context, in *GetConversationDetailRequest, opts ...grpc.CallOption) (*GetConversationDetailReply, error) {
	out := new(GetConversationDetailReply)
	err := c.cc.Invoke(ctx, Ai_GetConversationDetail_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AiServer is the server API for Ai service.
// All implementations must embed UnimplementedAiServer
// for forward compatibility
type AiServer interface {
	GetModelList(context.Context, *emptypb.Empty) (*GetModelListReply, error)
	GetUserConversationList(context.Context, *emptypb.Empty) (*GetUserConversationListReply, error)
	GetConversationDetail(context.Context, *GetConversationDetailRequest) (*GetConversationDetailReply, error)
	mustEmbedUnimplementedAiServer()
}

// UnimplementedAiServer must be embedded to have forward compatible implementations.
type UnimplementedAiServer struct {
}

func (UnimplementedAiServer) GetModelList(context.Context, *emptypb.Empty) (*GetModelListReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetModelList not implemented")
}
func (UnimplementedAiServer) GetUserConversationList(context.Context, *emptypb.Empty) (*GetUserConversationListReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserConversationList not implemented")
}
func (UnimplementedAiServer) GetConversationDetail(context.Context, *GetConversationDetailRequest) (*GetConversationDetailReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetConversationDetail not implemented")
}
func (UnimplementedAiServer) mustEmbedUnimplementedAiServer() {}

// UnsafeAiServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AiServer will
// result in compilation errors.
type UnsafeAiServer interface {
	mustEmbedUnimplementedAiServer()
}

func RegisterAiServer(s grpc.ServiceRegistrar, srv AiServer) {
	s.RegisterService(&Ai_ServiceDesc, srv)
}

func _Ai_GetModelList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AiServer).GetModelList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Ai_GetModelList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AiServer).GetModelList(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ai_GetUserConversationList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AiServer).GetUserConversationList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Ai_GetUserConversationList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AiServer).GetUserConversationList(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ai_GetConversationDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetConversationDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AiServer).GetConversationDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Ai_GetConversationDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AiServer).GetConversationDetail(ctx, req.(*GetConversationDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Ai_ServiceDesc is the grpc.ServiceDesc for Ai service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Ai_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "ai.v1.Ai",
	HandlerType: (*AiServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetModelList",
			Handler:    _Ai_GetModelList_Handler,
		},
		{
			MethodName: "GetUserConversationList",
			Handler:    _Ai_GetUserConversationList_Handler,
		},
		{
			MethodName: "GetConversationDetail",
			Handler:    _Ai_GetConversationDetail_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "ai/v1/ai.proto",
}
