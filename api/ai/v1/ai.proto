syntax = "proto3";

package ai.v1;

import "google/api/annotations.proto";
option go_package = "gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/api/ai/v1;v1";
import "validate/validate.proto";

import "google/protobuf/empty.proto";
// import "validate/validate.proto";


service Ai {
  rpc GetModelList (google.protobuf.Empty) returns (GetModelListReply) {
    option (google.api.http) = {
      get: "/v1/ai/conversation/model-list"
    };
  }

  rpc GetUserConversationList (google.protobuf.Empty) returns (GetUserConversationListReply) {
    option (google.api.http) = {
      get: "/v1/ai/conversation/conversation-list"
    };
  }

  rpc GetConversationDetail (GetConversationDetailRequest) returns (GetConversationDetailReply) {
    option (google.api.http) = {
      get: "/v1/ai/conversation/conversation-detail"
    };
  }
}

message GetConversationDetailRequest {
  int64 limit = 1 [(validate.rules).int64.gt = 0];
  int64 offset = 2 [(validate.rules).int64.gt = -1];
  string conversationId = 3 [(validate.rules).string.len = 56];
}

message GetConversationDetailReply {
  message Detail {
    string  conversationId = 1;
    uint64 createdAt = 2;
    uint64 sendAt = 3;
    string  model =5;
    string conversateType = 6;
    string content = 7;
  }
  repeated Detail  list = 1;
  int64 total = 2;
}

message GetModelListReply {
  message Model {
    string name = 1;
    string object = 2;
    string provider = 3;
  }
  repeated Model models = 1;
}

message GetUserConversationListReply {
  message Conversation {
    string  conversationId = 1;
    uint64 createdAt = 2;
    uint64 updatedAt = 3;
    string  title  = 4;
    string  model =5;
    string conversateType = 6;
  }
  repeated Conversation  list = 1;
}