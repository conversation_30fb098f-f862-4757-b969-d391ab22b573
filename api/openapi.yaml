# Generated with protoc-gen-openapi
# https://github.com/google/gnostic/tree/master/cmd/protoc-gen-openapi

openapi: 3.0.3
info:
    title: User API
    description: User智能堡垒机终端
    version: 0.0.1
paths:
    /v1/user:
        get:
            tags:
                - User
            description: Sends a greeting
            operationId: User_SayHello
            parameters:
                - name: name
                  in: query
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/HelloReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/user/check-device:
        post:
            tags:
                - User
            description: 校验用户设备
            operationId: User_CheckUserDevice
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/CheckUserDeviceRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/CheckUserDeviceReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/user/info:
        get:
            tags:
                - User
            description: 获取用户信息
            operationId: User_GetUserByCtx
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/GetUserByCtxReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/user/login-email:
        post:
            tags:
                - User
            description: 用户登录 - 邮箱验证码
            operationId: User_UserLoginEmailVerification
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/UserLoginEmailVerificationRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/UserLoginReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/user/login-email/send-code:
        post:
            tags:
                - User
            description: 用户登录 - 发送邮箱验证码
            operationId: User_UserLoginEmailSendCode
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/UserLoginEmailSendCodeRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/CommonReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/user/login-out:
        get:
            tags:
                - User
            description: 用户注销
            operationId: User_UserLoginOut
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/CommonReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/user/login-pwd:
        post:
            tags:
                - User
            description: 用户登录 - 账号密码
            operationId: User_UserLoginPwd
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/UserLoginPwdRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/UserLoginReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/user/login-sso:
        get:
            tags:
                - User
            description: 用户登录 - sso
            operationId: User_UserLoginSSO
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/UserLoginReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/user/quick-command:
        get:
            tags:
                - User
            operationId: User_ListUserQuickCommand
            parameters:
                - name: pageNo
                  in: query
                  schema:
                    type: integer
                    format: int32
                - name: pageSize
                  in: query
                  schema:
                    type: integer
                    format: int32
                - name: searchText
                  in: query
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ListUserQuickCommandReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
        put:
            tags:
                - User
            operationId: User_UpdateUserQuickCommand
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/UpdateUserQuickCommandRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/CommonReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
        post:
            tags:
                - User
            operationId: User_CreateUserQuickCommand
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/CreateUserQuickCommandRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/CommonReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
        delete:
            tags:
                - User
            operationId: User_DeleteUserQuickCommand
            parameters:
                - name: id
                  in: query
                  schema:
                    type: integer
                    format: int64
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/CommonReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/user/quick-command/info:
        get:
            tags:
                - User
            operationId: User_GetUserQuickCommand
            parameters:
                - name: id
                  in: query
                  schema:
                    type: integer
                    format: int64
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/GetUserQuickCommandReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/user/term-config:
        get:
            tags:
                - User
            description: 获取用户配置
            operationId: User_GetUserTermConfig
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/GetUserTermConfigReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
        put:
            tags:
                - User
            description: 更新用户配置
            operationId: User_UpdateUserTermConfig
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/UpdateUserTermConfigRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/CommonReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/user/users:
        get:
            tags:
                - User
            description: 获取用户信息
            operationId: User_GetUserInfoByEmail
            parameters:
                - name: email
                  in: query
                  schema:
                    type: string
                - name: organizationId
                  in: query
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/GetUserInfoByEmailReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
components:
    schemas:
        CheckUserDeviceReply:
            type: object
            properties:
                ip:
                    type: string
                macAddress:
                    type: string
                isOfficeDevice:
                    type: boolean
        CheckUserDeviceRequest:
            type: object
            properties:
                ip:
                    type: string
                macAddress:
                    type: string
        CommonReply:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                message:
                    type: string
        CreateUserQuickCommandRequest:
            type: object
            properties:
                alias:
                    type: string
                command:
                    type: string
                comment:
                    type: string
        GetUserByCtxReply:
            type: object
            properties:
                uid:
                    type: integer
                    format: int64
                email:
                    type: string
                name:
                    type: string
                avatar:
                    type: string
                enName:
                    type: string
                mobile:
                    type: string
                secondaryOrganization:
                    type: string
                tertiaryOrganization:
                    type: string
                team:
                    type: string
                registrationType:
                    type: integer
                    format: int64
                ip:
                    type: string
                macAddress:
                    type: string
        GetUserInfoByEmailReply:
            type: object
            properties:
                data:
                    type: array
                    items:
                        $ref: '#/components/schemas/GetUserInfoByEmailReply_User'
        GetUserInfoByEmailReply_User:
            type: object
            properties:
                id:
                    type: integer
                    format: int32
                name:
                    type: string
                email:
                    type: string
                organizationId:
                    type: string
                uid:
                    type: integer
                    format: int64
        GetUserQuickCommandReply:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                alias:
                    type: string
                command:
                    type: string
                comment:
                    type: string
        GetUserTermConfigReply:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                uid:
                    type: integer
                    format: int64
                fontSize:
                    type: integer
                    format: int64
                cursorStyle:
                    type: string
                scrollBack:
                    type: integer
                    format: int64
                language:
                    type: string
                aliasStatus:
                    type: integer
                    format: int64
                autoCompleteStatus:
                    type: integer
                    format: int64
                quickVimStatus:
                    type: integer
                    format: int64
                commonVimStatus:
                    type: integer
                    format: int64
                highlightStatus:
                    type: integer
                    format: int64
        GoogleProtobufAny:
            type: object
            properties:
                '@type':
                    type: string
                    description: The type of the serialized message.
            additionalProperties: true
            description: Contains an arbitrary serialized message along with a @type that describes the type of the serialized message.
        HelloReply:
            type: object
            properties:
                message:
                    type: string
        ListUserQuickCommandReply:
            type: object
            properties:
                data:
                    type: array
                    items:
                        $ref: '#/components/schemas/UserQuickCommand'
                pageNo:
                    type: integer
                    format: int32
                pageSize:
                    type: integer
                    format: int32
                totalCount:
                    type: integer
                    format: int32
                totalPage:
                    type: integer
                    format: int32
        Status:
            type: object
            properties:
                code:
                    type: integer
                    description: The status code, which should be an enum value of [google.rpc.Code][google.rpc.Code].
                    format: int32
                message:
                    type: string
                    description: A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the [google.rpc.Status.details][google.rpc.Status.details] field, or localized by the client.
                details:
                    type: array
                    items:
                        $ref: '#/components/schemas/GoogleProtobufAny'
                    description: A list of messages that carry the error details.  There is a common set of message types for APIs to use.
            description: 'The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).'
        UpdateUserQuickCommandRequest:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                alias:
                    type: string
                command:
                    type: string
                comment:
                    type: string
        UpdateUserTermConfigRequest:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                uid:
                    type: integer
                    format: int64
                fontSize:
                    type: integer
                    format: int64
                cursorStyle:
                    type: string
                scrollBack:
                    type: integer
                    format: int64
                language:
                    type: string
                aliasStatus:
                    type: integer
                    format: int64
                autoCompleteStatus:
                    type: integer
                    format: int64
                quickVimStatus:
                    type: integer
                    format: int64
                commonVimStatus:
                    type: integer
                    format: int64
                highlightStatus:
                    type: integer
                    format: int64
        UserLoginEmailSendCodeRequest:
            type: object
            properties:
                email:
                    type: string
        UserLoginEmailVerificationRequest:
            type: object
            properties:
                email:
                    type: string
                code:
                    type: string
        UserLoginPwdRequest:
            type: object
            properties:
                username:
                    type: string
                password:
                    type: string
        UserLoginReply:
            type: object
            properties:
                token:
                    type: string
                name:
                    type: string
                avatar:
                    type: string
                email:
                    type: string
                uid:
                    type: integer
                    format: int64
                registrationType:
                    type: integer
                    format: int64
        UserQuickCommand:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                alias:
                    type: string
                command:
                    type: string
                comment:
                    type: string
tags:
    - name: User
