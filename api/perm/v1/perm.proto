syntax = "proto3";

package perm.v1;

import "google/api/annotations.proto";
option go_package = "gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/api/perm/v1;v1";
option java_multiple_files = true;
option java_package = "dev.kratos.api.perm.v1";
option java_outer_classname = "PermProtoV1";

// The greeting service definition.
service Perm {
  // Sends a greeting
  rpc CreateUserAssetPermission (CreateUserAssetPermissionRequest) returns (CommonReply) {
    option (google.api.http) = {
      post: "/v1/perm/create"
      body: "*"
    };
  }
  rpc GetUserAssetPermissionList (GetUserAssetPermissionListRequest) returns (GetUserAssetPermissionListReply) {
    option (google.api.http) = {
      get: "/v1/perm/list"
    };
  }
}

message CreateUserAssetPermissionRequest {
  string email = 1;
  repeated string  ipList = 2;
  string role = 3;
  bool isExpired = 4;
  int64 validHours = 5;
  string organizationId = 6;
  string creator = 7;
  int64 uid = 8;
}


message CommonReply {
  string message = 1;
}


message GetUserAssetPermissionListRequest {
  int64 pageNo = 1;
  int64 pageSize = 2;
  string searchKey = 3;
  string ip = 4;
}

message GetUserAssetPermissionListReply {
  message PermissionAssetList {
    int64 id = 1;
    string ip = 2;
    string role = 3;
    string hostname = 4;
    string expireTime = 5;
    int64 status = 6;
    string createdAt = 7;
  }
  message User {
    int64 uid = 1;
    string email = 2;
    string user = 3;
    string organizationId = 4;
    string createdAt = 5;
    repeated PermissionAssetList permissionAssetList = 6;
  }
  repeated User data = 1;
  int64 pageNo = 2;
  int64 pageSize = 3;
  int64 totalCount = 4;
  int64 totalPage = 5;
}