// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.6.3
// - protoc             v4.23.4
// source: perm/v1/perm.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationPermCreateUserAssetPermission = "/perm.v1.Perm/CreateUserAssetPermission"
const OperationPermGetUserAssetPermissionList = "/perm.v1.Perm/GetUserAssetPermissionList"

type PermHTTPServer interface {
	// CreateUserAssetPermission Sends a greeting
	CreateUserAssetPermission(context.Context, *CreateUserAssetPermissionRequest) (*CommonReply, error)
	GetUserAssetPermissionList(context.Context, *GetUserAssetPermissionListRequest) (*GetUserAssetPermissionListReply, error)
}

func RegisterPermHTTPServer(s *http.Server, srv PermHTTPServer) {
	r := s.Route("/")
	r.POST("/v1/perm/create", _Perm_CreateUserAssetPermission0_HTTP_Handler(srv))
	r.GET("/v1/perm/list", _Perm_GetUserAssetPermissionList0_HTTP_Handler(srv))
}

func _Perm_CreateUserAssetPermission0_HTTP_Handler(srv PermHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateUserAssetPermissionRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationPermCreateUserAssetPermission)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateUserAssetPermission(ctx, req.(*CreateUserAssetPermissionRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CommonReply)
		return ctx.Result(200, reply)
	}
}

func _Perm_GetUserAssetPermissionList0_HTTP_Handler(srv PermHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetUserAssetPermissionListRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationPermGetUserAssetPermissionList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetUserAssetPermissionList(ctx, req.(*GetUserAssetPermissionListRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetUserAssetPermissionListReply)
		return ctx.Result(200, reply)
	}
}

type PermHTTPClient interface {
	CreateUserAssetPermission(ctx context.Context, req *CreateUserAssetPermissionRequest, opts ...http.CallOption) (rsp *CommonReply, err error)
	GetUserAssetPermissionList(ctx context.Context, req *GetUserAssetPermissionListRequest, opts ...http.CallOption) (rsp *GetUserAssetPermissionListReply, err error)
}

type PermHTTPClientImpl struct {
	cc *http.Client
}

func NewPermHTTPClient(client *http.Client) PermHTTPClient {
	return &PermHTTPClientImpl{client}
}

func (c *PermHTTPClientImpl) CreateUserAssetPermission(ctx context.Context, in *CreateUserAssetPermissionRequest, opts ...http.CallOption) (*CommonReply, error) {
	var out CommonReply
	pattern := "/v1/perm/create"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationPermCreateUserAssetPermission))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *PermHTTPClientImpl) GetUserAssetPermissionList(ctx context.Context, in *GetUserAssetPermissionListRequest, opts ...http.CallOption) (*GetUserAssetPermissionListReply, error) {
	var out GetUserAssetPermissionListReply
	pattern := "/v1/perm/list"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationPermGetUserAssetPermissionList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}
