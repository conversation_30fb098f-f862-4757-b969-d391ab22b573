// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: perm/v1/perm.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Perm_CreateUserAssetPermission_FullMethodName  = "/perm.v1.Perm/CreateUserAssetPermission"
	Perm_GetUserAssetPermissionList_FullMethodName = "/perm.v1.Perm/GetUserAssetPermissionList"
)

// PermClient is the client API for Perm service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PermClient interface {
	// Sends a greeting
	CreateUserAssetPermission(ctx context.Context, in *CreateUserAssetPermissionRequest, opts ...grpc.CallOption) (*CommonReply, error)
	GetUserAssetPermissionList(ctx context.Context, in *GetUserAssetPermissionListRequest, opts ...grpc.CallOption) (*GetUserAssetPermissionListReply, error)
}

type permClient struct {
	cc grpc.ClientConnInterface
}

func NewPermClient(cc grpc.ClientConnInterface) PermClient {
	return &permClient{cc}
}

func (c *permClient) CreateUserAssetPermission(ctx context.Context, in *CreateUserAssetPermissionRequest, opts ...grpc.CallOption) (*CommonReply, error) {
	out := new(CommonReply)
	err := c.cc.Invoke(ctx, Perm_CreateUserAssetPermission_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *permClient) GetUserAssetPermissionList(ctx context.Context, in *GetUserAssetPermissionListRequest, opts ...grpc.CallOption) (*GetUserAssetPermissionListReply, error) {
	out := new(GetUserAssetPermissionListReply)
	err := c.cc.Invoke(ctx, Perm_GetUserAssetPermissionList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PermServer is the server API for Perm service.
// All implementations must embed UnimplementedPermServer
// for forward compatibility
type PermServer interface {
	// Sends a greeting
	CreateUserAssetPermission(context.Context, *CreateUserAssetPermissionRequest) (*CommonReply, error)
	GetUserAssetPermissionList(context.Context, *GetUserAssetPermissionListRequest) (*GetUserAssetPermissionListReply, error)
	mustEmbedUnimplementedPermServer()
}

// UnimplementedPermServer must be embedded to have forward compatible implementations.
type UnimplementedPermServer struct {
}

func (UnimplementedPermServer) CreateUserAssetPermission(context.Context, *CreateUserAssetPermissionRequest) (*CommonReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateUserAssetPermission not implemented")
}
func (UnimplementedPermServer) GetUserAssetPermissionList(context.Context, *GetUserAssetPermissionListRequest) (*GetUserAssetPermissionListReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserAssetPermissionList not implemented")
}
func (UnimplementedPermServer) mustEmbedUnimplementedPermServer() {}

// UnsafePermServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PermServer will
// result in compilation errors.
type UnsafePermServer interface {
	mustEmbedUnimplementedPermServer()
}

func RegisterPermServer(s grpc.ServiceRegistrar, srv PermServer) {
	s.RegisterService(&Perm_ServiceDesc, srv)
}

func _Perm_CreateUserAssetPermission_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateUserAssetPermissionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PermServer).CreateUserAssetPermission(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Perm_CreateUserAssetPermission_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PermServer).CreateUserAssetPermission(ctx, req.(*CreateUserAssetPermissionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Perm_GetUserAssetPermissionList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserAssetPermissionListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PermServer).GetUserAssetPermissionList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Perm_GetUserAssetPermissionList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PermServer).GetUserAssetPermissionList(ctx, req.(*GetUserAssetPermissionListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Perm_ServiceDesc is the grpc.ServiceDesc for Perm service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Perm_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "perm.v1.Perm",
	HandlerType: (*PermServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateUserAssetPermission",
			Handler:    _Perm_CreateUserAssetPermission_Handler,
		},
		{
			MethodName: "GetUserAssetPermissionList",
			Handler:    _Perm_GetUserAssetPermissionList_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "perm/v1/perm.proto",
}
