// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v4.23.4
// source: perm/v1/perm.proto

package v1

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CreateUserAssetPermissionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Email          string   `protobuf:"bytes,1,opt,name=email,proto3" json:"email"`
	IpList         []string `protobuf:"bytes,2,rep,name=ipList,proto3" json:"ipList"`
	Role           string   `protobuf:"bytes,3,opt,name=role,proto3" json:"role"`
	IsExpired      bool     `protobuf:"varint,4,opt,name=isExpired,proto3" json:"isExpired"`
	ValidHours     int64    `protobuf:"varint,5,opt,name=validHours,proto3" json:"validHours"`
	OrganizationId string   `protobuf:"bytes,6,opt,name=organizationId,proto3" json:"organizationId"`
	Creator        string   `protobuf:"bytes,7,opt,name=creator,proto3" json:"creator"`
	Uid            int64    `protobuf:"varint,8,opt,name=uid,proto3" json:"uid"`
}

func (x *CreateUserAssetPermissionRequest) Reset() {
	*x = CreateUserAssetPermissionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_perm_v1_perm_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateUserAssetPermissionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateUserAssetPermissionRequest) ProtoMessage() {}

func (x *CreateUserAssetPermissionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_perm_v1_perm_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateUserAssetPermissionRequest.ProtoReflect.Descriptor instead.
func (*CreateUserAssetPermissionRequest) Descriptor() ([]byte, []int) {
	return file_perm_v1_perm_proto_rawDescGZIP(), []int{0}
}

func (x *CreateUserAssetPermissionRequest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *CreateUserAssetPermissionRequest) GetIpList() []string {
	if x != nil {
		return x.IpList
	}
	return nil
}

func (x *CreateUserAssetPermissionRequest) GetRole() string {
	if x != nil {
		return x.Role
	}
	return ""
}

func (x *CreateUserAssetPermissionRequest) GetIsExpired() bool {
	if x != nil {
		return x.IsExpired
	}
	return false
}

func (x *CreateUserAssetPermissionRequest) GetValidHours() int64 {
	if x != nil {
		return x.ValidHours
	}
	return 0
}

func (x *CreateUserAssetPermissionRequest) GetOrganizationId() string {
	if x != nil {
		return x.OrganizationId
	}
	return ""
}

func (x *CreateUserAssetPermissionRequest) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *CreateUserAssetPermissionRequest) GetUid() int64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

type CommonReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Message string `protobuf:"bytes,1,opt,name=message,proto3" json:"message"`
}

func (x *CommonReply) Reset() {
	*x = CommonReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_perm_v1_perm_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CommonReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommonReply) ProtoMessage() {}

func (x *CommonReply) ProtoReflect() protoreflect.Message {
	mi := &file_perm_v1_perm_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommonReply.ProtoReflect.Descriptor instead.
func (*CommonReply) Descriptor() ([]byte, []int) {
	return file_perm_v1_perm_proto_rawDescGZIP(), []int{1}
}

func (x *CommonReply) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type GetUserAssetPermissionListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PageNo    int64  `protobuf:"varint,1,opt,name=pageNo,proto3" json:"pageNo"`
	PageSize  int64  `protobuf:"varint,2,opt,name=pageSize,proto3" json:"pageSize"`
	SearchKey string `protobuf:"bytes,3,opt,name=searchKey,proto3" json:"searchKey"`
	Ip        string `protobuf:"bytes,4,opt,name=ip,proto3" json:"ip"`
}

func (x *GetUserAssetPermissionListRequest) Reset() {
	*x = GetUserAssetPermissionListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_perm_v1_perm_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserAssetPermissionListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserAssetPermissionListRequest) ProtoMessage() {}

func (x *GetUserAssetPermissionListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_perm_v1_perm_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserAssetPermissionListRequest.ProtoReflect.Descriptor instead.
func (*GetUserAssetPermissionListRequest) Descriptor() ([]byte, []int) {
	return file_perm_v1_perm_proto_rawDescGZIP(), []int{2}
}

func (x *GetUserAssetPermissionListRequest) GetPageNo() int64 {
	if x != nil {
		return x.PageNo
	}
	return 0
}

func (x *GetUserAssetPermissionListRequest) GetPageSize() int64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *GetUserAssetPermissionListRequest) GetSearchKey() string {
	if x != nil {
		return x.SearchKey
	}
	return ""
}

func (x *GetUserAssetPermissionListRequest) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

type GetUserAssetPermissionListReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data       []*GetUserAssetPermissionListReply_User `protobuf:"bytes,1,rep,name=data,proto3" json:"data"`
	PageNo     int64                                   `protobuf:"varint,2,opt,name=pageNo,proto3" json:"pageNo"`
	PageSize   int64                                   `protobuf:"varint,3,opt,name=pageSize,proto3" json:"pageSize"`
	TotalCount int64                                   `protobuf:"varint,4,opt,name=totalCount,proto3" json:"totalCount"`
	TotalPage  int64                                   `protobuf:"varint,5,opt,name=totalPage,proto3" json:"totalPage"`
}

func (x *GetUserAssetPermissionListReply) Reset() {
	*x = GetUserAssetPermissionListReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_perm_v1_perm_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserAssetPermissionListReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserAssetPermissionListReply) ProtoMessage() {}

func (x *GetUserAssetPermissionListReply) ProtoReflect() protoreflect.Message {
	mi := &file_perm_v1_perm_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserAssetPermissionListReply.ProtoReflect.Descriptor instead.
func (*GetUserAssetPermissionListReply) Descriptor() ([]byte, []int) {
	return file_perm_v1_perm_proto_rawDescGZIP(), []int{3}
}

func (x *GetUserAssetPermissionListReply) GetData() []*GetUserAssetPermissionListReply_User {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *GetUserAssetPermissionListReply) GetPageNo() int64 {
	if x != nil {
		return x.PageNo
	}
	return 0
}

func (x *GetUserAssetPermissionListReply) GetPageSize() int64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *GetUserAssetPermissionListReply) GetTotalCount() int64 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

func (x *GetUserAssetPermissionListReply) GetTotalPage() int64 {
	if x != nil {
		return x.TotalPage
	}
	return 0
}

type GetUserAssetPermissionListReply_PermissionAssetList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	Ip         string `protobuf:"bytes,2,opt,name=ip,proto3" json:"ip"`
	Role       string `protobuf:"bytes,3,opt,name=role,proto3" json:"role"`
	Hostname   string `protobuf:"bytes,4,opt,name=hostname,proto3" json:"hostname"`
	ExpireTime string `protobuf:"bytes,5,opt,name=expireTime,proto3" json:"expireTime"`
	Status     int64  `protobuf:"varint,6,opt,name=status,proto3" json:"status"`
	CreatedAt  string `protobuf:"bytes,7,opt,name=createdAt,proto3" json:"createdAt"`
}

func (x *GetUserAssetPermissionListReply_PermissionAssetList) Reset() {
	*x = GetUserAssetPermissionListReply_PermissionAssetList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_perm_v1_perm_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserAssetPermissionListReply_PermissionAssetList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserAssetPermissionListReply_PermissionAssetList) ProtoMessage() {}

func (x *GetUserAssetPermissionListReply_PermissionAssetList) ProtoReflect() protoreflect.Message {
	mi := &file_perm_v1_perm_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserAssetPermissionListReply_PermissionAssetList.ProtoReflect.Descriptor instead.
func (*GetUserAssetPermissionListReply_PermissionAssetList) Descriptor() ([]byte, []int) {
	return file_perm_v1_perm_proto_rawDescGZIP(), []int{3, 0}
}

func (x *GetUserAssetPermissionListReply_PermissionAssetList) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetUserAssetPermissionListReply_PermissionAssetList) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *GetUserAssetPermissionListReply_PermissionAssetList) GetRole() string {
	if x != nil {
		return x.Role
	}
	return ""
}

func (x *GetUserAssetPermissionListReply_PermissionAssetList) GetHostname() string {
	if x != nil {
		return x.Hostname
	}
	return ""
}

func (x *GetUserAssetPermissionListReply_PermissionAssetList) GetExpireTime() string {
	if x != nil {
		return x.ExpireTime
	}
	return ""
}

func (x *GetUserAssetPermissionListReply_PermissionAssetList) GetStatus() int64 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *GetUserAssetPermissionListReply_PermissionAssetList) GetCreatedAt() string {
	if x != nil {
		return x.CreatedAt
	}
	return ""
}

type GetUserAssetPermissionListReply_User struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid                 int64                                                  `protobuf:"varint,1,opt,name=uid,proto3" json:"uid"`
	Email               string                                                 `protobuf:"bytes,2,opt,name=email,proto3" json:"email"`
	User                string                                                 `protobuf:"bytes,3,opt,name=user,proto3" json:"user"`
	OrganizationId      string                                                 `protobuf:"bytes,4,opt,name=organizationId,proto3" json:"organizationId"`
	CreatedAt           string                                                 `protobuf:"bytes,5,opt,name=createdAt,proto3" json:"createdAt"`
	PermissionAssetList []*GetUserAssetPermissionListReply_PermissionAssetList `protobuf:"bytes,6,rep,name=permissionAssetList,proto3" json:"permissionAssetList"`
}

func (x *GetUserAssetPermissionListReply_User) Reset() {
	*x = GetUserAssetPermissionListReply_User{}
	if protoimpl.UnsafeEnabled {
		mi := &file_perm_v1_perm_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserAssetPermissionListReply_User) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserAssetPermissionListReply_User) ProtoMessage() {}

func (x *GetUserAssetPermissionListReply_User) ProtoReflect() protoreflect.Message {
	mi := &file_perm_v1_perm_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserAssetPermissionListReply_User.ProtoReflect.Descriptor instead.
func (*GetUserAssetPermissionListReply_User) Descriptor() ([]byte, []int) {
	return file_perm_v1_perm_proto_rawDescGZIP(), []int{3, 1}
}

func (x *GetUserAssetPermissionListReply_User) GetUid() int64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

func (x *GetUserAssetPermissionListReply_User) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *GetUserAssetPermissionListReply_User) GetUser() string {
	if x != nil {
		return x.User
	}
	return ""
}

func (x *GetUserAssetPermissionListReply_User) GetOrganizationId() string {
	if x != nil {
		return x.OrganizationId
	}
	return ""
}

func (x *GetUserAssetPermissionListReply_User) GetCreatedAt() string {
	if x != nil {
		return x.CreatedAt
	}
	return ""
}

func (x *GetUserAssetPermissionListReply_User) GetPermissionAssetList() []*GetUserAssetPermissionListReply_PermissionAssetList {
	if x != nil {
		return x.PermissionAssetList
	}
	return nil
}

var File_perm_v1_perm_proto protoreflect.FileDescriptor

var file_perm_v1_perm_proto_rawDesc = []byte{
	0x0a, 0x12, 0x70, 0x65, 0x72, 0x6d, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x65, 0x72, 0x6d, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x70, 0x65, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xf6, 0x01, 0x0a, 0x20,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x41, 0x73, 0x73, 0x65, 0x74, 0x50,
	0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x69, 0x70, 0x4c, 0x69, 0x73, 0x74,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x69, 0x70, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x12,
	0x0a, 0x04, 0x72, 0x6f, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x72, 0x6f,
	0x6c, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x69, 0x73, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x64,
	0x12, 0x1e, 0x0a, 0x0a, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x48, 0x6f, 0x75, 0x72, 0x73, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x48, 0x6f, 0x75, 0x72, 0x73,
	0x12, 0x26, 0x0a, 0x0e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x49, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x6f, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x6f, 0x72, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x03, 0x75, 0x69, 0x64, 0x22, 0x27, 0x0a, 0x0b, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x85, 0x01,
	0x0a, 0x21, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x41, 0x73, 0x73, 0x65, 0x74, 0x50, 0x65,
	0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x61, 0x67, 0x65, 0x4e, 0x6f, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x06, 0x70, 0x61, 0x67, 0x65, 0x4e, 0x6f, 0x12, 0x1a, 0x0a, 0x08, 0x70,
	0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x70,
	0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x4b, 0x65, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x4b, 0x65, 0x79, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x69, 0x70, 0x22, 0x8f, 0x05, 0x0a, 0x1f, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65,
	0x72, 0x41, 0x73, 0x73, 0x65, 0x74, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x41, 0x0a, 0x04, 0x64, 0x61, 0x74,
	0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x70, 0x65, 0x72, 0x6d, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x41, 0x73, 0x73, 0x65, 0x74, 0x50, 0x65,
	0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x16, 0x0a, 0x06,
	0x70, 0x61, 0x67, 0x65, 0x4e, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x70, 0x61,
	0x67, 0x65, 0x4e, 0x6f, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65,
	0x12, 0x1e, 0x0a, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x12, 0x1c, 0x0a, 0x09, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x50, 0x61, 0x67, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x09, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x50, 0x61, 0x67, 0x65, 0x1a, 0xbb,
	0x01, 0x0a, 0x13, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x41, 0x73, 0x73,
	0x65, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x6f, 0x6c, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x72, 0x6f, 0x6c, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x68, 0x6f,
	0x73, 0x74, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x68, 0x6f,
	0x73, 0x74, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65,
	0x54, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x65, 0x78, 0x70, 0x69,
	0x72, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1c,
	0x0a, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x1a, 0xf8, 0x01, 0x0a,
	0x04, 0x55, 0x73, 0x65, 0x72, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x12, 0x0a,
	0x04, 0x75, 0x73, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x73, 0x65,
	0x72, 0x12, 0x26, 0x0a, 0x0e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6f, 0x72, 0x67, 0x61, 0x6e,
	0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x6e, 0x0a, 0x13, 0x70, 0x65, 0x72, 0x6d, 0x69,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x06,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x70, 0x65, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x41, 0x73, 0x73, 0x65, 0x74, 0x50, 0x65, 0x72, 0x6d, 0x69,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x50,
	0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x13, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x41, 0x73,
	0x73, 0x65, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x32, 0x8c, 0x02, 0x0a, 0x04, 0x50, 0x65, 0x72, 0x6d,
	0x12, 0x78, 0x0a, 0x19, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x41, 0x73,
	0x73, 0x65, 0x74, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x29, 0x2e,
	0x70, 0x65, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x55, 0x73,
	0x65, 0x72, 0x41, 0x73, 0x73, 0x65, 0x74, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x14, 0x2e, 0x70, 0x65, 0x72, 0x6d, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x1a,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x14, 0x22, 0x0f, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x65, 0x72, 0x6d,
	0x2f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x3a, 0x01, 0x2a, 0x12, 0x89, 0x01, 0x0a, 0x1a, 0x47,
	0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x41, 0x73, 0x73, 0x65, 0x74, 0x50, 0x65, 0x72, 0x6d, 0x69,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x2a, 0x2e, 0x70, 0x65, 0x72, 0x6d,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x41, 0x73, 0x73, 0x65, 0x74,
	0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x70, 0x65, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x41, 0x73, 0x73, 0x65, 0x74, 0x50, 0x65, 0x72, 0x6d,
	0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22,
	0x15, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x0f, 0x12, 0x0d, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x65, 0x72,
	0x6d, 0x2f, 0x6c, 0x69, 0x73, 0x74, 0x42, 0x64, 0x0a, 0x16, 0x64, 0x65, 0x76, 0x2e, 0x6b, 0x72,
	0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x65, 0x72, 0x6d, 0x2e, 0x76, 0x31,
	0x42, 0x0b, 0x50, 0x65, 0x72, 0x6d, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x56, 0x31, 0x50, 0x01, 0x5a,
	0x3b, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x69, 0x6e, 0x74, 0x73, 0x69, 0x67, 0x2e, 0x6e,
	0x65, 0x74, 0x2f, 0x4e, 0x4f, 0x43, 0x2f, 0x63, 0x74, 0x6d, 0x2f, 0x70, 0x75, 0x62, 0x6c, 0x69,
	0x63, 0x2f, 0x63, 0x74, 0x6d, 0x5f, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x2f, 0x61, 0x70,
	0x69, 0x2f, 0x70, 0x65, 0x72, 0x6d, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_perm_v1_perm_proto_rawDescOnce sync.Once
	file_perm_v1_perm_proto_rawDescData = file_perm_v1_perm_proto_rawDesc
)

func file_perm_v1_perm_proto_rawDescGZIP() []byte {
	file_perm_v1_perm_proto_rawDescOnce.Do(func() {
		file_perm_v1_perm_proto_rawDescData = protoimpl.X.CompressGZIP(file_perm_v1_perm_proto_rawDescData)
	})
	return file_perm_v1_perm_proto_rawDescData
}

var file_perm_v1_perm_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_perm_v1_perm_proto_goTypes = []interface{}{
	(*CreateUserAssetPermissionRequest)(nil),                    // 0: perm.v1.CreateUserAssetPermissionRequest
	(*CommonReply)(nil),                                         // 1: perm.v1.CommonReply
	(*GetUserAssetPermissionListRequest)(nil),                   // 2: perm.v1.GetUserAssetPermissionListRequest
	(*GetUserAssetPermissionListReply)(nil),                     // 3: perm.v1.GetUserAssetPermissionListReply
	(*GetUserAssetPermissionListReply_PermissionAssetList)(nil), // 4: perm.v1.GetUserAssetPermissionListReply.PermissionAssetList
	(*GetUserAssetPermissionListReply_User)(nil),                // 5: perm.v1.GetUserAssetPermissionListReply.User
}
var file_perm_v1_perm_proto_depIdxs = []int32{
	5, // 0: perm.v1.GetUserAssetPermissionListReply.data:type_name -> perm.v1.GetUserAssetPermissionListReply.User
	4, // 1: perm.v1.GetUserAssetPermissionListReply.User.permissionAssetList:type_name -> perm.v1.GetUserAssetPermissionListReply.PermissionAssetList
	0, // 2: perm.v1.Perm.CreateUserAssetPermission:input_type -> perm.v1.CreateUserAssetPermissionRequest
	2, // 3: perm.v1.Perm.GetUserAssetPermissionList:input_type -> perm.v1.GetUserAssetPermissionListRequest
	1, // 4: perm.v1.Perm.CreateUserAssetPermission:output_type -> perm.v1.CommonReply
	3, // 5: perm.v1.Perm.GetUserAssetPermissionList:output_type -> perm.v1.GetUserAssetPermissionListReply
	4, // [4:6] is the sub-list for method output_type
	2, // [2:4] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_perm_v1_perm_proto_init() }
func file_perm_v1_perm_proto_init() {
	if File_perm_v1_perm_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_perm_v1_perm_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateUserAssetPermissionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_perm_v1_perm_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CommonReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_perm_v1_perm_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserAssetPermissionListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_perm_v1_perm_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserAssetPermissionListReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_perm_v1_perm_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserAssetPermissionListReply_PermissionAssetList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_perm_v1_perm_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserAssetPermissionListReply_User); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_perm_v1_perm_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_perm_v1_perm_proto_goTypes,
		DependencyIndexes: file_perm_v1_perm_proto_depIdxs,
		MessageInfos:      file_perm_v1_perm_proto_msgTypes,
	}.Build()
	File_perm_v1_perm_proto = out.File
	file_perm_v1_perm_proto_rawDesc = nil
	file_perm_v1_perm_proto_goTypes = nil
	file_perm_v1_perm_proto_depIdxs = nil
}
