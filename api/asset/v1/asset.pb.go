// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v4.23.4
// source: api/asset/v1/asset.proto

package v1

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetAssetProxyGatewaySelectReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data []*GetAssetProxyGatewaySelectReply_AssetProxyGateway `protobuf:"bytes,1,rep,name=data,proto3" json:"data"`
}

func (x *GetAssetProxyGatewaySelectReply) Reset() {
	*x = GetAssetProxyGatewaySelectReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_asset_v1_asset_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAssetProxyGatewaySelectReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAssetProxyGatewaySelectReply) ProtoMessage() {}

func (x *GetAssetProxyGatewaySelectReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_asset_v1_asset_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAssetProxyGatewaySelectReply.ProtoReflect.Descriptor instead.
func (*GetAssetProxyGatewaySelectReply) Descriptor() ([]byte, []int) {
	return file_api_asset_v1_asset_proto_rawDescGZIP(), []int{0}
}

func (x *GetAssetProxyGatewaySelectReply) GetData() []*GetAssetProxyGatewaySelectReply_AssetProxyGateway {
	if x != nil {
		return x.Data
	}
	return nil
}

type UserAssetAliasRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key   string `protobuf:"bytes,1,opt,name=key,proto3" json:"key"`
	Alias string `protobuf:"bytes,2,opt,name=alias,proto3" json:"alias"`
}

func (x *UserAssetAliasRequest) Reset() {
	*x = UserAssetAliasRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_asset_v1_asset_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserAssetAliasRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserAssetAliasRequest) ProtoMessage() {}

func (x *UserAssetAliasRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_asset_v1_asset_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserAssetAliasRequest.ProtoReflect.Descriptor instead.
func (*UserAssetAliasRequest) Descriptor() ([]byte, []int) {
	return file_api_asset_v1_asset_proto_rawDescGZIP(), []int{1}
}

func (x *UserAssetAliasRequest) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *UserAssetAliasRequest) GetAlias() string {
	if x != nil {
		return x.Alias
	}
	return ""
}

type ListUserWorkSpaceReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data []*ListUserWorkSpaceReply_WorkSpace `protobuf:"bytes,1,rep,name=data,proto3" json:"data"`
}

func (x *ListUserWorkSpaceReply) Reset() {
	*x = ListUserWorkSpaceReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_asset_v1_asset_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListUserWorkSpaceReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListUserWorkSpaceReply) ProtoMessage() {}

func (x *ListUserWorkSpaceReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_asset_v1_asset_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListUserWorkSpaceReply.ProtoReflect.Descriptor instead.
func (*ListUserWorkSpaceReply) Descriptor() ([]byte, []int) {
	return file_api_asset_v1_asset_proto_rawDescGZIP(), []int{2}
}

func (x *ListUserWorkSpaceReply) GetData() []*ListUserWorkSpaceReply_WorkSpace {
	if x != nil {
		return x.Data
	}
	return nil
}

type CreateAssetRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name           string `protobuf:"bytes,1,opt,name=name,proto3" json:"name"`
	Idc            string `protobuf:"bytes,2,opt,name=idc,proto3" json:"idc"`
	AssetIp        string `protobuf:"bytes,3,opt,name=assetIp,proto3" json:"assetIp"`
	Active         string `protobuf:"bytes,4,opt,name=active,proto3" json:"active"`
	Platform       string `protobuf:"bytes,5,opt,name=platform,proto3" json:"platform"`
	ProxyGatewayId int64  `protobuf:"varint,6,opt,name=proxyGatewayId,proto3" json:"proxyGatewayId"`
	Protocol       string `protobuf:"bytes,7,opt,name=protocol,proto3" json:"protocol"`
	AssetSource    string `protobuf:"bytes,8,opt,name=assetSource,proto3" json:"assetSource"`
	Os             string `protobuf:"bytes,9,opt,name=os,proto3" json:"os"`
	OrganizationId string `protobuf:"bytes,11,opt,name=organizationId,proto3" json:"organizationId"`
	Comment        string `protobuf:"bytes,12,opt,name=comment,proto3" json:"comment"`
}

func (x *CreateAssetRequest) Reset() {
	*x = CreateAssetRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_asset_v1_asset_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateAssetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAssetRequest) ProtoMessage() {}

func (x *CreateAssetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_asset_v1_asset_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAssetRequest.ProtoReflect.Descriptor instead.
func (*CreateAssetRequest) Descriptor() ([]byte, []int) {
	return file_api_asset_v1_asset_proto_rawDescGZIP(), []int{3}
}

func (x *CreateAssetRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateAssetRequest) GetIdc() string {
	if x != nil {
		return x.Idc
	}
	return ""
}

func (x *CreateAssetRequest) GetAssetIp() string {
	if x != nil {
		return x.AssetIp
	}
	return ""
}

func (x *CreateAssetRequest) GetActive() string {
	if x != nil {
		return x.Active
	}
	return ""
}

func (x *CreateAssetRequest) GetPlatform() string {
	if x != nil {
		return x.Platform
	}
	return ""
}

func (x *CreateAssetRequest) GetProxyGatewayId() int64 {
	if x != nil {
		return x.ProxyGatewayId
	}
	return 0
}

func (x *CreateAssetRequest) GetProtocol() string {
	if x != nil {
		return x.Protocol
	}
	return ""
}

func (x *CreateAssetRequest) GetAssetSource() string {
	if x != nil {
		return x.AssetSource
	}
	return ""
}

func (x *CreateAssetRequest) GetOs() string {
	if x != nil {
		return x.Os
	}
	return ""
}

func (x *CreateAssetRequest) GetOrganizationId() string {
	if x != nil {
		return x.OrganizationId
	}
	return ""
}

func (x *CreateAssetRequest) GetComment() string {
	if x != nil {
		return x.Comment
	}
	return ""
}

type UpdateAseetRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uuid           string `protobuf:"bytes,1,opt,name=uuid,proto3" json:"uuid"`
	Name           string `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	AssetIp        string `protobuf:"bytes,3,opt,name=assetIp,proto3" json:"assetIp"`
	Active         string `protobuf:"bytes,4,opt,name=active,proto3" json:"active"`
	Platform       string `protobuf:"bytes,5,opt,name=platform,proto3" json:"platform"`
	ProxyGatewayId int64  `protobuf:"varint,6,opt,name=proxyGatewayId,proto3" json:"proxyGatewayId"`
	Protocol       string `protobuf:"bytes,7,opt,name=protocol,proto3" json:"protocol"`
	Os             string `protobuf:"bytes,8,opt,name=os,proto3" json:"os"`
	OrganizationId string `protobuf:"bytes,10,opt,name=organizationId,proto3" json:"organizationId"`
	Comment        string `protobuf:"bytes,11,opt,name=comment,proto3" json:"comment"`
	Idc            string `protobuf:"bytes,12,opt,name=idc,proto3" json:"idc"`
}

func (x *UpdateAseetRequest) Reset() {
	*x = UpdateAseetRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_asset_v1_asset_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateAseetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAseetRequest) ProtoMessage() {}

func (x *UpdateAseetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_asset_v1_asset_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAseetRequest.ProtoReflect.Descriptor instead.
func (*UpdateAseetRequest) Descriptor() ([]byte, []int) {
	return file_api_asset_v1_asset_proto_rawDescGZIP(), []int{4}
}

func (x *UpdateAseetRequest) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

func (x *UpdateAseetRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdateAseetRequest) GetAssetIp() string {
	if x != nil {
		return x.AssetIp
	}
	return ""
}

func (x *UpdateAseetRequest) GetActive() string {
	if x != nil {
		return x.Active
	}
	return ""
}

func (x *UpdateAseetRequest) GetPlatform() string {
	if x != nil {
		return x.Platform
	}
	return ""
}

func (x *UpdateAseetRequest) GetProxyGatewayId() int64 {
	if x != nil {
		return x.ProxyGatewayId
	}
	return 0
}

func (x *UpdateAseetRequest) GetProtocol() string {
	if x != nil {
		return x.Protocol
	}
	return ""
}

func (x *UpdateAseetRequest) GetOs() string {
	if x != nil {
		return x.Os
	}
	return ""
}

func (x *UpdateAseetRequest) GetOrganizationId() string {
	if x != nil {
		return x.OrganizationId
	}
	return ""
}

func (x *UpdateAseetRequest) GetComment() string {
	if x != nil {
		return x.Comment
	}
	return ""
}

func (x *UpdateAseetRequest) GetIdc() string {
	if x != nil {
		return x.Idc
	}
	return ""
}

type DeleteAssetRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uuid string `protobuf:"bytes,1,opt,name=uuid,proto3" json:"uuid"`
}

func (x *DeleteAssetRequest) Reset() {
	*x = DeleteAssetRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_asset_v1_asset_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteAssetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteAssetRequest) ProtoMessage() {}

func (x *DeleteAssetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_asset_v1_asset_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteAssetRequest.ProtoReflect.Descriptor instead.
func (*DeleteAssetRequest) Descriptor() ([]byte, []int) {
	return file_api_asset_v1_asset_proto_rawDescGZIP(), []int{5}
}

func (x *DeleteAssetRequest) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

type ListAssetRouteRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OrganizationId string `protobuf:"bytes,1,opt,name=organizationId,proto3" json:"organizationId"`
}

func (x *ListAssetRouteRequest) Reset() {
	*x = ListAssetRouteRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_asset_v1_asset_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAssetRouteRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAssetRouteRequest) ProtoMessage() {}

func (x *ListAssetRouteRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_asset_v1_asset_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAssetRouteRequest.ProtoReflect.Descriptor instead.
func (*ListAssetRouteRequest) Descriptor() ([]byte, []int) {
	return file_api_asset_v1_asset_proto_rawDescGZIP(), []int{6}
}

func (x *ListAssetRouteRequest) GetOrganizationId() string {
	if x != nil {
		return x.OrganizationId
	}
	return ""
}

type ListAssetRouteReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Routers []*ListAssetRouteReply_Route `protobuf:"bytes,1,rep,name=routers,proto3" json:"routers"`
}

func (x *ListAssetRouteReply) Reset() {
	*x = ListAssetRouteReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_asset_v1_asset_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAssetRouteReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAssetRouteReply) ProtoMessage() {}

func (x *ListAssetRouteReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_asset_v1_asset_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAssetRouteReply.ProtoReflect.Descriptor instead.
func (*ListAssetRouteReply) Descriptor() ([]byte, []int) {
	return file_api_asset_v1_asset_proto_rawDescGZIP(), []int{7}
}

func (x *ListAssetRouteReply) GetRouters() []*ListAssetRouteReply_Route {
	if x != nil {
		return x.Routers
	}
	return nil
}

type Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Message string `protobuf:"bytes,1,opt,name=message,proto3" json:"message"`
}

func (x *Reply) Reset() {
	*x = Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_asset_v1_asset_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Reply) ProtoMessage() {}

func (x *Reply) ProtoReflect() protoreflect.Message {
	mi := &file_api_asset_v1_asset_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Reply.ProtoReflect.Descriptor instead.
func (*Reply) Descriptor() ([]byte, []int) {
	return file_api_asset_v1_asset_proto_rawDescGZIP(), []int{8}
}

func (x *Reply) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type UserAssetFavoriteRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AssetIp string `protobuf:"bytes,1,opt,name=assetIp,proto3" json:"assetIp"`
	Action  string `protobuf:"bytes,2,opt,name=action,proto3" json:"action"`
}

func (x *UserAssetFavoriteRequest) Reset() {
	*x = UserAssetFavoriteRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_asset_v1_asset_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserAssetFavoriteRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserAssetFavoriteRequest) ProtoMessage() {}

func (x *UserAssetFavoriteRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_asset_v1_asset_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserAssetFavoriteRequest.ProtoReflect.Descriptor instead.
func (*UserAssetFavoriteRequest) Descriptor() ([]byte, []int) {
	return file_api_asset_v1_asset_proto_rawDescGZIP(), []int{9}
}

func (x *UserAssetFavoriteRequest) GetAssetIp() string {
	if x != nil {
		return x.AssetIp
	}
	return ""
}

func (x *UserAssetFavoriteRequest) GetAction() string {
	if x != nil {
		return x.Action
	}
	return ""
}

type GetAssetListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PageSize       int64  `protobuf:"varint,1,opt,name=pageSize,proto3" json:"pageSize"`
	PageNo         int64  `protobuf:"varint,2,opt,name=pageNo,proto3" json:"pageNo"`
	SearchText     string `protobuf:"bytes,3,opt,name=searchText,proto3" json:"searchText"`
	Idc            string `protobuf:"bytes,4,opt,name=idc,proto3" json:"idc"`
	OrganizationId string `protobuf:"bytes,5,opt,name=organizationId,proto3" json:"organizationId"`
	ProxyGatewayId int64  `protobuf:"varint,6,opt,name=proxyGatewayId,proto3" json:"proxyGatewayId"`
}

func (x *GetAssetListRequest) Reset() {
	*x = GetAssetListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_asset_v1_asset_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAssetListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAssetListRequest) ProtoMessage() {}

func (x *GetAssetListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_asset_v1_asset_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAssetListRequest.ProtoReflect.Descriptor instead.
func (*GetAssetListRequest) Descriptor() ([]byte, []int) {
	return file_api_asset_v1_asset_proto_rawDescGZIP(), []int{10}
}

func (x *GetAssetListRequest) GetPageSize() int64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *GetAssetListRequest) GetPageNo() int64 {
	if x != nil {
		return x.PageNo
	}
	return 0
}

func (x *GetAssetListRequest) GetSearchText() string {
	if x != nil {
		return x.SearchText
	}
	return ""
}

func (x *GetAssetListRequest) GetIdc() string {
	if x != nil {
		return x.Idc
	}
	return ""
}

func (x *GetAssetListRequest) GetOrganizationId() string {
	if x != nil {
		return x.OrganizationId
	}
	return ""
}

func (x *GetAssetListRequest) GetProxyGatewayId() int64 {
	if x != nil {
		return x.ProxyGatewayId
	}
	return 0
}

type GetAssetListReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data       []*GetAssetListReply_Asset `protobuf:"bytes,1,rep,name=data,proto3" json:"data"`
	PageNo     int64                      `protobuf:"varint,2,opt,name=pageNo,proto3" json:"pageNo"`
	PageSize   int64                      `protobuf:"varint,3,opt,name=pageSize,proto3" json:"pageSize"`
	TotalCount int64                      `protobuf:"varint,4,opt,name=totalCount,proto3" json:"totalCount"`
	TotalPage  int64                      `protobuf:"varint,5,opt,name=totalPage,proto3" json:"totalPage"`
}

func (x *GetAssetListReply) Reset() {
	*x = GetAssetListReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_asset_v1_asset_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAssetListReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAssetListReply) ProtoMessage() {}

func (x *GetAssetListReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_asset_v1_asset_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAssetListReply.ProtoReflect.Descriptor instead.
func (*GetAssetListReply) Descriptor() ([]byte, []int) {
	return file_api_asset_v1_asset_proto_rawDescGZIP(), []int{11}
}

func (x *GetAssetListReply) GetData() []*GetAssetListReply_Asset {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *GetAssetListReply) GetPageNo() int64 {
	if x != nil {
		return x.PageNo
	}
	return 0
}

func (x *GetAssetListReply) GetPageSize() int64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *GetAssetListReply) GetTotalCount() int64 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

func (x *GetAssetListReply) GetTotalPage() int64 {
	if x != nil {
		return x.TotalPage
	}
	return 0
}

type GetAssetListByIpRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PageSize       int64  `protobuf:"varint,1,opt,name=pageSize,proto3" json:"pageSize"`
	PageNo         int64  `protobuf:"varint,2,opt,name=pageNo,proto3" json:"pageNo"`
	Ip             string `protobuf:"bytes,3,opt,name=ip,proto3" json:"ip"`
	OrganizationId string `protobuf:"bytes,4,opt,name=organizationId,proto3" json:"organizationId"`
}

func (x *GetAssetListByIpRequest) Reset() {
	*x = GetAssetListByIpRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_asset_v1_asset_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAssetListByIpRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAssetListByIpRequest) ProtoMessage() {}

func (x *GetAssetListByIpRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_asset_v1_asset_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAssetListByIpRequest.ProtoReflect.Descriptor instead.
func (*GetAssetListByIpRequest) Descriptor() ([]byte, []int) {
	return file_api_asset_v1_asset_proto_rawDescGZIP(), []int{12}
}

func (x *GetAssetListByIpRequest) GetPageSize() int64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *GetAssetListByIpRequest) GetPageNo() int64 {
	if x != nil {
		return x.PageNo
	}
	return 0
}

func (x *GetAssetListByIpRequest) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *GetAssetListByIpRequest) GetOrganizationId() string {
	if x != nil {
		return x.OrganizationId
	}
	return ""
}

type GetAssetListByIpReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data       []*GetAssetListByIpReply_Asset `protobuf:"bytes,1,rep,name=data,proto3" json:"data"`
	PageNo     int64                          `protobuf:"varint,2,opt,name=pageNo,proto3" json:"pageNo"`
	PageSize   int64                          `protobuf:"varint,3,opt,name=pageSize,proto3" json:"pageSize"`
	TotalCount int64                          `protobuf:"varint,4,opt,name=totalCount,proto3" json:"totalCount"`
	TotalPage  int64                          `protobuf:"varint,5,opt,name=totalPage,proto3" json:"totalPage"`
}

func (x *GetAssetListByIpReply) Reset() {
	*x = GetAssetListByIpReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_asset_v1_asset_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAssetListByIpReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAssetListByIpReply) ProtoMessage() {}

func (x *GetAssetListByIpReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_asset_v1_asset_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAssetListByIpReply.ProtoReflect.Descriptor instead.
func (*GetAssetListByIpReply) Descriptor() ([]byte, []int) {
	return file_api_asset_v1_asset_proto_rawDescGZIP(), []int{13}
}

func (x *GetAssetListByIpReply) GetData() []*GetAssetListByIpReply_Asset {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *GetAssetListByIpReply) GetPageNo() int64 {
	if x != nil {
		return x.PageNo
	}
	return 0
}

func (x *GetAssetListByIpReply) GetPageSize() int64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *GetAssetListByIpReply) GetTotalCount() int64 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

func (x *GetAssetListByIpReply) GetTotalPage() int64 {
	if x != nil {
		return x.TotalPage
	}
	return 0
}

type GetAssetProxyGatewayListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PageSize   int64  `protobuf:"varint,1,opt,name=pageSize,proto3" json:"pageSize"`
	PageNo     int64  `protobuf:"varint,2,opt,name=pageNo,proto3" json:"pageNo"`
	SearchText string `protobuf:"bytes,3,opt,name=searchText,proto3" json:"searchText"`
}

func (x *GetAssetProxyGatewayListRequest) Reset() {
	*x = GetAssetProxyGatewayListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_asset_v1_asset_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAssetProxyGatewayListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAssetProxyGatewayListRequest) ProtoMessage() {}

func (x *GetAssetProxyGatewayListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_asset_v1_asset_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAssetProxyGatewayListRequest.ProtoReflect.Descriptor instead.
func (*GetAssetProxyGatewayListRequest) Descriptor() ([]byte, []int) {
	return file_api_asset_v1_asset_proto_rawDescGZIP(), []int{14}
}

func (x *GetAssetProxyGatewayListRequest) GetPageSize() int64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *GetAssetProxyGatewayListRequest) GetPageNo() int64 {
	if x != nil {
		return x.PageNo
	}
	return 0
}

func (x *GetAssetProxyGatewayListRequest) GetSearchText() string {
	if x != nil {
		return x.SearchText
	}
	return ""
}

type CreateAssetProxyGatewayRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name           string `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	OrganizationId string `protobuf:"bytes,3,opt,name=organizationId,proto3" json:"organizationId"`
	Ip             string `protobuf:"bytes,4,opt,name=ip,proto3" json:"ip"`
	Port           int64  `protobuf:"varint,1,opt,name=port,proto3" json:"port"`
	Username       string `protobuf:"bytes,6,opt,name=username,proto3" json:"username"`
	Password       string `protobuf:"bytes,7,opt,name=password,proto3" json:"password"`
	PrivateKey     string `protobuf:"bytes,8,opt,name=privateKey,proto3" json:"privateKey"`
	Comment        string `protobuf:"bytes,9,opt,name=comment,proto3" json:"comment"`
	Idc            string `protobuf:"bytes,10,opt,name=idc,proto3" json:"idc"`
}

func (x *CreateAssetProxyGatewayRequest) Reset() {
	*x = CreateAssetProxyGatewayRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_asset_v1_asset_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateAssetProxyGatewayRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAssetProxyGatewayRequest) ProtoMessage() {}

func (x *CreateAssetProxyGatewayRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_asset_v1_asset_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAssetProxyGatewayRequest.ProtoReflect.Descriptor instead.
func (*CreateAssetProxyGatewayRequest) Descriptor() ([]byte, []int) {
	return file_api_asset_v1_asset_proto_rawDescGZIP(), []int{15}
}

func (x *CreateAssetProxyGatewayRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateAssetProxyGatewayRequest) GetOrganizationId() string {
	if x != nil {
		return x.OrganizationId
	}
	return ""
}

func (x *CreateAssetProxyGatewayRequest) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *CreateAssetProxyGatewayRequest) GetPort() int64 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *CreateAssetProxyGatewayRequest) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *CreateAssetProxyGatewayRequest) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *CreateAssetProxyGatewayRequest) GetPrivateKey() string {
	if x != nil {
		return x.PrivateKey
	}
	return ""
}

func (x *CreateAssetProxyGatewayRequest) GetComment() string {
	if x != nil {
		return x.Comment
	}
	return ""
}

func (x *CreateAssetProxyGatewayRequest) GetIdc() string {
	if x != nil {
		return x.Idc
	}
	return ""
}

type UpdateAssetProxyGatewayRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id             int32  `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	Name           string `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	OrganizationId string `protobuf:"bytes,3,opt,name=organizationId,proto3" json:"organizationId"`
	Ip             string `protobuf:"bytes,4,opt,name=ip,proto3" json:"ip"`
	Port           int64  `protobuf:"varint,5,opt,name=port,proto3" json:"port"`
	Username       string `protobuf:"bytes,6,opt,name=username,proto3" json:"username"`
	Password       string `protobuf:"bytes,7,opt,name=password,proto3" json:"password"`
	PrivateKey     string `protobuf:"bytes,8,opt,name=privateKey,proto3" json:"privateKey"`
	Comment        string `protobuf:"bytes,9,opt,name=comment,proto3" json:"comment"`
	Idc            string `protobuf:"bytes,10,opt,name=idc,proto3" json:"idc"`
}

func (x *UpdateAssetProxyGatewayRequest) Reset() {
	*x = UpdateAssetProxyGatewayRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_asset_v1_asset_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateAssetProxyGatewayRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAssetProxyGatewayRequest) ProtoMessage() {}

func (x *UpdateAssetProxyGatewayRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_asset_v1_asset_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAssetProxyGatewayRequest.ProtoReflect.Descriptor instead.
func (*UpdateAssetProxyGatewayRequest) Descriptor() ([]byte, []int) {
	return file_api_asset_v1_asset_proto_rawDescGZIP(), []int{16}
}

func (x *UpdateAssetProxyGatewayRequest) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateAssetProxyGatewayRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdateAssetProxyGatewayRequest) GetOrganizationId() string {
	if x != nil {
		return x.OrganizationId
	}
	return ""
}

func (x *UpdateAssetProxyGatewayRequest) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *UpdateAssetProxyGatewayRequest) GetPort() int64 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *UpdateAssetProxyGatewayRequest) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *UpdateAssetProxyGatewayRequest) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *UpdateAssetProxyGatewayRequest) GetPrivateKey() string {
	if x != nil {
		return x.PrivateKey
	}
	return ""
}

func (x *UpdateAssetProxyGatewayRequest) GetComment() string {
	if x != nil {
		return x.Comment
	}
	return ""
}

func (x *UpdateAssetProxyGatewayRequest) GetIdc() string {
	if x != nil {
		return x.Idc
	}
	return ""
}

type DeleteAssetProxyGatewayRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
}

func (x *DeleteAssetProxyGatewayRequest) Reset() {
	*x = DeleteAssetProxyGatewayRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_asset_v1_asset_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteAssetProxyGatewayRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteAssetProxyGatewayRequest) ProtoMessage() {}

func (x *DeleteAssetProxyGatewayRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_asset_v1_asset_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteAssetProxyGatewayRequest.ProtoReflect.Descriptor instead.
func (*DeleteAssetProxyGatewayRequest) Descriptor() ([]byte, []int) {
	return file_api_asset_v1_asset_proto_rawDescGZIP(), []int{17}
}

func (x *DeleteAssetProxyGatewayRequest) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

type GetAssetProxyGatewayListReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data       []*GetAssetProxyGatewayListReply_AssetProxyGateway `protobuf:"bytes,1,rep,name=data,proto3" json:"data"`
	PageNo     int64                                              `protobuf:"varint,2,opt,name=pageNo,proto3" json:"pageNo"`
	PageSize   int64                                              `protobuf:"varint,3,opt,name=pageSize,proto3" json:"pageSize"`
	TotalCount int64                                              `protobuf:"varint,4,opt,name=totalCount,proto3" json:"totalCount"`
	TotalPage  int64                                              `protobuf:"varint,5,opt,name=totalPage,proto3" json:"totalPage"`
}

func (x *GetAssetProxyGatewayListReply) Reset() {
	*x = GetAssetProxyGatewayListReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_asset_v1_asset_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAssetProxyGatewayListReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAssetProxyGatewayListReply) ProtoMessage() {}

func (x *GetAssetProxyGatewayListReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_asset_v1_asset_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAssetProxyGatewayListReply.ProtoReflect.Descriptor instead.
func (*GetAssetProxyGatewayListReply) Descriptor() ([]byte, []int) {
	return file_api_asset_v1_asset_proto_rawDescGZIP(), []int{18}
}

func (x *GetAssetProxyGatewayListReply) GetData() []*GetAssetProxyGatewayListReply_AssetProxyGateway {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *GetAssetProxyGatewayListReply) GetPageNo() int64 {
	if x != nil {
		return x.PageNo
	}
	return 0
}

func (x *GetAssetProxyGatewayListReply) GetPageSize() int64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *GetAssetProxyGatewayListReply) GetTotalCount() int64 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

func (x *GetAssetProxyGatewayListReply) GetTotalPage() int64 {
	if x != nil {
		return x.TotalPage
	}
	return 0
}

type GetAssetAdminUserListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PageSize   int64  `protobuf:"varint,1,opt,name=pageSize,proto3" json:"pageSize"`
	PageNo     int64  `protobuf:"varint,2,opt,name=pageNo,proto3" json:"pageNo"`
	SearchText string `protobuf:"bytes,3,opt,name=searchText,proto3" json:"searchText"`
}

func (x *GetAssetAdminUserListRequest) Reset() {
	*x = GetAssetAdminUserListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_asset_v1_asset_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAssetAdminUserListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAssetAdminUserListRequest) ProtoMessage() {}

func (x *GetAssetAdminUserListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_asset_v1_asset_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAssetAdminUserListRequest.ProtoReflect.Descriptor instead.
func (*GetAssetAdminUserListRequest) Descriptor() ([]byte, []int) {
	return file_api_asset_v1_asset_proto_rawDescGZIP(), []int{19}
}

func (x *GetAssetAdminUserListRequest) GetPageSize() int64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *GetAssetAdminUserListRequest) GetPageNo() int64 {
	if x != nil {
		return x.PageNo
	}
	return 0
}

func (x *GetAssetAdminUserListRequest) GetSearchText() string {
	if x != nil {
		return x.SearchText
	}
	return ""
}

type CreateAssetAdminUserRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OrganizationId string `protobuf:"bytes,1,opt,name=organizationId,proto3" json:"organizationId"`
	Username       string `protobuf:"bytes,2,opt,name=username,proto3" json:"username"`
	Password       string `protobuf:"bytes,3,opt,name=password,proto3" json:"password"`
	PrivateKey     string `protobuf:"bytes,4,opt,name=privateKey,proto3" json:"privateKey"`
	Comment        string `protobuf:"bytes,5,opt,name=comment,proto3" json:"comment"`
}

func (x *CreateAssetAdminUserRequest) Reset() {
	*x = CreateAssetAdminUserRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_asset_v1_asset_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateAssetAdminUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAssetAdminUserRequest) ProtoMessage() {}

func (x *CreateAssetAdminUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_asset_v1_asset_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAssetAdminUserRequest.ProtoReflect.Descriptor instead.
func (*CreateAssetAdminUserRequest) Descriptor() ([]byte, []int) {
	return file_api_asset_v1_asset_proto_rawDescGZIP(), []int{20}
}

func (x *CreateAssetAdminUserRequest) GetOrganizationId() string {
	if x != nil {
		return x.OrganizationId
	}
	return ""
}

func (x *CreateAssetAdminUserRequest) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *CreateAssetAdminUserRequest) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *CreateAssetAdminUserRequest) GetPrivateKey() string {
	if x != nil {
		return x.PrivateKey
	}
	return ""
}

func (x *CreateAssetAdminUserRequest) GetComment() string {
	if x != nil {
		return x.Comment
	}
	return ""
}

type UpdateAssetAdminUserRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id             int32  `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	OrganizationId string `protobuf:"bytes,2,opt,name=organizationId,proto3" json:"organizationId"`
	Username       string `protobuf:"bytes,3,opt,name=username,proto3" json:"username"`
	Password       string `protobuf:"bytes,4,opt,name=password,proto3" json:"password"`
	PrivateKey     string `protobuf:"bytes,5,opt,name=privateKey,proto3" json:"privateKey"`
	Comment        string `protobuf:"bytes,6,opt,name=comment,proto3" json:"comment"`
}

func (x *UpdateAssetAdminUserRequest) Reset() {
	*x = UpdateAssetAdminUserRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_asset_v1_asset_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateAssetAdminUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAssetAdminUserRequest) ProtoMessage() {}

func (x *UpdateAssetAdminUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_asset_v1_asset_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAssetAdminUserRequest.ProtoReflect.Descriptor instead.
func (*UpdateAssetAdminUserRequest) Descriptor() ([]byte, []int) {
	return file_api_asset_v1_asset_proto_rawDescGZIP(), []int{21}
}

func (x *UpdateAssetAdminUserRequest) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateAssetAdminUserRequest) GetOrganizationId() string {
	if x != nil {
		return x.OrganizationId
	}
	return ""
}

func (x *UpdateAssetAdminUserRequest) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *UpdateAssetAdminUserRequest) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *UpdateAssetAdminUserRequest) GetPrivateKey() string {
	if x != nil {
		return x.PrivateKey
	}
	return ""
}

func (x *UpdateAssetAdminUserRequest) GetComment() string {
	if x != nil {
		return x.Comment
	}
	return ""
}

type DeleteAssetAdminUserRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
}

func (x *DeleteAssetAdminUserRequest) Reset() {
	*x = DeleteAssetAdminUserRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_asset_v1_asset_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteAssetAdminUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteAssetAdminUserRequest) ProtoMessage() {}

func (x *DeleteAssetAdminUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_asset_v1_asset_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteAssetAdminUserRequest.ProtoReflect.Descriptor instead.
func (*DeleteAssetAdminUserRequest) Descriptor() ([]byte, []int) {
	return file_api_asset_v1_asset_proto_rawDescGZIP(), []int{22}
}

func (x *DeleteAssetAdminUserRequest) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

type GetAssetAdminUserListReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data       []*GetAssetAdminUserListReply_AssetAdminUser `protobuf:"bytes,1,rep,name=data,proto3" json:"data"`
	PageNo     int64                                        `protobuf:"varint,2,opt,name=pageNo,proto3" json:"pageNo"`
	PageSize   int64                                        `protobuf:"varint,3,opt,name=pageSize,proto3" json:"pageSize"`
	TotalCount int64                                        `protobuf:"varint,4,opt,name=totalCount,proto3" json:"totalCount"`
	TotalPage  int64                                        `protobuf:"varint,5,opt,name=totalPage,proto3" json:"totalPage"`
}

func (x *GetAssetAdminUserListReply) Reset() {
	*x = GetAssetAdminUserListReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_asset_v1_asset_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAssetAdminUserListReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAssetAdminUserListReply) ProtoMessage() {}

func (x *GetAssetAdminUserListReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_asset_v1_asset_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAssetAdminUserListReply.ProtoReflect.Descriptor instead.
func (*GetAssetAdminUserListReply) Descriptor() ([]byte, []int) {
	return file_api_asset_v1_asset_proto_rawDescGZIP(), []int{23}
}

func (x *GetAssetAdminUserListReply) GetData() []*GetAssetAdminUserListReply_AssetAdminUser {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *GetAssetAdminUserListReply) GetPageNo() int64 {
	if x != nil {
		return x.PageNo
	}
	return 0
}

func (x *GetAssetAdminUserListReply) GetPageSize() int64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *GetAssetAdminUserListReply) GetTotalCount() int64 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

func (x *GetAssetAdminUserListReply) GetTotalPage() int64 {
	if x != nil {
		return x.TotalPage
	}
	return 0
}

type GetAssetProxyGatewaySelectReply_AssetProxyGateway struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id             int32  `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	OrganizationId string `protobuf:"bytes,2,opt,name=organizationId,proto3" json:"organizationId"`
	Name           string `protobuf:"bytes,3,opt,name=name,proto3" json:"name"`
	Ip             string `protobuf:"bytes,4,opt,name=ip,proto3" json:"ip"`
	Port           int64  `protobuf:"varint,5,opt,name=port,proto3" json:"port"`
	Username       string `protobuf:"bytes,6,opt,name=username,proto3" json:"username"`
	Comment        string `protobuf:"bytes,7,opt,name=comment,proto3" json:"comment"`
	Idc            string `protobuf:"bytes,8,opt,name=idc,proto3" json:"idc"`
}

func (x *GetAssetProxyGatewaySelectReply_AssetProxyGateway) Reset() {
	*x = GetAssetProxyGatewaySelectReply_AssetProxyGateway{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_asset_v1_asset_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAssetProxyGatewaySelectReply_AssetProxyGateway) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAssetProxyGatewaySelectReply_AssetProxyGateway) ProtoMessage() {}

func (x *GetAssetProxyGatewaySelectReply_AssetProxyGateway) ProtoReflect() protoreflect.Message {
	mi := &file_api_asset_v1_asset_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAssetProxyGatewaySelectReply_AssetProxyGateway.ProtoReflect.Descriptor instead.
func (*GetAssetProxyGatewaySelectReply_AssetProxyGateway) Descriptor() ([]byte, []int) {
	return file_api_asset_v1_asset_proto_rawDescGZIP(), []int{0, 0}
}

func (x *GetAssetProxyGatewaySelectReply_AssetProxyGateway) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetAssetProxyGatewaySelectReply_AssetProxyGateway) GetOrganizationId() string {
	if x != nil {
		return x.OrganizationId
	}
	return ""
}

func (x *GetAssetProxyGatewaySelectReply_AssetProxyGateway) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GetAssetProxyGatewaySelectReply_AssetProxyGateway) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *GetAssetProxyGatewaySelectReply_AssetProxyGateway) GetPort() int64 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *GetAssetProxyGatewaySelectReply_AssetProxyGateway) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *GetAssetProxyGatewaySelectReply_AssetProxyGateway) GetComment() string {
	if x != nil {
		return x.Comment
	}
	return ""
}

func (x *GetAssetProxyGatewaySelectReply_AssetProxyGateway) GetIdc() string {
	if x != nil {
		return x.Idc
	}
	return ""
}

type ListUserWorkSpaceReply_WorkSpace struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key   string `protobuf:"bytes,1,opt,name=key,proto3" json:"key"`
	Label string `protobuf:"bytes,2,opt,name=label,proto3" json:"label"`
}

func (x *ListUserWorkSpaceReply_WorkSpace) Reset() {
	*x = ListUserWorkSpaceReply_WorkSpace{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_asset_v1_asset_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListUserWorkSpaceReply_WorkSpace) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListUserWorkSpaceReply_WorkSpace) ProtoMessage() {}

func (x *ListUserWorkSpaceReply_WorkSpace) ProtoReflect() protoreflect.Message {
	mi := &file_api_asset_v1_asset_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListUserWorkSpaceReply_WorkSpace.ProtoReflect.Descriptor instead.
func (*ListUserWorkSpaceReply_WorkSpace) Descriptor() ([]byte, []int) {
	return file_api_asset_v1_asset_proto_rawDescGZIP(), []int{2, 0}
}

func (x *ListUserWorkSpaceReply_WorkSpace) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *ListUserWorkSpaceReply_WorkSpace) GetLabel() string {
	if x != nil {
		return x.Label
	}
	return ""
}

type ListAssetRouteReply_Children struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key            string `protobuf:"bytes,1,opt,name=key,proto3" json:"key"`
	Title          string `protobuf:"bytes,2,opt,name=title,proto3" json:"title"`
	Favorite       bool   `protobuf:"varint,3,opt,name=favorite,proto3" json:"favorite"`
	Ip             string `protobuf:"bytes,4,opt,name=ip,proto3" json:"ip"`
	OrganizationId string `protobuf:"bytes,5,opt,name=organizationId,proto3" json:"organizationId"`
}

func (x *ListAssetRouteReply_Children) Reset() {
	*x = ListAssetRouteReply_Children{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_asset_v1_asset_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAssetRouteReply_Children) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAssetRouteReply_Children) ProtoMessage() {}

func (x *ListAssetRouteReply_Children) ProtoReflect() protoreflect.Message {
	mi := &file_api_asset_v1_asset_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAssetRouteReply_Children.ProtoReflect.Descriptor instead.
func (*ListAssetRouteReply_Children) Descriptor() ([]byte, []int) {
	return file_api_asset_v1_asset_proto_rawDescGZIP(), []int{7, 0}
}

func (x *ListAssetRouteReply_Children) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *ListAssetRouteReply_Children) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *ListAssetRouteReply_Children) GetFavorite() bool {
	if x != nil {
		return x.Favorite
	}
	return false
}

func (x *ListAssetRouteReply_Children) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *ListAssetRouteReply_Children) GetOrganizationId() string {
	if x != nil {
		return x.OrganizationId
	}
	return ""
}

type ListAssetRouteReply_Route struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key      string                          `protobuf:"bytes,1,opt,name=key,proto3" json:"key"`
	Title    string                          `protobuf:"bytes,2,opt,name=title,proto3" json:"title"`
	Children []*ListAssetRouteReply_Children `protobuf:"bytes,3,rep,name=children,proto3" json:"children"`
}

func (x *ListAssetRouteReply_Route) Reset() {
	*x = ListAssetRouteReply_Route{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_asset_v1_asset_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAssetRouteReply_Route) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAssetRouteReply_Route) ProtoMessage() {}

func (x *ListAssetRouteReply_Route) ProtoReflect() protoreflect.Message {
	mi := &file_api_asset_v1_asset_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAssetRouteReply_Route.ProtoReflect.Descriptor instead.
func (*ListAssetRouteReply_Route) Descriptor() ([]byte, []int) {
	return file_api_asset_v1_asset_proto_rawDescGZIP(), []int{7, 1}
}

func (x *ListAssetRouteReply_Route) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *ListAssetRouteReply_Route) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *ListAssetRouteReply_Route) GetChildren() []*ListAssetRouteReply_Children {
	if x != nil {
		return x.Children
	}
	return nil
}

type GetAssetListReply_Asset struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uuid           string `protobuf:"bytes,1,opt,name=uuid,proto3" json:"uuid"`
	Name           string `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	AssetIp        string `protobuf:"bytes,3,opt,name=assetIp,proto3" json:"assetIp"`
	Active         string `protobuf:"bytes,4,opt,name=active,proto3" json:"active"`
	Platform       string `protobuf:"bytes,5,opt,name=platform,proto3" json:"platform"`
	ProxyGatewayId int64  `protobuf:"varint,6,opt,name=proxyGatewayId,proto3" json:"proxyGatewayId"`
	Protocol       string `protobuf:"bytes,7,opt,name=protocol,proto3" json:"protocol"`
	Os             string `protobuf:"bytes,8,opt,name=os,proto3" json:"os"`
	OrganizationId string `protobuf:"bytes,10,opt,name=organizationId,proto3" json:"organizationId"`
	Comment        string `protobuf:"bytes,11,opt,name=comment,proto3" json:"comment"`
	CreateUser     string `protobuf:"bytes,12,opt,name=createUser,proto3" json:"createUser"`
	Idc            string `protobuf:"bytes,13,opt,name=idc,proto3" json:"idc"`
	CreatedAt      string `protobuf:"bytes,14,opt,name=createdAt,proto3" json:"createdAt"`
	UpdatedAt      string `protobuf:"bytes,15,opt,name=updatedAt,proto3" json:"updatedAt"`
}

func (x *GetAssetListReply_Asset) Reset() {
	*x = GetAssetListReply_Asset{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_asset_v1_asset_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAssetListReply_Asset) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAssetListReply_Asset) ProtoMessage() {}

func (x *GetAssetListReply_Asset) ProtoReflect() protoreflect.Message {
	mi := &file_api_asset_v1_asset_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAssetListReply_Asset.ProtoReflect.Descriptor instead.
func (*GetAssetListReply_Asset) Descriptor() ([]byte, []int) {
	return file_api_asset_v1_asset_proto_rawDescGZIP(), []int{11, 0}
}

func (x *GetAssetListReply_Asset) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

func (x *GetAssetListReply_Asset) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GetAssetListReply_Asset) GetAssetIp() string {
	if x != nil {
		return x.AssetIp
	}
	return ""
}

func (x *GetAssetListReply_Asset) GetActive() string {
	if x != nil {
		return x.Active
	}
	return ""
}

func (x *GetAssetListReply_Asset) GetPlatform() string {
	if x != nil {
		return x.Platform
	}
	return ""
}

func (x *GetAssetListReply_Asset) GetProxyGatewayId() int64 {
	if x != nil {
		return x.ProxyGatewayId
	}
	return 0
}

func (x *GetAssetListReply_Asset) GetProtocol() string {
	if x != nil {
		return x.Protocol
	}
	return ""
}

func (x *GetAssetListReply_Asset) GetOs() string {
	if x != nil {
		return x.Os
	}
	return ""
}

func (x *GetAssetListReply_Asset) GetOrganizationId() string {
	if x != nil {
		return x.OrganizationId
	}
	return ""
}

func (x *GetAssetListReply_Asset) GetComment() string {
	if x != nil {
		return x.Comment
	}
	return ""
}

func (x *GetAssetListReply_Asset) GetCreateUser() string {
	if x != nil {
		return x.CreateUser
	}
	return ""
}

func (x *GetAssetListReply_Asset) GetIdc() string {
	if x != nil {
		return x.Idc
	}
	return ""
}

func (x *GetAssetListReply_Asset) GetCreatedAt() string {
	if x != nil {
		return x.CreatedAt
	}
	return ""
}

func (x *GetAssetListReply_Asset) GetUpdatedAt() string {
	if x != nil {
		return x.UpdatedAt
	}
	return ""
}

type GetAssetListByIpReply_Asset struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id             int32  `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	Name           string `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	AssetIp        string `protobuf:"bytes,3,opt,name=assetIp,proto3" json:"assetIp"`
	OrganizationId string `protobuf:"bytes,4,opt,name=organizationId,proto3" json:"organizationId"`
	Uuid           string `protobuf:"bytes,5,opt,name=uuid,proto3" json:"uuid"`
}

func (x *GetAssetListByIpReply_Asset) Reset() {
	*x = GetAssetListByIpReply_Asset{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_asset_v1_asset_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAssetListByIpReply_Asset) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAssetListByIpReply_Asset) ProtoMessage() {}

func (x *GetAssetListByIpReply_Asset) ProtoReflect() protoreflect.Message {
	mi := &file_api_asset_v1_asset_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAssetListByIpReply_Asset.ProtoReflect.Descriptor instead.
func (*GetAssetListByIpReply_Asset) Descriptor() ([]byte, []int) {
	return file_api_asset_v1_asset_proto_rawDescGZIP(), []int{13, 0}
}

func (x *GetAssetListByIpReply_Asset) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetAssetListByIpReply_Asset) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GetAssetListByIpReply_Asset) GetAssetIp() string {
	if x != nil {
		return x.AssetIp
	}
	return ""
}

func (x *GetAssetListByIpReply_Asset) GetOrganizationId() string {
	if x != nil {
		return x.OrganizationId
	}
	return ""
}

func (x *GetAssetListByIpReply_Asset) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

type GetAssetProxyGatewayListReply_AssetProxyGateway struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id             int32  `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	OrganizationId string `protobuf:"bytes,2,opt,name=organizationId,proto3" json:"organizationId"`
	Name           string `protobuf:"bytes,3,opt,name=name,proto3" json:"name"`
	Ip             string `protobuf:"bytes,4,opt,name=ip,proto3" json:"ip"`
	Port           int64  `protobuf:"varint,5,opt,name=port,proto3" json:"port"`
	Username       string `protobuf:"bytes,6,opt,name=username,proto3" json:"username"`
	Comment        string `protobuf:"bytes,7,opt,name=comment,proto3" json:"comment"`
	Idc            string `protobuf:"bytes,8,opt,name=idc,proto3" json:"idc"`
}

func (x *GetAssetProxyGatewayListReply_AssetProxyGateway) Reset() {
	*x = GetAssetProxyGatewayListReply_AssetProxyGateway{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_asset_v1_asset_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAssetProxyGatewayListReply_AssetProxyGateway) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAssetProxyGatewayListReply_AssetProxyGateway) ProtoMessage() {}

func (x *GetAssetProxyGatewayListReply_AssetProxyGateway) ProtoReflect() protoreflect.Message {
	mi := &file_api_asset_v1_asset_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAssetProxyGatewayListReply_AssetProxyGateway.ProtoReflect.Descriptor instead.
func (*GetAssetProxyGatewayListReply_AssetProxyGateway) Descriptor() ([]byte, []int) {
	return file_api_asset_v1_asset_proto_rawDescGZIP(), []int{18, 0}
}

func (x *GetAssetProxyGatewayListReply_AssetProxyGateway) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetAssetProxyGatewayListReply_AssetProxyGateway) GetOrganizationId() string {
	if x != nil {
		return x.OrganizationId
	}
	return ""
}

func (x *GetAssetProxyGatewayListReply_AssetProxyGateway) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GetAssetProxyGatewayListReply_AssetProxyGateway) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *GetAssetProxyGatewayListReply_AssetProxyGateway) GetPort() int64 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *GetAssetProxyGatewayListReply_AssetProxyGateway) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *GetAssetProxyGatewayListReply_AssetProxyGateway) GetComment() string {
	if x != nil {
		return x.Comment
	}
	return ""
}

func (x *GetAssetProxyGatewayListReply_AssetProxyGateway) GetIdc() string {
	if x != nil {
		return x.Idc
	}
	return ""
}

type GetAssetAdminUserListReply_AssetAdminUser struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id             int32  `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	OrganizationId string `protobuf:"bytes,2,opt,name=organizationId,proto3" json:"organizationId"`
	Username       string `protobuf:"bytes,3,opt,name=username,proto3" json:"username"`
}

func (x *GetAssetAdminUserListReply_AssetAdminUser) Reset() {
	*x = GetAssetAdminUserListReply_AssetAdminUser{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_asset_v1_asset_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAssetAdminUserListReply_AssetAdminUser) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAssetAdminUserListReply_AssetAdminUser) ProtoMessage() {}

func (x *GetAssetAdminUserListReply_AssetAdminUser) ProtoReflect() protoreflect.Message {
	mi := &file_api_asset_v1_asset_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAssetAdminUserListReply_AssetAdminUser.ProtoReflect.Descriptor instead.
func (*GetAssetAdminUserListReply_AssetAdminUser) Descriptor() ([]byte, []int) {
	return file_api_asset_v1_asset_proto_rawDescGZIP(), []int{23, 0}
}

func (x *GetAssetAdminUserListReply_AssetAdminUser) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetAssetAdminUserListReply_AssetAdminUser) GetOrganizationId() string {
	if x != nil {
		return x.OrganizationId
	}
	return ""
}

func (x *GetAssetAdminUserListReply_AssetAdminUser) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

var File_api_asset_v1_asset_proto protoreflect.FileDescriptor

var file_api_asset_v1_asset_proto_rawDesc = []byte{
	0x0a, 0x18, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x61,
	0x73, 0x73, 0x65, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x08, 0x61, 0x73, 0x73, 0x65,
	0x74, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70,
	0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xc0, 0x02, 0x0a, 0x1f, 0x47, 0x65, 0x74,
	0x41, 0x73, 0x73, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x78, 0x79, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x4f, 0x0a, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x61, 0x73, 0x73,
	0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x50, 0x72,
	0x6f, 0x78, 0x79, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x78, 0x79,
	0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x1a, 0xcb, 0x01,
	0x0a, 0x11, 0x41, 0x73, 0x73, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x78, 0x79, 0x47, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x26, 0x0a, 0x0e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6f, 0x72, 0x67,
	0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x12,
	0x12, 0x0a, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x70,
	0x6f, 0x72, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x64, 0x63,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x69, 0x64, 0x63, 0x22, 0x51, 0x0a, 0x15, 0x55,
	0x73, 0x65, 0x72, 0x41, 0x73, 0x73, 0x65, 0x74, 0x41, 0x6c, 0x69, 0x61, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12,
	0x1d, 0x0a, 0x05, 0x61, 0x6c, 0x69, 0x61, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x05, 0x61, 0x6c, 0x69, 0x61, 0x73, 0x22, 0x8d,
	0x01, 0x0a, 0x16, 0x4c, 0x69, 0x73, 0x74, 0x55, 0x73, 0x65, 0x72, 0x57, 0x6f, 0x72, 0x6b, 0x53,
	0x70, 0x61, 0x63, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x3e, 0x0a, 0x04, 0x64, 0x61, 0x74,
	0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x55, 0x73, 0x65, 0x72, 0x57, 0x6f, 0x72, 0x6b, 0x53,
	0x70, 0x61, 0x63, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x53, 0x70,
	0x61, 0x63, 0x65, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x1a, 0x33, 0x0a, 0x09, 0x57, 0x6f, 0x72,
	0x6b, 0x53, 0x70, 0x61, 0x63, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x61, 0x62, 0x65,
	0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x22, 0x9a,
	0x03, 0x0a, 0x12, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x19, 0x0a, 0x03, 0x69, 0x64, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x03, 0x69, 0x64, 0x63, 0x12, 0x21, 0x0a,
	0x07, 0x61, 0x73, 0x73, 0x65, 0x74, 0x49, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x61, 0x73, 0x73, 0x65, 0x74, 0x49, 0x70,
	0x12, 0x1f, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x65, 0x12, 0x23, 0x0a, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x08, 0x70, 0x6c,
	0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x12, 0x2f, 0x0a, 0x0e, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x47,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x49, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0e, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x47, 0x61,
	0x74, 0x65, 0x77, 0x61, 0x79, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x63, 0x6f, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02,
	0x10, 0x01, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x12, 0x29, 0x0a, 0x0b,
	0x61, 0x73, 0x73, 0x65, 0x74, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0b, 0x61, 0x73, 0x73, 0x65,
	0x74, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x17, 0x0a, 0x02, 0x6f, 0x73, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x02, 0x6f, 0x73,
	0x12, 0x2f, 0x0a, 0x0e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x49, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10,
	0x01, 0x52, 0x0e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49,
	0x64, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x22, 0x95, 0x03, 0x0a, 0x12,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x73, 0x65, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x1b, 0x0a, 0x04, 0x75, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x04, 0x75, 0x75, 0x69, 0x64, 0x12,
	0x1b, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x07,
	0x61, 0x73, 0x73, 0x65, 0x74, 0x49, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x61, 0x73, 0x73, 0x65, 0x74, 0x49, 0x70, 0x12,
	0x1f, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x12, 0x23, 0x0a, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x08, 0x70, 0x6c, 0x61,
	0x74, 0x66, 0x6f, 0x72, 0x6d, 0x12, 0x2f, 0x0a, 0x0e, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x47, 0x61,
	0x74, 0x65, 0x77, 0x61, 0x79, 0x49, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0e, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x47, 0x61, 0x74,
	0x65, 0x77, 0x61, 0x79, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63,
	0x6f, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10,
	0x01, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x12, 0x17, 0x0a, 0x02, 0x6f,
	0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01,
	0x52, 0x02, 0x6f, 0x73, 0x12, 0x2f, 0x0a, 0x0e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52,
	0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x19, 0x0a, 0x03, 0x69, 0x64, 0x63, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x03,
	0x69, 0x64, 0x63, 0x22, 0x28, 0x0a, 0x12, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x73, 0x73,
	0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x75, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x75, 0x69, 0x64, 0x22, 0x3f, 0x0a,
	0x15, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e,
	0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0xd2,
	0x02, 0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x6f, 0x75, 0x74,
	0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x3d, 0x0a, 0x07, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x72,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x6f, 0x75, 0x74,
	0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x52, 0x07, 0x72, 0x6f,
	0x75, 0x74, 0x65, 0x72, 0x73, 0x1a, 0x86, 0x01, 0x0a, 0x08, 0x43, 0x68, 0x69, 0x6c, 0x64, 0x72,
	0x65, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x61,
	0x76, 0x6f, 0x72, 0x69, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x66, 0x61,
	0x76, 0x6f, 0x72, 0x69, 0x74, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x12, 0x26, 0x0a, 0x0e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e,
	0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x1a, 0x73,
	0x0a, 0x05, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74,
	0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12,
	0x42, 0x0a, 0x08, 0x63, 0x68, 0x69, 0x6c, 0x64, 0x72, 0x65, 0x6e, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x26, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x2e, 0x43, 0x68, 0x69, 0x6c, 0x64, 0x72, 0x65, 0x6e, 0x52, 0x08, 0x63, 0x68, 0x69, 0x6c, 0x64,
	0x72, 0x65, 0x6e, 0x22, 0x21, 0x0a, 0x05, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x18, 0x0a, 0x07,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x5e, 0x0a, 0x18, 0x55, 0x73, 0x65, 0x72, 0x41, 0x73,
	0x73, 0x65, 0x74, 0x46, 0x61, 0x76, 0x6f, 0x72, 0x69, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x21, 0x0a, 0x07, 0x61, 0x73, 0x73, 0x65, 0x74, 0x49, 0x70, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x61, 0x73,
	0x73, 0x65, 0x74, 0x49, 0x70, 0x12, 0x1f, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x06,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xe6, 0x01, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x41, 0x73,
	0x73, 0x65, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x23,
	0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53,
	0x69, 0x7a, 0x65, 0x12, 0x28, 0x0a, 0x06, 0x70, 0x61, 0x67, 0x65, 0x4e, 0x6f, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x10, 0xfa, 0x42, 0x0d, 0x22, 0x0b, 0x20, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0x01, 0x52, 0x06, 0x70, 0x61, 0x67, 0x65, 0x4e, 0x6f, 0x12, 0x1e, 0x0a,
	0x0a, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x54, 0x65, 0x78, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x54, 0x65, 0x78, 0x74, 0x12, 0x10, 0x0a,
	0x03, 0x69, 0x64, 0x63, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x69, 0x64, 0x63, 0x12,
	0x26, 0x0a, 0x0e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49,
	0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0e, 0x70, 0x72, 0x6f, 0x78, 0x79,
	0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x49, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0e, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x49, 0x64, 0x22,
	0xc0, 0x04, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x35, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x65, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x16, 0x0a, 0x06,
	0x70, 0x61, 0x67, 0x65, 0x4e, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x70, 0x61,
	0x67, 0x65, 0x4e, 0x6f, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65,
	0x12, 0x1e, 0x0a, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x12, 0x1c, 0x0a, 0x09, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x50, 0x61, 0x67, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x09, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x50, 0x61, 0x67, 0x65, 0x1a, 0x81,
	0x03, 0x0a, 0x05, 0x41, 0x73, 0x73, 0x65, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x75, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x75, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x61, 0x73, 0x73, 0x65, 0x74, 0x49, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x61, 0x73, 0x73, 0x65, 0x74, 0x49, 0x70, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x63,
	0x74, 0x69, 0x76, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69,
	0x76, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x12, 0x26,
	0x0a, 0x0e, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x49, 0x64,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x47, 0x61, 0x74,
	0x65, 0x77, 0x61, 0x79, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63,
	0x6f, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63,
	0x6f, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x6f, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02,
	0x6f, 0x73, 0x12, 0x26, 0x0a, 0x0e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x49, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6f, 0x72, 0x67, 0x61,
	0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f,
	0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6d,
	0x6d, 0x65, 0x6e, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x55, 0x73,
	0x65, 0x72, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x55, 0x73, 0x65, 0x72, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x64, 0x63, 0x18, 0x0d, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x69, 0x64, 0x63, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x41, 0x74, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x41, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41,
	0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64,
	0x41, 0x74, 0x22, 0xa0, 0x01, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x4c,
	0x69, 0x73, 0x74, 0x42, 0x79, 0x49, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x23,
	0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53,
	0x69, 0x7a, 0x65, 0x12, 0x28, 0x0a, 0x06, 0x70, 0x61, 0x67, 0x65, 0x4e, 0x6f, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x10, 0xfa, 0x42, 0x0d, 0x22, 0x0b, 0x20, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0x01, 0x52, 0x06, 0x70, 0x61, 0x67, 0x65, 0x4e, 0x6f, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x12, 0x26, 0x0a,
	0x0e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0xc8, 0x02, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x41, 0x73, 0x73,
	0x65, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x49, 0x70, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12,
	0x39, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e,
	0x61, 0x73, 0x73, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x73, 0x73, 0x65,
	0x74, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x49, 0x70, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x41,
	0x73, 0x73, 0x65, 0x74, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x61,
	0x67, 0x65, 0x4e, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x70, 0x61, 0x67, 0x65,
	0x4e, 0x6f, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1e,
	0x0a, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1c,
	0x0a, 0x09, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x50, 0x61, 0x67, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x09, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x50, 0x61, 0x67, 0x65, 0x1a, 0x81, 0x01, 0x0a,
	0x05, 0x41, 0x73, 0x73, 0x65, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x73,
	0x73, 0x65, 0x74, 0x49, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x73, 0x73,
	0x65, 0x74, 0x49, 0x70, 0x12, 0x26, 0x0a, 0x0e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6f, 0x72,
	0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04,
	0x75, 0x75, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x75, 0x69, 0x64,
	0x22, 0x90, 0x01, 0x0a, 0x1f, 0x47, 0x65, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x50, 0x72, 0x6f,
	0x78, 0x79, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x23, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x28, 0x0a, 0x06, 0x70, 0x61, 0x67,
	0x65, 0x4e, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x10, 0xfa, 0x42, 0x0d, 0x22, 0x0b,
	0x20, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x01, 0x52, 0x06, 0x70, 0x61, 0x67,
	0x65, 0x4e, 0x6f, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x54, 0x65, 0x78,
	0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x54,
	0x65, 0x78, 0x74, 0x22, 0xba, 0x02, 0x0a, 0x1e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x73,
	0x73, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x78, 0x79, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x2f, 0x0a, 0x0e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x72, 0x02, 0x10, 0x01, 0x52, 0x0e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x02, 0x69, 0x70, 0x12, 0x1b, 0x0a,
	0x04, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x23, 0x0a, 0x08, 0x75, 0x73,
	0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x70,
	0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x4b, 0x65, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x70, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x4b, 0x65, 0x79, 0x12, 0x18, 0x0a, 0x07, 0x63,
	0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f,
	0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x19, 0x0a, 0x03, 0x69, 0x64, 0x63, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x03, 0x69, 0x64, 0x63,
	0x22, 0xca, 0x02, 0x0a, 0x1e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74,
	0x50, 0x72, 0x6f, 0x78, 0x79, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x1b, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x2f, 0x0a, 0x0e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10,
	0x01, 0x52, 0x0e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49,
	0x64, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x02, 0x69, 0x70, 0x12, 0x1b, 0x0a, 0x04, 0x70, 0x6f,
	0x72, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x23, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02,
	0x10, 0x01, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08,
	0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x72, 0x69, 0x76,
	0x61, 0x74, 0x65, 0x4b, 0x65, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x72,
	0x69, 0x76, 0x61, 0x74, 0x65, 0x4b, 0x65, 0x79, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6d, 0x6d,
	0x65, 0x6e, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x65,
	0x6e, 0x74, 0x12, 0x19, 0x0a, 0x03, 0x69, 0x64, 0x63, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x03, 0x69, 0x64, 0x63, 0x22, 0x30, 0x0a,
	0x1e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x78,
	0x79, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x22,
	0xae, 0x03, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x78,
	0x79, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x12, 0x4d, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x39, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x73,
	0x73, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x78, 0x79, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x50, 0x72,
	0x6f, 0x78, 0x79, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61,
	0x12, 0x16, 0x0a, 0x06, 0x70, 0x61, 0x67, 0x65, 0x4e, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x06, 0x70, 0x61, 0x67, 0x65, 0x4e, 0x6f, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65,
	0x53, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65,
	0x53, 0x69, 0x7a, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x50, 0x61, 0x67,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x50, 0x61,
	0x67, 0x65, 0x1a, 0xcb, 0x01, 0x0a, 0x11, 0x41, 0x73, 0x73, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x78,
	0x79, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x26, 0x0a, 0x0e, 0x6f, 0x72, 0x67, 0x61,
	0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x02, 0x69, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x10,
	0x0a, 0x03, 0x69, 0x64, 0x63, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x69, 0x64, 0x63,
	0x22, 0x8d, 0x01, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x41, 0x64, 0x6d,
	0x69, 0x6e, 0x55, 0x73, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x23, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x08, 0x70, 0x61,
	0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x28, 0x0a, 0x06, 0x70, 0x61, 0x67, 0x65, 0x4e, 0x6f,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x10, 0xfa, 0x42, 0x0d, 0x22, 0x0b, 0x20, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x01, 0x52, 0x06, 0x70, 0x61, 0x67, 0x65, 0x4e, 0x6f,
	0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x54, 0x65, 0x78, 0x74, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x54, 0x65, 0x78, 0x74,
	0x22, 0xd2, 0x01, 0x0a, 0x1b, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74,
	0x41, 0x64, 0x6d, 0x69, 0x6e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x2f, 0x0a, 0x0e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10,
	0x01, 0x52, 0x0e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49,
	0x64, 0x12, 0x23, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x08, 0x75, 0x73,
	0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f,
	0x72, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f,
	0x72, 0x64, 0x12, 0x27, 0x0a, 0x0a, 0x70, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x4b, 0x65, 0x79,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52,
	0x0a, 0x70, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x4b, 0x65, 0x79, 0x12, 0x18, 0x0a, 0x07, 0x63,
	0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f,
	0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x22, 0xe2, 0x01, 0x0a, 0x1b, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x41, 0x73, 0x73, 0x65, 0x74, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x2f, 0x0a, 0x0e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10,
	0x01, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70,
	0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70,
	0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x27, 0x0a, 0x0a, 0x70, 0x72, 0x69, 0x76, 0x61,
	0x74, 0x65, 0x4b, 0x65, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x72, 0x02, 0x10, 0x01, 0x52, 0x0a, 0x70, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x4b, 0x65, 0x79,
	0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x22, 0x2d, 0x0a, 0x1b, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x55, 0x73,
	0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x22, 0xbd, 0x02, 0x0a, 0x1a, 0x47, 0x65,
	0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x55, 0x73, 0x65, 0x72, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x47, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x55,
	0x73, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x41, 0x73, 0x73,
	0x65, 0x74, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x04, 0x64, 0x61, 0x74,
	0x61, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x61, 0x67, 0x65, 0x4e, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x06, 0x70, 0x61, 0x67, 0x65, 0x4e, 0x6f, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x67,
	0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x70, 0x61, 0x67,
	0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x50, 0x61,
	0x67, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x50,
	0x61, 0x67, 0x65, 0x1a, 0x64, 0x0a, 0x0e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x41, 0x64, 0x6d, 0x69,
	0x6e, 0x55, 0x73, 0x65, 0x72, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x26, 0x0a, 0x0e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6f,
	0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1a, 0x0a,
	0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x32, 0x88, 0x10, 0x0a, 0x05, 0x41, 0x73,
	0x73, 0x65, 0x74, 0x12, 0x64, 0x0a, 0x0c, 0x47, 0x65, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x4c,
	0x69, 0x73, 0x74, 0x12, 0x1d, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x65, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65,
	0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22,
	0x18, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x12, 0x12, 0x10, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x73, 0x73,
	0x65, 0x74, 0x2f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x12, 0x59, 0x0a, 0x0b, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x12, 0x1c, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x0f, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x1b, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x15, 0x22,
	0x10, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x2f, 0x61, 0x73, 0x73, 0x65, 0x74,
	0x73, 0x3a, 0x01, 0x2a, 0x12, 0x59, 0x0a, 0x0b, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x73,
	0x73, 0x65, 0x74, 0x12, 0x1c, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x73, 0x65, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x0f, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x22, 0x1b, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x15, 0x1a, 0x10, 0x2f, 0x76, 0x31, 0x2f,
	0x61, 0x73, 0x73, 0x65, 0x74, 0x2f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x3a, 0x01, 0x2a, 0x12,
	0x5d, 0x0a, 0x0b, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x12, 0x1c,
	0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x0f, 0x2e, 0x61,
	0x73, 0x73, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x1f, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x19, 0x2a, 0x17, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x73, 0x73, 0x65, 0x74,
	0x2f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2f, 0x7b, 0x75, 0x75, 0x69, 0x64, 0x7d, 0x12, 0x67,
	0x0a, 0x11, 0x55, 0x73, 0x65, 0x72, 0x41, 0x73, 0x73, 0x65, 0x74, 0x46, 0x61, 0x76, 0x6f, 0x72,
	0x69, 0x74, 0x65, 0x12, 0x22, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x55,
	0x73, 0x65, 0x72, 0x41, 0x73, 0x73, 0x65, 0x74, 0x46, 0x61, 0x76, 0x6f, 0x72, 0x69, 0x74, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x0f, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x1d, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x17,
	0x22, 0x12, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x2f, 0x66, 0x61, 0x76, 0x6f,
	0x72, 0x69, 0x74, 0x65, 0x3a, 0x01, 0x2a, 0x12, 0x6a, 0x0a, 0x0e, 0x4c, 0x69, 0x73, 0x74, 0x41,
	0x73, 0x73, 0x65, 0x74, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x12, 0x1f, 0x2e, 0x61, 0x73, 0x73, 0x65,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x6f,
	0x75, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1d, 0x2e, 0x61, 0x73, 0x73,
	0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52,
	0x6f, 0x75, 0x74, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x18, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x12, 0x12, 0x10, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x2f, 0x72, 0x6f, 0x75,
	0x74, 0x65, 0x73, 0x12, 0x70, 0x0a, 0x11, 0x4c, 0x69, 0x73, 0x74, 0x55, 0x73, 0x65, 0x72, 0x57,
	0x6f, 0x72, 0x6b, 0x53, 0x70, 0x61, 0x63, 0x65, 0x12, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79,
	0x1a, 0x20, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74,
	0x55, 0x73, 0x65, 0x72, 0x57, 0x6f, 0x72, 0x6b, 0x53, 0x70, 0x61, 0x63, 0x65, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x22, 0x21, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1b, 0x12, 0x19, 0x2f, 0x76, 0x31, 0x2f,
	0x61, 0x73, 0x73, 0x65, 0x74, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2d, 0x77, 0x6f, 0x72, 0x6b, 0x2d,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x5e, 0x0a, 0x0e, 0x75, 0x73, 0x65, 0x72, 0x41, 0x73, 0x73,
	0x65, 0x74, 0x41, 0x6c, 0x69, 0x61, 0x73, 0x12, 0x1f, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x41, 0x73, 0x73, 0x65, 0x74, 0x41, 0x6c, 0x69, 0x61,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x0f, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x1a, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x14, 0x22, 0x0f, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x2f, 0x61, 0x6c, 0x69,
	0x61, 0x73, 0x3a, 0x01, 0x2a, 0x12, 0x6d, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x41, 0x73, 0x73, 0x65,
	0x74, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x49, 0x70, 0x12, 0x21, 0x2e, 0x61, 0x73, 0x73, 0x65,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x4c, 0x69, 0x73,
	0x74, 0x42, 0x79, 0x49, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e, 0x61,
	0x73, 0x73, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74,
	0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x49, 0x70, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x15, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x0f, 0x12, 0x0d, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x73, 0x73, 0x65, 0x74,
	0x2f, 0x69, 0x70, 0x73, 0x12, 0x88, 0x01, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x41, 0x73, 0x73, 0x65,
	0x74, 0x50, 0x72, 0x6f, 0x78, 0x79, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x53, 0x65, 0x6c,
	0x65, 0x63, 0x74, 0x12, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x29, 0x2e, 0x61, 0x73,
	0x73, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x50,
	0x72, 0x6f, 0x78, 0x79, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x53, 0x65, 0x6c, 0x65, 0x63,
	0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x27, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x21, 0x12, 0x1f,
	0x2f, 0x76, 0x31, 0x2f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x2f, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x2d,
	0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x73, 0x2f, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x12,
	0x90, 0x01, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x78,
	0x79, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x29, 0x2e, 0x61,
	0x73, 0x73, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74,
	0x50, 0x72, 0x6f, 0x78, 0x79, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x78, 0x79,
	0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x22, 0x20, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1a, 0x12, 0x18, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x73,
	0x73, 0x65, 0x74, 0x2f, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x2d, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x73, 0x12, 0x79, 0x0a, 0x17, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x73, 0x73, 0x65,
	0x74, 0x50, 0x72, 0x6f, 0x78, 0x79, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x12, 0x28, 0x2e,
	0x61, 0x73, 0x73, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41,
	0x73, 0x73, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x78, 0x79, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x0f, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x23, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1d,
	0x22, 0x18, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x2f, 0x70, 0x72, 0x6f, 0x78,
	0x79, 0x2d, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x73, 0x3a, 0x01, 0x2a, 0x12, 0x79, 0x0a,
	0x17, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x78,
	0x79, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x12, 0x28, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x50,
	0x72, 0x6f, 0x78, 0x79, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x0f, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x22, 0x23, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1d, 0x1a, 0x18, 0x2f, 0x76, 0x31,
	0x2f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x2f, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x2d, 0x67, 0x61, 0x74,
	0x65, 0x77, 0x61, 0x79, 0x73, 0x3a, 0x01, 0x2a, 0x12, 0x7b, 0x0a, 0x17, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x78, 0x79, 0x47, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x12, 0x28, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x78, 0x79, 0x47,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x0f, 0x2e,
	0x61, 0x73, 0x73, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x25,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1f, 0x2a, 0x1d, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x73, 0x73, 0x65,
	0x74, 0x2f, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x2d, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x73,
	0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x12, 0x84, 0x01, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x41, 0x73, 0x73,
	0x65, 0x74, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x55, 0x73, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x12,
	0x26, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x73,
	0x73, 0x65, 0x74, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x55, 0x73, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x41, 0x64, 0x6d, 0x69, 0x6e,
	0x55, 0x73, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x1d, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x17, 0x12, 0x15, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x73, 0x73, 0x65, 0x74,
	0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2d, 0x75, 0x73, 0x65, 0x72, 0x73, 0x12, 0x70, 0x0a, 0x14,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x41, 0x64, 0x6d, 0x69, 0x6e,
	0x55, 0x73, 0x65, 0x72, 0x12, 0x25, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x41, 0x64, 0x6d, 0x69, 0x6e,
	0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x0f, 0x2e, 0x61, 0x73,
	0x73, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x20, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x1a, 0x22, 0x15, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x2f,
	0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2d, 0x75, 0x73, 0x65, 0x72, 0x73, 0x3a, 0x01, 0x2a, 0x12, 0x70,
	0x0a, 0x14, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x41, 0x64, 0x6d,
	0x69, 0x6e, 0x55, 0x73, 0x65, 0x72, 0x12, 0x25, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x41, 0x64, 0x6d,
	0x69, 0x6e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x0f, 0x2e,
	0x61, 0x73, 0x73, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x20,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1a, 0x1a, 0x15, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x73, 0x73, 0x65,
	0x74, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2d, 0x75, 0x73, 0x65, 0x72, 0x73, 0x3a, 0x01, 0x2a,
	0x12, 0x72, 0x0a, 0x14, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x41,
	0x64, 0x6d, 0x69, 0x6e, 0x55, 0x73, 0x65, 0x72, 0x12, 0x25, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x41,
	0x64, 0x6d, 0x69, 0x6e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x0f, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x22, 0x22, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1c, 0x2a, 0x1a, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x73,
	0x73, 0x65, 0x74, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2d, 0x75, 0x73, 0x65, 0x72, 0x73, 0x2f,
	0x7b, 0x69, 0x64, 0x7d, 0x42, 0x3e, 0x5a, 0x3c, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x69,
	0x6e, 0x74, 0x73, 0x69, 0x67, 0x2e, 0x6e, 0x65, 0x74, 0x2f, 0x4e, 0x4f, 0x43, 0x2f, 0x63, 0x74,
	0x6d, 0x2f, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x2f, 0x63, 0x74, 0x6d, 0x5f, 0x62, 0x61, 0x63,
	0x6b, 0x65, 0x6e, 0x64, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x2f, 0x76,
	0x31, 0x3b, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_asset_v1_asset_proto_rawDescOnce sync.Once
	file_api_asset_v1_asset_proto_rawDescData = file_api_asset_v1_asset_proto_rawDesc
)

func file_api_asset_v1_asset_proto_rawDescGZIP() []byte {
	file_api_asset_v1_asset_proto_rawDescOnce.Do(func() {
		file_api_asset_v1_asset_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_asset_v1_asset_proto_rawDescData)
	})
	return file_api_asset_v1_asset_proto_rawDescData
}

var file_api_asset_v1_asset_proto_msgTypes = make([]protoimpl.MessageInfo, 32)
var file_api_asset_v1_asset_proto_goTypes = []interface{}{
	(*GetAssetProxyGatewaySelectReply)(nil),                   // 0: asset.v1.GetAssetProxyGatewaySelectReply
	(*UserAssetAliasRequest)(nil),                             // 1: asset.v1.UserAssetAliasRequest
	(*ListUserWorkSpaceReply)(nil),                            // 2: asset.v1.ListUserWorkSpaceReply
	(*CreateAssetRequest)(nil),                                // 3: asset.v1.CreateAssetRequest
	(*UpdateAseetRequest)(nil),                                // 4: asset.v1.UpdateAseetRequest
	(*DeleteAssetRequest)(nil),                                // 5: asset.v1.DeleteAssetRequest
	(*ListAssetRouteRequest)(nil),                             // 6: asset.v1.ListAssetRouteRequest
	(*ListAssetRouteReply)(nil),                               // 7: asset.v1.ListAssetRouteReply
	(*Reply)(nil),                                             // 8: asset.v1.Reply
	(*UserAssetFavoriteRequest)(nil),                          // 9: asset.v1.UserAssetFavoriteRequest
	(*GetAssetListRequest)(nil),                               // 10: asset.v1.GetAssetListRequest
	(*GetAssetListReply)(nil),                                 // 11: asset.v1.GetAssetListReply
	(*GetAssetListByIpRequest)(nil),                           // 12: asset.v1.GetAssetListByIpRequest
	(*GetAssetListByIpReply)(nil),                             // 13: asset.v1.GetAssetListByIpReply
	(*GetAssetProxyGatewayListRequest)(nil),                   // 14: asset.v1.GetAssetProxyGatewayListRequest
	(*CreateAssetProxyGatewayRequest)(nil),                    // 15: asset.v1.CreateAssetProxyGatewayRequest
	(*UpdateAssetProxyGatewayRequest)(nil),                    // 16: asset.v1.UpdateAssetProxyGatewayRequest
	(*DeleteAssetProxyGatewayRequest)(nil),                    // 17: asset.v1.DeleteAssetProxyGatewayRequest
	(*GetAssetProxyGatewayListReply)(nil),                     // 18: asset.v1.GetAssetProxyGatewayListReply
	(*GetAssetAdminUserListRequest)(nil),                      // 19: asset.v1.GetAssetAdminUserListRequest
	(*CreateAssetAdminUserRequest)(nil),                       // 20: asset.v1.CreateAssetAdminUserRequest
	(*UpdateAssetAdminUserRequest)(nil),                       // 21: asset.v1.UpdateAssetAdminUserRequest
	(*DeleteAssetAdminUserRequest)(nil),                       // 22: asset.v1.DeleteAssetAdminUserRequest
	(*GetAssetAdminUserListReply)(nil),                        // 23: asset.v1.GetAssetAdminUserListReply
	(*GetAssetProxyGatewaySelectReply_AssetProxyGateway)(nil), // 24: asset.v1.GetAssetProxyGatewaySelectReply.AssetProxyGateway
	(*ListUserWorkSpaceReply_WorkSpace)(nil),                  // 25: asset.v1.ListUserWorkSpaceReply.WorkSpace
	(*ListAssetRouteReply_Children)(nil),                      // 26: asset.v1.ListAssetRouteReply.Children
	(*ListAssetRouteReply_Route)(nil),                         // 27: asset.v1.ListAssetRouteReply.Route
	(*GetAssetListReply_Asset)(nil),                           // 28: asset.v1.GetAssetListReply.Asset
	(*GetAssetListByIpReply_Asset)(nil),                       // 29: asset.v1.GetAssetListByIpReply.Asset
	(*GetAssetProxyGatewayListReply_AssetProxyGateway)(nil),   // 30: asset.v1.GetAssetProxyGatewayListReply.AssetProxyGateway
	(*GetAssetAdminUserListReply_AssetAdminUser)(nil),         // 31: asset.v1.GetAssetAdminUserListReply.AssetAdminUser
	(*emptypb.Empty)(nil),                                     // 32: google.protobuf.Empty
}
var file_api_asset_v1_asset_proto_depIdxs = []int32{
	24, // 0: asset.v1.GetAssetProxyGatewaySelectReply.data:type_name -> asset.v1.GetAssetProxyGatewaySelectReply.AssetProxyGateway
	25, // 1: asset.v1.ListUserWorkSpaceReply.data:type_name -> asset.v1.ListUserWorkSpaceReply.WorkSpace
	27, // 2: asset.v1.ListAssetRouteReply.routers:type_name -> asset.v1.ListAssetRouteReply.Route
	28, // 3: asset.v1.GetAssetListReply.data:type_name -> asset.v1.GetAssetListReply.Asset
	29, // 4: asset.v1.GetAssetListByIpReply.data:type_name -> asset.v1.GetAssetListByIpReply.Asset
	30, // 5: asset.v1.GetAssetProxyGatewayListReply.data:type_name -> asset.v1.GetAssetProxyGatewayListReply.AssetProxyGateway
	31, // 6: asset.v1.GetAssetAdminUserListReply.data:type_name -> asset.v1.GetAssetAdminUserListReply.AssetAdminUser
	26, // 7: asset.v1.ListAssetRouteReply.Route.children:type_name -> asset.v1.ListAssetRouteReply.Children
	10, // 8: asset.v1.Asset.GetAssetList:input_type -> asset.v1.GetAssetListRequest
	3,  // 9: asset.v1.Asset.CreateAsset:input_type -> asset.v1.CreateAssetRequest
	4,  // 10: asset.v1.Asset.UpdateAsset:input_type -> asset.v1.UpdateAseetRequest
	5,  // 11: asset.v1.Asset.DeleteAsset:input_type -> asset.v1.DeleteAssetRequest
	9,  // 12: asset.v1.Asset.UserAssetFavorite:input_type -> asset.v1.UserAssetFavoriteRequest
	6,  // 13: asset.v1.Asset.ListAssetRoute:input_type -> asset.v1.ListAssetRouteRequest
	32, // 14: asset.v1.Asset.ListUserWorkSpace:input_type -> google.protobuf.Empty
	1,  // 15: asset.v1.Asset.userAssetAlias:input_type -> asset.v1.UserAssetAliasRequest
	12, // 16: asset.v1.Asset.GetAssetListByIp:input_type -> asset.v1.GetAssetListByIpRequest
	32, // 17: asset.v1.Asset.GetAssetProxyGatewaySelect:input_type -> google.protobuf.Empty
	14, // 18: asset.v1.Asset.GetAssetProxyGatewayList:input_type -> asset.v1.GetAssetProxyGatewayListRequest
	15, // 19: asset.v1.Asset.CreateAssetProxyGateway:input_type -> asset.v1.CreateAssetProxyGatewayRequest
	16, // 20: asset.v1.Asset.UpdateAssetProxyGateway:input_type -> asset.v1.UpdateAssetProxyGatewayRequest
	17, // 21: asset.v1.Asset.DeleteAssetProxyGateway:input_type -> asset.v1.DeleteAssetProxyGatewayRequest
	19, // 22: asset.v1.Asset.GetAssetAdminUserList:input_type -> asset.v1.GetAssetAdminUserListRequest
	20, // 23: asset.v1.Asset.CreateAssetAdminUser:input_type -> asset.v1.CreateAssetAdminUserRequest
	21, // 24: asset.v1.Asset.UpdateAssetAdminUser:input_type -> asset.v1.UpdateAssetAdminUserRequest
	22, // 25: asset.v1.Asset.DeleteAssetAdminUser:input_type -> asset.v1.DeleteAssetAdminUserRequest
	11, // 26: asset.v1.Asset.GetAssetList:output_type -> asset.v1.GetAssetListReply
	8,  // 27: asset.v1.Asset.CreateAsset:output_type -> asset.v1.Reply
	8,  // 28: asset.v1.Asset.UpdateAsset:output_type -> asset.v1.Reply
	8,  // 29: asset.v1.Asset.DeleteAsset:output_type -> asset.v1.Reply
	8,  // 30: asset.v1.Asset.UserAssetFavorite:output_type -> asset.v1.Reply
	7,  // 31: asset.v1.Asset.ListAssetRoute:output_type -> asset.v1.ListAssetRouteReply
	2,  // 32: asset.v1.Asset.ListUserWorkSpace:output_type -> asset.v1.ListUserWorkSpaceReply
	8,  // 33: asset.v1.Asset.userAssetAlias:output_type -> asset.v1.Reply
	13, // 34: asset.v1.Asset.GetAssetListByIp:output_type -> asset.v1.GetAssetListByIpReply
	0,  // 35: asset.v1.Asset.GetAssetProxyGatewaySelect:output_type -> asset.v1.GetAssetProxyGatewaySelectReply
	18, // 36: asset.v1.Asset.GetAssetProxyGatewayList:output_type -> asset.v1.GetAssetProxyGatewayListReply
	8,  // 37: asset.v1.Asset.CreateAssetProxyGateway:output_type -> asset.v1.Reply
	8,  // 38: asset.v1.Asset.UpdateAssetProxyGateway:output_type -> asset.v1.Reply
	8,  // 39: asset.v1.Asset.DeleteAssetProxyGateway:output_type -> asset.v1.Reply
	23, // 40: asset.v1.Asset.GetAssetAdminUserList:output_type -> asset.v1.GetAssetAdminUserListReply
	8,  // 41: asset.v1.Asset.CreateAssetAdminUser:output_type -> asset.v1.Reply
	8,  // 42: asset.v1.Asset.UpdateAssetAdminUser:output_type -> asset.v1.Reply
	8,  // 43: asset.v1.Asset.DeleteAssetAdminUser:output_type -> asset.v1.Reply
	26, // [26:44] is the sub-list for method output_type
	8,  // [8:26] is the sub-list for method input_type
	8,  // [8:8] is the sub-list for extension type_name
	8,  // [8:8] is the sub-list for extension extendee
	0,  // [0:8] is the sub-list for field type_name
}

func init() { file_api_asset_v1_asset_proto_init() }
func file_api_asset_v1_asset_proto_init() {
	if File_api_asset_v1_asset_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_asset_v1_asset_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAssetProxyGatewaySelectReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_asset_v1_asset_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserAssetAliasRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_asset_v1_asset_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListUserWorkSpaceReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_asset_v1_asset_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateAssetRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_asset_v1_asset_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateAseetRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_asset_v1_asset_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteAssetRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_asset_v1_asset_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAssetRouteRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_asset_v1_asset_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAssetRouteReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_asset_v1_asset_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_asset_v1_asset_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserAssetFavoriteRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_asset_v1_asset_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAssetListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_asset_v1_asset_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAssetListReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_asset_v1_asset_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAssetListByIpRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_asset_v1_asset_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAssetListByIpReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_asset_v1_asset_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAssetProxyGatewayListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_asset_v1_asset_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateAssetProxyGatewayRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_asset_v1_asset_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateAssetProxyGatewayRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_asset_v1_asset_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteAssetProxyGatewayRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_asset_v1_asset_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAssetProxyGatewayListReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_asset_v1_asset_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAssetAdminUserListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_asset_v1_asset_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateAssetAdminUserRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_asset_v1_asset_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateAssetAdminUserRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_asset_v1_asset_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteAssetAdminUserRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_asset_v1_asset_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAssetAdminUserListReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_asset_v1_asset_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAssetProxyGatewaySelectReply_AssetProxyGateway); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_asset_v1_asset_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListUserWorkSpaceReply_WorkSpace); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_asset_v1_asset_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAssetRouteReply_Children); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_asset_v1_asset_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAssetRouteReply_Route); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_asset_v1_asset_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAssetListReply_Asset); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_asset_v1_asset_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAssetListByIpReply_Asset); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_asset_v1_asset_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAssetProxyGatewayListReply_AssetProxyGateway); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_asset_v1_asset_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAssetAdminUserListReply_AssetAdminUser); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_asset_v1_asset_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   32,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_asset_v1_asset_proto_goTypes,
		DependencyIndexes: file_api_asset_v1_asset_proto_depIdxs,
		MessageInfos:      file_api_asset_v1_asset_proto_msgTypes,
	}.Build()
	File_api_asset_v1_asset_proto = out.File
	file_api_asset_v1_asset_proto_rawDesc = nil
	file_api_asset_v1_asset_proto_goTypes = nil
	file_api_asset_v1_asset_proto_depIdxs = nil
}
