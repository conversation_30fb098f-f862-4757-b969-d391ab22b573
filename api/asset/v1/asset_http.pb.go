// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.6.3
// - protoc             v4.23.4
// source: api/asset/v1/asset.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationAssetCreateAsset = "/asset.v1.Asset/CreateAsset"
const OperationAssetCreateAssetAdminUser = "/asset.v1.Asset/CreateAssetAdminUser"
const OperationAssetCreateAssetProxyGateway = "/asset.v1.Asset/CreateAssetProxyGateway"
const OperationAssetDeleteAsset = "/asset.v1.Asset/DeleteAsset"
const OperationAssetDeleteAssetAdminUser = "/asset.v1.Asset/DeleteAssetAdminUser"
const OperationAssetDeleteAssetProxyGateway = "/asset.v1.Asset/DeleteAssetProxyGateway"
const OperationAssetGetAssetAdminUserList = "/asset.v1.Asset/GetAssetAdminUserList"
const OperationAssetGetAssetList = "/asset.v1.Asset/GetAssetList"
const OperationAssetGetAssetListByIp = "/asset.v1.Asset/GetAssetListByIp"
const OperationAssetGetAssetProxyGatewayList = "/asset.v1.Asset/GetAssetProxyGatewayList"
const OperationAssetGetAssetProxyGatewaySelect = "/asset.v1.Asset/GetAssetProxyGatewaySelect"
const OperationAssetListAssetRoute = "/asset.v1.Asset/ListAssetRoute"
const OperationAssetListUserWorkSpace = "/asset.v1.Asset/ListUserWorkSpace"
const OperationAssetUpdateAsset = "/asset.v1.Asset/UpdateAsset"
const OperationAssetUpdateAssetAdminUser = "/asset.v1.Asset/UpdateAssetAdminUser"
const OperationAssetUpdateAssetProxyGateway = "/asset.v1.Asset/UpdateAssetProxyGateway"
const OperationAssetuserAssetAlias = "/asset.v1.Asset/userAssetAlias"
const OperationAssetUserAssetFavorite = "/asset.v1.Asset/UserAssetFavorite"

type AssetHTTPServer interface {
	CreateAsset(context.Context, *CreateAssetRequest) (*Reply, error)
	CreateAssetAdminUser(context.Context, *CreateAssetAdminUserRequest) (*Reply, error)
	CreateAssetProxyGateway(context.Context, *CreateAssetProxyGatewayRequest) (*Reply, error)
	DeleteAsset(context.Context, *DeleteAssetRequest) (*Reply, error)
	DeleteAssetAdminUser(context.Context, *DeleteAssetAdminUserRequest) (*Reply, error)
	DeleteAssetProxyGateway(context.Context, *DeleteAssetProxyGatewayRequest) (*Reply, error)
	GetAssetAdminUserList(context.Context, *GetAssetAdminUserListRequest) (*GetAssetAdminUserListReply, error)
	GetAssetList(context.Context, *GetAssetListRequest) (*GetAssetListReply, error)
	GetAssetListByIp(context.Context, *GetAssetListByIpRequest) (*GetAssetListByIpReply, error)
	GetAssetProxyGatewayList(context.Context, *GetAssetProxyGatewayListRequest) (*GetAssetProxyGatewayListReply, error)
	GetAssetProxyGatewaySelect(context.Context, *emptypb.Empty) (*GetAssetProxyGatewaySelectReply, error)
	ListAssetRoute(context.Context, *ListAssetRouteRequest) (*ListAssetRouteReply, error)
	ListUserWorkSpace(context.Context, *emptypb.Empty) (*ListUserWorkSpaceReply, error)
	UpdateAsset(context.Context, *UpdateAseetRequest) (*Reply, error)
	UpdateAssetAdminUser(context.Context, *UpdateAssetAdminUserRequest) (*Reply, error)
	UpdateAssetProxyGateway(context.Context, *UpdateAssetProxyGatewayRequest) (*Reply, error)
	UserAssetAlias(context.Context, *UserAssetAliasRequest) (*Reply, error)
	UserAssetFavorite(context.Context, *UserAssetFavoriteRequest) (*Reply, error)
}

func RegisterAssetHTTPServer(s *http.Server, srv AssetHTTPServer) {
	r := s.Route("/")
	r.GET("/v1/asset/assets", _Asset_GetAssetList0_HTTP_Handler(srv))
	r.POST("/v1/asset/assets", _Asset_CreateAsset0_HTTP_Handler(srv))
	r.PUT("/v1/asset/assets", _Asset_UpdateAsset0_HTTP_Handler(srv))
	r.DELETE("/v1/asset/assets/{uuid}", _Asset_DeleteAsset0_HTTP_Handler(srv))
	r.POST("/v1/asset/favorite", _Asset_UserAssetFavorite0_HTTP_Handler(srv))
	r.GET("/v1/asset/routes", _Asset_ListAssetRoute0_HTTP_Handler(srv))
	r.GET("/v1/asset/user-work-space", _Asset_ListUserWorkSpace0_HTTP_Handler(srv))
	r.POST("/v1/asset/alias", _Asset_UserAssetAlias0_HTTP_Handler(srv))
	r.GET("/v1/asset/ips", _Asset_GetAssetListByIp0_HTTP_Handler(srv))
	r.GET("/v1/asset/proxy-gateways/select", _Asset_GetAssetProxyGatewaySelect0_HTTP_Handler(srv))
	r.GET("/v1/asset/proxy-gateways", _Asset_GetAssetProxyGatewayList0_HTTP_Handler(srv))
	r.POST("/v1/asset/proxy-gateways", _Asset_CreateAssetProxyGateway0_HTTP_Handler(srv))
	r.PUT("/v1/asset/proxy-gateways", _Asset_UpdateAssetProxyGateway0_HTTP_Handler(srv))
	r.DELETE("/v1/asset/proxy-gateways/{id}", _Asset_DeleteAssetProxyGateway0_HTTP_Handler(srv))
	r.GET("/v1/asset/admin-users", _Asset_GetAssetAdminUserList0_HTTP_Handler(srv))
	r.POST("/v1/asset/admin-users", _Asset_CreateAssetAdminUser0_HTTP_Handler(srv))
	r.PUT("/v1/asset/admin-users", _Asset_UpdateAssetAdminUser0_HTTP_Handler(srv))
	r.DELETE("/v1/asset/admin-users/{id}", _Asset_DeleteAssetAdminUser0_HTTP_Handler(srv))
}

func _Asset_GetAssetList0_HTTP_Handler(srv AssetHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetAssetListRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAssetGetAssetList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetAssetList(ctx, req.(*GetAssetListRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetAssetListReply)
		return ctx.Result(200, reply)
	}
}

func _Asset_CreateAsset0_HTTP_Handler(srv AssetHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateAssetRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAssetCreateAsset)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateAsset(ctx, req.(*CreateAssetRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Reply)
		return ctx.Result(200, reply)
	}
}

func _Asset_UpdateAsset0_HTTP_Handler(srv AssetHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdateAseetRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAssetUpdateAsset)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateAsset(ctx, req.(*UpdateAseetRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Reply)
		return ctx.Result(200, reply)
	}
}

func _Asset_DeleteAsset0_HTTP_Handler(srv AssetHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteAssetRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAssetDeleteAsset)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteAsset(ctx, req.(*DeleteAssetRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Reply)
		return ctx.Result(200, reply)
	}
}

func _Asset_UserAssetFavorite0_HTTP_Handler(srv AssetHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UserAssetFavoriteRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAssetUserAssetFavorite)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UserAssetFavorite(ctx, req.(*UserAssetFavoriteRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Reply)
		return ctx.Result(200, reply)
	}
}

func _Asset_ListAssetRoute0_HTTP_Handler(srv AssetHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListAssetRouteRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAssetListAssetRoute)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListAssetRoute(ctx, req.(*ListAssetRouteRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListAssetRouteReply)
		return ctx.Result(200, reply)
	}
}

func _Asset_ListUserWorkSpace0_HTTP_Handler(srv AssetHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in emptypb.Empty
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAssetListUserWorkSpace)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListUserWorkSpace(ctx, req.(*emptypb.Empty))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListUserWorkSpaceReply)
		return ctx.Result(200, reply)
	}
}

func _Asset_UserAssetAlias0_HTTP_Handler(srv AssetHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UserAssetAliasRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAssetuserAssetAlias)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UserAssetAlias(ctx, req.(*UserAssetAliasRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Reply)
		return ctx.Result(200, reply)
	}
}

func _Asset_GetAssetListByIp0_HTTP_Handler(srv AssetHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetAssetListByIpRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAssetGetAssetListByIp)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetAssetListByIp(ctx, req.(*GetAssetListByIpRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetAssetListByIpReply)
		return ctx.Result(200, reply)
	}
}

func _Asset_GetAssetProxyGatewaySelect0_HTTP_Handler(srv AssetHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in emptypb.Empty
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAssetGetAssetProxyGatewaySelect)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetAssetProxyGatewaySelect(ctx, req.(*emptypb.Empty))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetAssetProxyGatewaySelectReply)
		return ctx.Result(200, reply)
	}
}

func _Asset_GetAssetProxyGatewayList0_HTTP_Handler(srv AssetHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetAssetProxyGatewayListRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAssetGetAssetProxyGatewayList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetAssetProxyGatewayList(ctx, req.(*GetAssetProxyGatewayListRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetAssetProxyGatewayListReply)
		return ctx.Result(200, reply)
	}
}

func _Asset_CreateAssetProxyGateway0_HTTP_Handler(srv AssetHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateAssetProxyGatewayRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAssetCreateAssetProxyGateway)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateAssetProxyGateway(ctx, req.(*CreateAssetProxyGatewayRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Reply)
		return ctx.Result(200, reply)
	}
}

func _Asset_UpdateAssetProxyGateway0_HTTP_Handler(srv AssetHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdateAssetProxyGatewayRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAssetUpdateAssetProxyGateway)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateAssetProxyGateway(ctx, req.(*UpdateAssetProxyGatewayRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Reply)
		return ctx.Result(200, reply)
	}
}

func _Asset_DeleteAssetProxyGateway0_HTTP_Handler(srv AssetHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteAssetProxyGatewayRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAssetDeleteAssetProxyGateway)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteAssetProxyGateway(ctx, req.(*DeleteAssetProxyGatewayRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Reply)
		return ctx.Result(200, reply)
	}
}

func _Asset_GetAssetAdminUserList0_HTTP_Handler(srv AssetHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetAssetAdminUserListRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAssetGetAssetAdminUserList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetAssetAdminUserList(ctx, req.(*GetAssetAdminUserListRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetAssetAdminUserListReply)
		return ctx.Result(200, reply)
	}
}

func _Asset_CreateAssetAdminUser0_HTTP_Handler(srv AssetHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateAssetAdminUserRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAssetCreateAssetAdminUser)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateAssetAdminUser(ctx, req.(*CreateAssetAdminUserRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Reply)
		return ctx.Result(200, reply)
	}
}

func _Asset_UpdateAssetAdminUser0_HTTP_Handler(srv AssetHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdateAssetAdminUserRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAssetUpdateAssetAdminUser)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateAssetAdminUser(ctx, req.(*UpdateAssetAdminUserRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Reply)
		return ctx.Result(200, reply)
	}
}

func _Asset_DeleteAssetAdminUser0_HTTP_Handler(srv AssetHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteAssetAdminUserRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAssetDeleteAssetAdminUser)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteAssetAdminUser(ctx, req.(*DeleteAssetAdminUserRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Reply)
		return ctx.Result(200, reply)
	}
}

type AssetHTTPClient interface {
	CreateAsset(ctx context.Context, req *CreateAssetRequest, opts ...http.CallOption) (rsp *Reply, err error)
	CreateAssetAdminUser(ctx context.Context, req *CreateAssetAdminUserRequest, opts ...http.CallOption) (rsp *Reply, err error)
	CreateAssetProxyGateway(ctx context.Context, req *CreateAssetProxyGatewayRequest, opts ...http.CallOption) (rsp *Reply, err error)
	DeleteAsset(ctx context.Context, req *DeleteAssetRequest, opts ...http.CallOption) (rsp *Reply, err error)
	DeleteAssetAdminUser(ctx context.Context, req *DeleteAssetAdminUserRequest, opts ...http.CallOption) (rsp *Reply, err error)
	DeleteAssetProxyGateway(ctx context.Context, req *DeleteAssetProxyGatewayRequest, opts ...http.CallOption) (rsp *Reply, err error)
	GetAssetAdminUserList(ctx context.Context, req *GetAssetAdminUserListRequest, opts ...http.CallOption) (rsp *GetAssetAdminUserListReply, err error)
	GetAssetList(ctx context.Context, req *GetAssetListRequest, opts ...http.CallOption) (rsp *GetAssetListReply, err error)
	GetAssetListByIp(ctx context.Context, req *GetAssetListByIpRequest, opts ...http.CallOption) (rsp *GetAssetListByIpReply, err error)
	GetAssetProxyGatewayList(ctx context.Context, req *GetAssetProxyGatewayListRequest, opts ...http.CallOption) (rsp *GetAssetProxyGatewayListReply, err error)
	GetAssetProxyGatewaySelect(ctx context.Context, req *emptypb.Empty, opts ...http.CallOption) (rsp *GetAssetProxyGatewaySelectReply, err error)
	ListAssetRoute(ctx context.Context, req *ListAssetRouteRequest, opts ...http.CallOption) (rsp *ListAssetRouteReply, err error)
	ListUserWorkSpace(ctx context.Context, req *emptypb.Empty, opts ...http.CallOption) (rsp *ListUserWorkSpaceReply, err error)
	UpdateAsset(ctx context.Context, req *UpdateAseetRequest, opts ...http.CallOption) (rsp *Reply, err error)
	UpdateAssetAdminUser(ctx context.Context, req *UpdateAssetAdminUserRequest, opts ...http.CallOption) (rsp *Reply, err error)
	UpdateAssetProxyGateway(ctx context.Context, req *UpdateAssetProxyGatewayRequest, opts ...http.CallOption) (rsp *Reply, err error)
	UserAssetAlias(ctx context.Context, req *UserAssetAliasRequest, opts ...http.CallOption) (rsp *Reply, err error)
	UserAssetFavorite(ctx context.Context, req *UserAssetFavoriteRequest, opts ...http.CallOption) (rsp *Reply, err error)
}

type AssetHTTPClientImpl struct {
	cc *http.Client
}

func NewAssetHTTPClient(client *http.Client) AssetHTTPClient {
	return &AssetHTTPClientImpl{client}
}

func (c *AssetHTTPClientImpl) CreateAsset(ctx context.Context, in *CreateAssetRequest, opts ...http.CallOption) (*Reply, error) {
	var out Reply
	pattern := "/v1/asset/assets"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAssetCreateAsset))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *AssetHTTPClientImpl) CreateAssetAdminUser(ctx context.Context, in *CreateAssetAdminUserRequest, opts ...http.CallOption) (*Reply, error) {
	var out Reply
	pattern := "/v1/asset/admin-users"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAssetCreateAssetAdminUser))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *AssetHTTPClientImpl) CreateAssetProxyGateway(ctx context.Context, in *CreateAssetProxyGatewayRequest, opts ...http.CallOption) (*Reply, error) {
	var out Reply
	pattern := "/v1/asset/proxy-gateways"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAssetCreateAssetProxyGateway))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *AssetHTTPClientImpl) DeleteAsset(ctx context.Context, in *DeleteAssetRequest, opts ...http.CallOption) (*Reply, error) {
	var out Reply
	pattern := "/v1/asset/assets/{uuid}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAssetDeleteAsset))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *AssetHTTPClientImpl) DeleteAssetAdminUser(ctx context.Context, in *DeleteAssetAdminUserRequest, opts ...http.CallOption) (*Reply, error) {
	var out Reply
	pattern := "/v1/asset/admin-users/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAssetDeleteAssetAdminUser))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *AssetHTTPClientImpl) DeleteAssetProxyGateway(ctx context.Context, in *DeleteAssetProxyGatewayRequest, opts ...http.CallOption) (*Reply, error) {
	var out Reply
	pattern := "/v1/asset/proxy-gateways/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAssetDeleteAssetProxyGateway))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *AssetHTTPClientImpl) GetAssetAdminUserList(ctx context.Context, in *GetAssetAdminUserListRequest, opts ...http.CallOption) (*GetAssetAdminUserListReply, error) {
	var out GetAssetAdminUserListReply
	pattern := "/v1/asset/admin-users"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAssetGetAssetAdminUserList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *AssetHTTPClientImpl) GetAssetList(ctx context.Context, in *GetAssetListRequest, opts ...http.CallOption) (*GetAssetListReply, error) {
	var out GetAssetListReply
	pattern := "/v1/asset/assets"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAssetGetAssetList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *AssetHTTPClientImpl) GetAssetListByIp(ctx context.Context, in *GetAssetListByIpRequest, opts ...http.CallOption) (*GetAssetListByIpReply, error) {
	var out GetAssetListByIpReply
	pattern := "/v1/asset/ips"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAssetGetAssetListByIp))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *AssetHTTPClientImpl) GetAssetProxyGatewayList(ctx context.Context, in *GetAssetProxyGatewayListRequest, opts ...http.CallOption) (*GetAssetProxyGatewayListReply, error) {
	var out GetAssetProxyGatewayListReply
	pattern := "/v1/asset/proxy-gateways"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAssetGetAssetProxyGatewayList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *AssetHTTPClientImpl) GetAssetProxyGatewaySelect(ctx context.Context, in *emptypb.Empty, opts ...http.CallOption) (*GetAssetProxyGatewaySelectReply, error) {
	var out GetAssetProxyGatewaySelectReply
	pattern := "/v1/asset/proxy-gateways/select"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAssetGetAssetProxyGatewaySelect))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *AssetHTTPClientImpl) ListAssetRoute(ctx context.Context, in *ListAssetRouteRequest, opts ...http.CallOption) (*ListAssetRouteReply, error) {
	var out ListAssetRouteReply
	pattern := "/v1/asset/routes"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAssetListAssetRoute))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *AssetHTTPClientImpl) ListUserWorkSpace(ctx context.Context, in *emptypb.Empty, opts ...http.CallOption) (*ListUserWorkSpaceReply, error) {
	var out ListUserWorkSpaceReply
	pattern := "/v1/asset/user-work-space"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAssetListUserWorkSpace))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *AssetHTTPClientImpl) UpdateAsset(ctx context.Context, in *UpdateAseetRequest, opts ...http.CallOption) (*Reply, error) {
	var out Reply
	pattern := "/v1/asset/assets"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAssetUpdateAsset))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *AssetHTTPClientImpl) UpdateAssetAdminUser(ctx context.Context, in *UpdateAssetAdminUserRequest, opts ...http.CallOption) (*Reply, error) {
	var out Reply
	pattern := "/v1/asset/admin-users"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAssetUpdateAssetAdminUser))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *AssetHTTPClientImpl) UpdateAssetProxyGateway(ctx context.Context, in *UpdateAssetProxyGatewayRequest, opts ...http.CallOption) (*Reply, error) {
	var out Reply
	pattern := "/v1/asset/proxy-gateways"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAssetUpdateAssetProxyGateway))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *AssetHTTPClientImpl) UserAssetAlias(ctx context.Context, in *UserAssetAliasRequest, opts ...http.CallOption) (*Reply, error) {
	var out Reply
	pattern := "/v1/asset/alias"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAssetuserAssetAlias))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *AssetHTTPClientImpl) UserAssetFavorite(ctx context.Context, in *UserAssetFavoriteRequest, opts ...http.CallOption) (*Reply, error) {
	var out Reply
	pattern := "/v1/asset/favorite"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAssetUserAssetFavorite))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}
