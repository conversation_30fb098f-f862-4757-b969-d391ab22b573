// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/asset/v1/asset.proto

package v1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on GetAssetProxyGatewaySelectReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAssetProxyGatewaySelectReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAssetProxyGatewaySelectReply with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetAssetProxyGatewaySelectReplyMultiError, or nil if none found.
func (m *GetAssetProxyGatewaySelectReply) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAssetProxyGatewaySelectReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetData() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetAssetProxyGatewaySelectReplyValidationError{
						field:  fmt.Sprintf("Data[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetAssetProxyGatewaySelectReplyValidationError{
						field:  fmt.Sprintf("Data[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetAssetProxyGatewaySelectReplyValidationError{
					field:  fmt.Sprintf("Data[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetAssetProxyGatewaySelectReplyMultiError(errors)
	}

	return nil
}

// GetAssetProxyGatewaySelectReplyMultiError is an error wrapping multiple
// validation errors returned by GetAssetProxyGatewaySelectReply.ValidateAll()
// if the designated constraints aren't met.
type GetAssetProxyGatewaySelectReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAssetProxyGatewaySelectReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAssetProxyGatewaySelectReplyMultiError) AllErrors() []error { return m }

// GetAssetProxyGatewaySelectReplyValidationError is the validation error
// returned by GetAssetProxyGatewaySelectReply.Validate if the designated
// constraints aren't met.
type GetAssetProxyGatewaySelectReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAssetProxyGatewaySelectReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAssetProxyGatewaySelectReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAssetProxyGatewaySelectReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAssetProxyGatewaySelectReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAssetProxyGatewaySelectReplyValidationError) ErrorName() string {
	return "GetAssetProxyGatewaySelectReplyValidationError"
}

// Error satisfies the builtin error interface
func (e GetAssetProxyGatewaySelectReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAssetProxyGatewaySelectReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAssetProxyGatewaySelectReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAssetProxyGatewaySelectReplyValidationError{}

// Validate checks the field values on UserAssetAliasRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UserAssetAliasRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UserAssetAliasRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UserAssetAliasRequestMultiError, or nil if none found.
func (m *UserAssetAliasRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UserAssetAliasRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetKey()) < 1 {
		err := UserAssetAliasRequestValidationError{
			field:  "Key",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetAlias()) < 1 {
		err := UserAssetAliasRequestValidationError{
			field:  "Alias",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return UserAssetAliasRequestMultiError(errors)
	}

	return nil
}

// UserAssetAliasRequestMultiError is an error wrapping multiple validation
// errors returned by UserAssetAliasRequest.ValidateAll() if the designated
// constraints aren't met.
type UserAssetAliasRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UserAssetAliasRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UserAssetAliasRequestMultiError) AllErrors() []error { return m }

// UserAssetAliasRequestValidationError is the validation error returned by
// UserAssetAliasRequest.Validate if the designated constraints aren't met.
type UserAssetAliasRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UserAssetAliasRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UserAssetAliasRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UserAssetAliasRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UserAssetAliasRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UserAssetAliasRequestValidationError) ErrorName() string {
	return "UserAssetAliasRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UserAssetAliasRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUserAssetAliasRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UserAssetAliasRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UserAssetAliasRequestValidationError{}

// Validate checks the field values on ListUserWorkSpaceReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListUserWorkSpaceReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListUserWorkSpaceReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListUserWorkSpaceReplyMultiError, or nil if none found.
func (m *ListUserWorkSpaceReply) ValidateAll() error {
	return m.validate(true)
}

func (m *ListUserWorkSpaceReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetData() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListUserWorkSpaceReplyValidationError{
						field:  fmt.Sprintf("Data[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListUserWorkSpaceReplyValidationError{
						field:  fmt.Sprintf("Data[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListUserWorkSpaceReplyValidationError{
					field:  fmt.Sprintf("Data[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListUserWorkSpaceReplyMultiError(errors)
	}

	return nil
}

// ListUserWorkSpaceReplyMultiError is an error wrapping multiple validation
// errors returned by ListUserWorkSpaceReply.ValidateAll() if the designated
// constraints aren't met.
type ListUserWorkSpaceReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListUserWorkSpaceReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListUserWorkSpaceReplyMultiError) AllErrors() []error { return m }

// ListUserWorkSpaceReplyValidationError is the validation error returned by
// ListUserWorkSpaceReply.Validate if the designated constraints aren't met.
type ListUserWorkSpaceReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListUserWorkSpaceReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListUserWorkSpaceReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListUserWorkSpaceReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListUserWorkSpaceReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListUserWorkSpaceReplyValidationError) ErrorName() string {
	return "ListUserWorkSpaceReplyValidationError"
}

// Error satisfies the builtin error interface
func (e ListUserWorkSpaceReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListUserWorkSpaceReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListUserWorkSpaceReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListUserWorkSpaceReplyValidationError{}

// Validate checks the field values on CreateAssetRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateAssetRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateAssetRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateAssetRequestMultiError, or nil if none found.
func (m *CreateAssetRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateAssetRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetName()) < 1 {
		err := CreateAssetRequestValidationError{
			field:  "Name",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetIdc()) < 1 {
		err := CreateAssetRequestValidationError{
			field:  "Idc",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetAssetIp()) < 1 {
		err := CreateAssetRequestValidationError{
			field:  "AssetIp",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetActive()) < 1 {
		err := CreateAssetRequestValidationError{
			field:  "Active",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetPlatform()) < 1 {
		err := CreateAssetRequestValidationError{
			field:  "Platform",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetProxyGatewayId() <= 0 {
		err := CreateAssetRequestValidationError{
			field:  "ProxyGatewayId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetProtocol()) < 1 {
		err := CreateAssetRequestValidationError{
			field:  "Protocol",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetAssetSource()) < 1 {
		err := CreateAssetRequestValidationError{
			field:  "AssetSource",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetOs()) < 1 {
		err := CreateAssetRequestValidationError{
			field:  "Os",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetOrganizationId()) < 1 {
		err := CreateAssetRequestValidationError{
			field:  "OrganizationId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Comment

	if len(errors) > 0 {
		return CreateAssetRequestMultiError(errors)
	}

	return nil
}

// CreateAssetRequestMultiError is an error wrapping multiple validation errors
// returned by CreateAssetRequest.ValidateAll() if the designated constraints
// aren't met.
type CreateAssetRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateAssetRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateAssetRequestMultiError) AllErrors() []error { return m }

// CreateAssetRequestValidationError is the validation error returned by
// CreateAssetRequest.Validate if the designated constraints aren't met.
type CreateAssetRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateAssetRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateAssetRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateAssetRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateAssetRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateAssetRequestValidationError) ErrorName() string {
	return "CreateAssetRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateAssetRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateAssetRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateAssetRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateAssetRequestValidationError{}

// Validate checks the field values on UpdateAseetRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateAseetRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateAseetRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateAseetRequestMultiError, or nil if none found.
func (m *UpdateAseetRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateAseetRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetUuid()) < 1 {
		err := UpdateAseetRequestValidationError{
			field:  "Uuid",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetName()) < 1 {
		err := UpdateAseetRequestValidationError{
			field:  "Name",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetAssetIp()) < 1 {
		err := UpdateAseetRequestValidationError{
			field:  "AssetIp",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetActive()) < 1 {
		err := UpdateAseetRequestValidationError{
			field:  "Active",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetPlatform()) < 1 {
		err := UpdateAseetRequestValidationError{
			field:  "Platform",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetProxyGatewayId() <= 0 {
		err := UpdateAseetRequestValidationError{
			field:  "ProxyGatewayId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetProtocol()) < 1 {
		err := UpdateAseetRequestValidationError{
			field:  "Protocol",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetOs()) < 1 {
		err := UpdateAseetRequestValidationError{
			field:  "Os",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetOrganizationId()) < 1 {
		err := UpdateAseetRequestValidationError{
			field:  "OrganizationId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetComment()) < 1 {
		err := UpdateAseetRequestValidationError{
			field:  "Comment",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetIdc()) < 1 {
		err := UpdateAseetRequestValidationError{
			field:  "Idc",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return UpdateAseetRequestMultiError(errors)
	}

	return nil
}

// UpdateAseetRequestMultiError is an error wrapping multiple validation errors
// returned by UpdateAseetRequest.ValidateAll() if the designated constraints
// aren't met.
type UpdateAseetRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateAseetRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateAseetRequestMultiError) AllErrors() []error { return m }

// UpdateAseetRequestValidationError is the validation error returned by
// UpdateAseetRequest.Validate if the designated constraints aren't met.
type UpdateAseetRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateAseetRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateAseetRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateAseetRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateAseetRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateAseetRequestValidationError) ErrorName() string {
	return "UpdateAseetRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateAseetRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateAseetRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateAseetRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateAseetRequestValidationError{}

// Validate checks the field values on DeleteAssetRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteAssetRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteAssetRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteAssetRequestMultiError, or nil if none found.
func (m *DeleteAssetRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteAssetRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Uuid

	if len(errors) > 0 {
		return DeleteAssetRequestMultiError(errors)
	}

	return nil
}

// DeleteAssetRequestMultiError is an error wrapping multiple validation errors
// returned by DeleteAssetRequest.ValidateAll() if the designated constraints
// aren't met.
type DeleteAssetRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteAssetRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteAssetRequestMultiError) AllErrors() []error { return m }

// DeleteAssetRequestValidationError is the validation error returned by
// DeleteAssetRequest.Validate if the designated constraints aren't met.
type DeleteAssetRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteAssetRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteAssetRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteAssetRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteAssetRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteAssetRequestValidationError) ErrorName() string {
	return "DeleteAssetRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteAssetRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteAssetRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteAssetRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteAssetRequestValidationError{}

// Validate checks the field values on ListAssetRouteRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListAssetRouteRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListAssetRouteRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListAssetRouteRequestMultiError, or nil if none found.
func (m *ListAssetRouteRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListAssetRouteRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for OrganizationId

	if len(errors) > 0 {
		return ListAssetRouteRequestMultiError(errors)
	}

	return nil
}

// ListAssetRouteRequestMultiError is an error wrapping multiple validation
// errors returned by ListAssetRouteRequest.ValidateAll() if the designated
// constraints aren't met.
type ListAssetRouteRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListAssetRouteRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListAssetRouteRequestMultiError) AllErrors() []error { return m }

// ListAssetRouteRequestValidationError is the validation error returned by
// ListAssetRouteRequest.Validate if the designated constraints aren't met.
type ListAssetRouteRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListAssetRouteRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListAssetRouteRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListAssetRouteRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListAssetRouteRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListAssetRouteRequestValidationError) ErrorName() string {
	return "ListAssetRouteRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListAssetRouteRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListAssetRouteRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListAssetRouteRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListAssetRouteRequestValidationError{}

// Validate checks the field values on ListAssetRouteReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListAssetRouteReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListAssetRouteReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListAssetRouteReplyMultiError, or nil if none found.
func (m *ListAssetRouteReply) ValidateAll() error {
	return m.validate(true)
}

func (m *ListAssetRouteReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetRouters() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListAssetRouteReplyValidationError{
						field:  fmt.Sprintf("Routers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListAssetRouteReplyValidationError{
						field:  fmt.Sprintf("Routers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListAssetRouteReplyValidationError{
					field:  fmt.Sprintf("Routers[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListAssetRouteReplyMultiError(errors)
	}

	return nil
}

// ListAssetRouteReplyMultiError is an error wrapping multiple validation
// errors returned by ListAssetRouteReply.ValidateAll() if the designated
// constraints aren't met.
type ListAssetRouteReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListAssetRouteReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListAssetRouteReplyMultiError) AllErrors() []error { return m }

// ListAssetRouteReplyValidationError is the validation error returned by
// ListAssetRouteReply.Validate if the designated constraints aren't met.
type ListAssetRouteReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListAssetRouteReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListAssetRouteReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListAssetRouteReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListAssetRouteReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListAssetRouteReplyValidationError) ErrorName() string {
	return "ListAssetRouteReplyValidationError"
}

// Error satisfies the builtin error interface
func (e ListAssetRouteReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListAssetRouteReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListAssetRouteReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListAssetRouteReplyValidationError{}

// Validate checks the field values on Reply with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Reply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Reply with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in ReplyMultiError, or nil if none found.
func (m *Reply) ValidateAll() error {
	return m.validate(true)
}

func (m *Reply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Message

	if len(errors) > 0 {
		return ReplyMultiError(errors)
	}

	return nil
}

// ReplyMultiError is an error wrapping multiple validation errors returned by
// Reply.ValidateAll() if the designated constraints aren't met.
type ReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReplyMultiError) AllErrors() []error { return m }

// ReplyValidationError is the validation error returned by Reply.Validate if
// the designated constraints aren't met.
type ReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReplyValidationError) ErrorName() string { return "ReplyValidationError" }

// Error satisfies the builtin error interface
func (e ReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReplyValidationError{}

// Validate checks the field values on UserAssetFavoriteRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UserAssetFavoriteRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UserAssetFavoriteRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UserAssetFavoriteRequestMultiError, or nil if none found.
func (m *UserAssetFavoriteRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UserAssetFavoriteRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetAssetIp()) < 1 {
		err := UserAssetFavoriteRequestValidationError{
			field:  "AssetIp",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetAction()) < 1 {
		err := UserAssetFavoriteRequestValidationError{
			field:  "Action",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return UserAssetFavoriteRequestMultiError(errors)
	}

	return nil
}

// UserAssetFavoriteRequestMultiError is an error wrapping multiple validation
// errors returned by UserAssetFavoriteRequest.ValidateAll() if the designated
// constraints aren't met.
type UserAssetFavoriteRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UserAssetFavoriteRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UserAssetFavoriteRequestMultiError) AllErrors() []error { return m }

// UserAssetFavoriteRequestValidationError is the validation error returned by
// UserAssetFavoriteRequest.Validate if the designated constraints aren't met.
type UserAssetFavoriteRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UserAssetFavoriteRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UserAssetFavoriteRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UserAssetFavoriteRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UserAssetFavoriteRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UserAssetFavoriteRequestValidationError) ErrorName() string {
	return "UserAssetFavoriteRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UserAssetFavoriteRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUserAssetFavoriteRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UserAssetFavoriteRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UserAssetFavoriteRequestValidationError{}

// Validate checks the field values on GetAssetListRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAssetListRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAssetListRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAssetListRequestMultiError, or nil if none found.
func (m *GetAssetListRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAssetListRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetPageSize() <= 0 {
		err := GetAssetListRequestValidationError{
			field:  "PageSize",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetPageNo() <= -1 {
		err := GetAssetListRequestValidationError{
			field:  "PageNo",
			reason: "value must be greater than -1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for SearchText

	// no validation rules for Idc

	// no validation rules for OrganizationId

	// no validation rules for ProxyGatewayId

	if len(errors) > 0 {
		return GetAssetListRequestMultiError(errors)
	}

	return nil
}

// GetAssetListRequestMultiError is an error wrapping multiple validation
// errors returned by GetAssetListRequest.ValidateAll() if the designated
// constraints aren't met.
type GetAssetListRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAssetListRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAssetListRequestMultiError) AllErrors() []error { return m }

// GetAssetListRequestValidationError is the validation error returned by
// GetAssetListRequest.Validate if the designated constraints aren't met.
type GetAssetListRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAssetListRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAssetListRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAssetListRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAssetListRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAssetListRequestValidationError) ErrorName() string {
	return "GetAssetListRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetAssetListRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAssetListRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAssetListRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAssetListRequestValidationError{}

// Validate checks the field values on GetAssetListReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetAssetListReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAssetListReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAssetListReplyMultiError, or nil if none found.
func (m *GetAssetListReply) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAssetListReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetData() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetAssetListReplyValidationError{
						field:  fmt.Sprintf("Data[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetAssetListReplyValidationError{
						field:  fmt.Sprintf("Data[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetAssetListReplyValidationError{
					field:  fmt.Sprintf("Data[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for PageNo

	// no validation rules for PageSize

	// no validation rules for TotalCount

	// no validation rules for TotalPage

	if len(errors) > 0 {
		return GetAssetListReplyMultiError(errors)
	}

	return nil
}

// GetAssetListReplyMultiError is an error wrapping multiple validation errors
// returned by GetAssetListReply.ValidateAll() if the designated constraints
// aren't met.
type GetAssetListReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAssetListReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAssetListReplyMultiError) AllErrors() []error { return m }

// GetAssetListReplyValidationError is the validation error returned by
// GetAssetListReply.Validate if the designated constraints aren't met.
type GetAssetListReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAssetListReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAssetListReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAssetListReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAssetListReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAssetListReplyValidationError) ErrorName() string {
	return "GetAssetListReplyValidationError"
}

// Error satisfies the builtin error interface
func (e GetAssetListReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAssetListReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAssetListReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAssetListReplyValidationError{}

// Validate checks the field values on GetAssetListByIpRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAssetListByIpRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAssetListByIpRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAssetListByIpRequestMultiError, or nil if none found.
func (m *GetAssetListByIpRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAssetListByIpRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetPageSize() <= 0 {
		err := GetAssetListByIpRequestValidationError{
			field:  "PageSize",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetPageNo() <= -1 {
		err := GetAssetListByIpRequestValidationError{
			field:  "PageNo",
			reason: "value must be greater than -1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Ip

	// no validation rules for OrganizationId

	if len(errors) > 0 {
		return GetAssetListByIpRequestMultiError(errors)
	}

	return nil
}

// GetAssetListByIpRequestMultiError is an error wrapping multiple validation
// errors returned by GetAssetListByIpRequest.ValidateAll() if the designated
// constraints aren't met.
type GetAssetListByIpRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAssetListByIpRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAssetListByIpRequestMultiError) AllErrors() []error { return m }

// GetAssetListByIpRequestValidationError is the validation error returned by
// GetAssetListByIpRequest.Validate if the designated constraints aren't met.
type GetAssetListByIpRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAssetListByIpRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAssetListByIpRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAssetListByIpRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAssetListByIpRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAssetListByIpRequestValidationError) ErrorName() string {
	return "GetAssetListByIpRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetAssetListByIpRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAssetListByIpRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAssetListByIpRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAssetListByIpRequestValidationError{}

// Validate checks the field values on GetAssetListByIpReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAssetListByIpReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAssetListByIpReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAssetListByIpReplyMultiError, or nil if none found.
func (m *GetAssetListByIpReply) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAssetListByIpReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetData() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetAssetListByIpReplyValidationError{
						field:  fmt.Sprintf("Data[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetAssetListByIpReplyValidationError{
						field:  fmt.Sprintf("Data[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetAssetListByIpReplyValidationError{
					field:  fmt.Sprintf("Data[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for PageNo

	// no validation rules for PageSize

	// no validation rules for TotalCount

	// no validation rules for TotalPage

	if len(errors) > 0 {
		return GetAssetListByIpReplyMultiError(errors)
	}

	return nil
}

// GetAssetListByIpReplyMultiError is an error wrapping multiple validation
// errors returned by GetAssetListByIpReply.ValidateAll() if the designated
// constraints aren't met.
type GetAssetListByIpReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAssetListByIpReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAssetListByIpReplyMultiError) AllErrors() []error { return m }

// GetAssetListByIpReplyValidationError is the validation error returned by
// GetAssetListByIpReply.Validate if the designated constraints aren't met.
type GetAssetListByIpReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAssetListByIpReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAssetListByIpReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAssetListByIpReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAssetListByIpReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAssetListByIpReplyValidationError) ErrorName() string {
	return "GetAssetListByIpReplyValidationError"
}

// Error satisfies the builtin error interface
func (e GetAssetListByIpReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAssetListByIpReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAssetListByIpReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAssetListByIpReplyValidationError{}

// Validate checks the field values on GetAssetProxyGatewayListRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAssetProxyGatewayListRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAssetProxyGatewayListRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetAssetProxyGatewayListRequestMultiError, or nil if none found.
func (m *GetAssetProxyGatewayListRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAssetProxyGatewayListRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetPageSize() <= 0 {
		err := GetAssetProxyGatewayListRequestValidationError{
			field:  "PageSize",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetPageNo() <= -1 {
		err := GetAssetProxyGatewayListRequestValidationError{
			field:  "PageNo",
			reason: "value must be greater than -1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for SearchText

	if len(errors) > 0 {
		return GetAssetProxyGatewayListRequestMultiError(errors)
	}

	return nil
}

// GetAssetProxyGatewayListRequestMultiError is an error wrapping multiple
// validation errors returned by GetAssetProxyGatewayListRequest.ValidateAll()
// if the designated constraints aren't met.
type GetAssetProxyGatewayListRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAssetProxyGatewayListRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAssetProxyGatewayListRequestMultiError) AllErrors() []error { return m }

// GetAssetProxyGatewayListRequestValidationError is the validation error
// returned by GetAssetProxyGatewayListRequest.Validate if the designated
// constraints aren't met.
type GetAssetProxyGatewayListRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAssetProxyGatewayListRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAssetProxyGatewayListRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAssetProxyGatewayListRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAssetProxyGatewayListRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAssetProxyGatewayListRequestValidationError) ErrorName() string {
	return "GetAssetProxyGatewayListRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetAssetProxyGatewayListRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAssetProxyGatewayListRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAssetProxyGatewayListRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAssetProxyGatewayListRequestValidationError{}

// Validate checks the field values on CreateAssetProxyGatewayRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateAssetProxyGatewayRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateAssetProxyGatewayRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CreateAssetProxyGatewayRequestMultiError, or nil if none found.
func (m *CreateAssetProxyGatewayRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateAssetProxyGatewayRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetName()) < 1 {
		err := CreateAssetProxyGatewayRequestValidationError{
			field:  "Name",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetOrganizationId()) < 1 {
		err := CreateAssetProxyGatewayRequestValidationError{
			field:  "OrganizationId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetIp()) < 1 {
		err := CreateAssetProxyGatewayRequestValidationError{
			field:  "Ip",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetPort() <= 0 {
		err := CreateAssetProxyGatewayRequestValidationError{
			field:  "Port",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetUsername()) < 1 {
		err := CreateAssetProxyGatewayRequestValidationError{
			field:  "Username",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Password

	// no validation rules for PrivateKey

	// no validation rules for Comment

	if utf8.RuneCountInString(m.GetIdc()) < 1 {
		err := CreateAssetProxyGatewayRequestValidationError{
			field:  "Idc",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return CreateAssetProxyGatewayRequestMultiError(errors)
	}

	return nil
}

// CreateAssetProxyGatewayRequestMultiError is an error wrapping multiple
// validation errors returned by CreateAssetProxyGatewayRequest.ValidateAll()
// if the designated constraints aren't met.
type CreateAssetProxyGatewayRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateAssetProxyGatewayRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateAssetProxyGatewayRequestMultiError) AllErrors() []error { return m }

// CreateAssetProxyGatewayRequestValidationError is the validation error
// returned by CreateAssetProxyGatewayRequest.Validate if the designated
// constraints aren't met.
type CreateAssetProxyGatewayRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateAssetProxyGatewayRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateAssetProxyGatewayRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateAssetProxyGatewayRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateAssetProxyGatewayRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateAssetProxyGatewayRequestValidationError) ErrorName() string {
	return "CreateAssetProxyGatewayRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateAssetProxyGatewayRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateAssetProxyGatewayRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateAssetProxyGatewayRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateAssetProxyGatewayRequestValidationError{}

// Validate checks the field values on UpdateAssetProxyGatewayRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateAssetProxyGatewayRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateAssetProxyGatewayRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// UpdateAssetProxyGatewayRequestMultiError, or nil if none found.
func (m *UpdateAssetProxyGatewayRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateAssetProxyGatewayRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if utf8.RuneCountInString(m.GetName()) < 1 {
		err := UpdateAssetProxyGatewayRequestValidationError{
			field:  "Name",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetOrganizationId()) < 1 {
		err := UpdateAssetProxyGatewayRequestValidationError{
			field:  "OrganizationId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetIp()) < 1 {
		err := UpdateAssetProxyGatewayRequestValidationError{
			field:  "Ip",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetPort() <= 0 {
		err := UpdateAssetProxyGatewayRequestValidationError{
			field:  "Port",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetUsername()) < 1 {
		err := UpdateAssetProxyGatewayRequestValidationError{
			field:  "Username",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Password

	// no validation rules for PrivateKey

	// no validation rules for Comment

	if utf8.RuneCountInString(m.GetIdc()) < 1 {
		err := UpdateAssetProxyGatewayRequestValidationError{
			field:  "Idc",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return UpdateAssetProxyGatewayRequestMultiError(errors)
	}

	return nil
}

// UpdateAssetProxyGatewayRequestMultiError is an error wrapping multiple
// validation errors returned by UpdateAssetProxyGatewayRequest.ValidateAll()
// if the designated constraints aren't met.
type UpdateAssetProxyGatewayRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateAssetProxyGatewayRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateAssetProxyGatewayRequestMultiError) AllErrors() []error { return m }

// UpdateAssetProxyGatewayRequestValidationError is the validation error
// returned by UpdateAssetProxyGatewayRequest.Validate if the designated
// constraints aren't met.
type UpdateAssetProxyGatewayRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateAssetProxyGatewayRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateAssetProxyGatewayRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateAssetProxyGatewayRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateAssetProxyGatewayRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateAssetProxyGatewayRequestValidationError) ErrorName() string {
	return "UpdateAssetProxyGatewayRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateAssetProxyGatewayRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateAssetProxyGatewayRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateAssetProxyGatewayRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateAssetProxyGatewayRequestValidationError{}

// Validate checks the field values on DeleteAssetProxyGatewayRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteAssetProxyGatewayRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteAssetProxyGatewayRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// DeleteAssetProxyGatewayRequestMultiError, or nil if none found.
func (m *DeleteAssetProxyGatewayRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteAssetProxyGatewayRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return DeleteAssetProxyGatewayRequestMultiError(errors)
	}

	return nil
}

// DeleteAssetProxyGatewayRequestMultiError is an error wrapping multiple
// validation errors returned by DeleteAssetProxyGatewayRequest.ValidateAll()
// if the designated constraints aren't met.
type DeleteAssetProxyGatewayRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteAssetProxyGatewayRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteAssetProxyGatewayRequestMultiError) AllErrors() []error { return m }

// DeleteAssetProxyGatewayRequestValidationError is the validation error
// returned by DeleteAssetProxyGatewayRequest.Validate if the designated
// constraints aren't met.
type DeleteAssetProxyGatewayRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteAssetProxyGatewayRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteAssetProxyGatewayRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteAssetProxyGatewayRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteAssetProxyGatewayRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteAssetProxyGatewayRequestValidationError) ErrorName() string {
	return "DeleteAssetProxyGatewayRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteAssetProxyGatewayRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteAssetProxyGatewayRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteAssetProxyGatewayRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteAssetProxyGatewayRequestValidationError{}

// Validate checks the field values on GetAssetProxyGatewayListReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAssetProxyGatewayListReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAssetProxyGatewayListReply with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetAssetProxyGatewayListReplyMultiError, or nil if none found.
func (m *GetAssetProxyGatewayListReply) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAssetProxyGatewayListReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetData() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetAssetProxyGatewayListReplyValidationError{
						field:  fmt.Sprintf("Data[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetAssetProxyGatewayListReplyValidationError{
						field:  fmt.Sprintf("Data[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetAssetProxyGatewayListReplyValidationError{
					field:  fmt.Sprintf("Data[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for PageNo

	// no validation rules for PageSize

	// no validation rules for TotalCount

	// no validation rules for TotalPage

	if len(errors) > 0 {
		return GetAssetProxyGatewayListReplyMultiError(errors)
	}

	return nil
}

// GetAssetProxyGatewayListReplyMultiError is an error wrapping multiple
// validation errors returned by GetAssetProxyGatewayListReply.ValidateAll()
// if the designated constraints aren't met.
type GetAssetProxyGatewayListReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAssetProxyGatewayListReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAssetProxyGatewayListReplyMultiError) AllErrors() []error { return m }

// GetAssetProxyGatewayListReplyValidationError is the validation error
// returned by GetAssetProxyGatewayListReply.Validate if the designated
// constraints aren't met.
type GetAssetProxyGatewayListReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAssetProxyGatewayListReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAssetProxyGatewayListReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAssetProxyGatewayListReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAssetProxyGatewayListReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAssetProxyGatewayListReplyValidationError) ErrorName() string {
	return "GetAssetProxyGatewayListReplyValidationError"
}

// Error satisfies the builtin error interface
func (e GetAssetProxyGatewayListReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAssetProxyGatewayListReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAssetProxyGatewayListReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAssetProxyGatewayListReplyValidationError{}

// Validate checks the field values on GetAssetAdminUserListRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAssetAdminUserListRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAssetAdminUserListRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAssetAdminUserListRequestMultiError, or nil if none found.
func (m *GetAssetAdminUserListRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAssetAdminUserListRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetPageSize() <= 0 {
		err := GetAssetAdminUserListRequestValidationError{
			field:  "PageSize",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetPageNo() <= -1 {
		err := GetAssetAdminUserListRequestValidationError{
			field:  "PageNo",
			reason: "value must be greater than -1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for SearchText

	if len(errors) > 0 {
		return GetAssetAdminUserListRequestMultiError(errors)
	}

	return nil
}

// GetAssetAdminUserListRequestMultiError is an error wrapping multiple
// validation errors returned by GetAssetAdminUserListRequest.ValidateAll() if
// the designated constraints aren't met.
type GetAssetAdminUserListRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAssetAdminUserListRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAssetAdminUserListRequestMultiError) AllErrors() []error { return m }

// GetAssetAdminUserListRequestValidationError is the validation error returned
// by GetAssetAdminUserListRequest.Validate if the designated constraints
// aren't met.
type GetAssetAdminUserListRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAssetAdminUserListRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAssetAdminUserListRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAssetAdminUserListRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAssetAdminUserListRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAssetAdminUserListRequestValidationError) ErrorName() string {
	return "GetAssetAdminUserListRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetAssetAdminUserListRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAssetAdminUserListRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAssetAdminUserListRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAssetAdminUserListRequestValidationError{}

// Validate checks the field values on CreateAssetAdminUserRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateAssetAdminUserRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateAssetAdminUserRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateAssetAdminUserRequestMultiError, or nil if none found.
func (m *CreateAssetAdminUserRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateAssetAdminUserRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetOrganizationId()) < 1 {
		err := CreateAssetAdminUserRequestValidationError{
			field:  "OrganizationId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetUsername()) < 1 {
		err := CreateAssetAdminUserRequestValidationError{
			field:  "Username",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Password

	if utf8.RuneCountInString(m.GetPrivateKey()) < 1 {
		err := CreateAssetAdminUserRequestValidationError{
			field:  "PrivateKey",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Comment

	if len(errors) > 0 {
		return CreateAssetAdminUserRequestMultiError(errors)
	}

	return nil
}

// CreateAssetAdminUserRequestMultiError is an error wrapping multiple
// validation errors returned by CreateAssetAdminUserRequest.ValidateAll() if
// the designated constraints aren't met.
type CreateAssetAdminUserRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateAssetAdminUserRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateAssetAdminUserRequestMultiError) AllErrors() []error { return m }

// CreateAssetAdminUserRequestValidationError is the validation error returned
// by CreateAssetAdminUserRequest.Validate if the designated constraints
// aren't met.
type CreateAssetAdminUserRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateAssetAdminUserRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateAssetAdminUserRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateAssetAdminUserRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateAssetAdminUserRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateAssetAdminUserRequestValidationError) ErrorName() string {
	return "CreateAssetAdminUserRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateAssetAdminUserRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateAssetAdminUserRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateAssetAdminUserRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateAssetAdminUserRequestValidationError{}

// Validate checks the field values on UpdateAssetAdminUserRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateAssetAdminUserRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateAssetAdminUserRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateAssetAdminUserRequestMultiError, or nil if none found.
func (m *UpdateAssetAdminUserRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateAssetAdminUserRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if utf8.RuneCountInString(m.GetOrganizationId()) < 1 {
		err := UpdateAssetAdminUserRequestValidationError{
			field:  "OrganizationId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetUsername()) < 1 {
		err := UpdateAssetAdminUserRequestValidationError{
			field:  "Username",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Password

	if utf8.RuneCountInString(m.GetPrivateKey()) < 1 {
		err := UpdateAssetAdminUserRequestValidationError{
			field:  "PrivateKey",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Comment

	if len(errors) > 0 {
		return UpdateAssetAdminUserRequestMultiError(errors)
	}

	return nil
}

// UpdateAssetAdminUserRequestMultiError is an error wrapping multiple
// validation errors returned by UpdateAssetAdminUserRequest.ValidateAll() if
// the designated constraints aren't met.
type UpdateAssetAdminUserRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateAssetAdminUserRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateAssetAdminUserRequestMultiError) AllErrors() []error { return m }

// UpdateAssetAdminUserRequestValidationError is the validation error returned
// by UpdateAssetAdminUserRequest.Validate if the designated constraints
// aren't met.
type UpdateAssetAdminUserRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateAssetAdminUserRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateAssetAdminUserRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateAssetAdminUserRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateAssetAdminUserRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateAssetAdminUserRequestValidationError) ErrorName() string {
	return "UpdateAssetAdminUserRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateAssetAdminUserRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateAssetAdminUserRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateAssetAdminUserRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateAssetAdminUserRequestValidationError{}

// Validate checks the field values on DeleteAssetAdminUserRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteAssetAdminUserRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteAssetAdminUserRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteAssetAdminUserRequestMultiError, or nil if none found.
func (m *DeleteAssetAdminUserRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteAssetAdminUserRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return DeleteAssetAdminUserRequestMultiError(errors)
	}

	return nil
}

// DeleteAssetAdminUserRequestMultiError is an error wrapping multiple
// validation errors returned by DeleteAssetAdminUserRequest.ValidateAll() if
// the designated constraints aren't met.
type DeleteAssetAdminUserRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteAssetAdminUserRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteAssetAdminUserRequestMultiError) AllErrors() []error { return m }

// DeleteAssetAdminUserRequestValidationError is the validation error returned
// by DeleteAssetAdminUserRequest.Validate if the designated constraints
// aren't met.
type DeleteAssetAdminUserRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteAssetAdminUserRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteAssetAdminUserRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteAssetAdminUserRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteAssetAdminUserRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteAssetAdminUserRequestValidationError) ErrorName() string {
	return "DeleteAssetAdminUserRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteAssetAdminUserRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteAssetAdminUserRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteAssetAdminUserRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteAssetAdminUserRequestValidationError{}

// Validate checks the field values on GetAssetAdminUserListReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAssetAdminUserListReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAssetAdminUserListReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAssetAdminUserListReplyMultiError, or nil if none found.
func (m *GetAssetAdminUserListReply) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAssetAdminUserListReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetData() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetAssetAdminUserListReplyValidationError{
						field:  fmt.Sprintf("Data[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetAssetAdminUserListReplyValidationError{
						field:  fmt.Sprintf("Data[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetAssetAdminUserListReplyValidationError{
					field:  fmt.Sprintf("Data[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for PageNo

	// no validation rules for PageSize

	// no validation rules for TotalCount

	// no validation rules for TotalPage

	if len(errors) > 0 {
		return GetAssetAdminUserListReplyMultiError(errors)
	}

	return nil
}

// GetAssetAdminUserListReplyMultiError is an error wrapping multiple
// validation errors returned by GetAssetAdminUserListReply.ValidateAll() if
// the designated constraints aren't met.
type GetAssetAdminUserListReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAssetAdminUserListReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAssetAdminUserListReplyMultiError) AllErrors() []error { return m }

// GetAssetAdminUserListReplyValidationError is the validation error returned
// by GetAssetAdminUserListReply.Validate if the designated constraints aren't met.
type GetAssetAdminUserListReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAssetAdminUserListReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAssetAdminUserListReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAssetAdminUserListReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAssetAdminUserListReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAssetAdminUserListReplyValidationError) ErrorName() string {
	return "GetAssetAdminUserListReplyValidationError"
}

// Error satisfies the builtin error interface
func (e GetAssetAdminUserListReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAssetAdminUserListReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAssetAdminUserListReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAssetAdminUserListReplyValidationError{}

// Validate checks the field values on
// GetAssetProxyGatewaySelectReply_AssetProxyGateway with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetAssetProxyGatewaySelectReply_AssetProxyGateway) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetAssetProxyGatewaySelectReply_AssetProxyGateway with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// GetAssetProxyGatewaySelectReply_AssetProxyGatewayMultiError, or nil if none found.
func (m *GetAssetProxyGatewaySelectReply_AssetProxyGateway) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAssetProxyGatewaySelectReply_AssetProxyGateway) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for OrganizationId

	// no validation rules for Name

	// no validation rules for Ip

	// no validation rules for Port

	// no validation rules for Username

	// no validation rules for Comment

	// no validation rules for Idc

	if len(errors) > 0 {
		return GetAssetProxyGatewaySelectReply_AssetProxyGatewayMultiError(errors)
	}

	return nil
}

// GetAssetProxyGatewaySelectReply_AssetProxyGatewayMultiError is an error
// wrapping multiple validation errors returned by
// GetAssetProxyGatewaySelectReply_AssetProxyGateway.ValidateAll() if the
// designated constraints aren't met.
type GetAssetProxyGatewaySelectReply_AssetProxyGatewayMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAssetProxyGatewaySelectReply_AssetProxyGatewayMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAssetProxyGatewaySelectReply_AssetProxyGatewayMultiError) AllErrors() []error { return m }

// GetAssetProxyGatewaySelectReply_AssetProxyGatewayValidationError is the
// validation error returned by
// GetAssetProxyGatewaySelectReply_AssetProxyGateway.Validate if the
// designated constraints aren't met.
type GetAssetProxyGatewaySelectReply_AssetProxyGatewayValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAssetProxyGatewaySelectReply_AssetProxyGatewayValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e GetAssetProxyGatewaySelectReply_AssetProxyGatewayValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetAssetProxyGatewaySelectReply_AssetProxyGatewayValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e GetAssetProxyGatewaySelectReply_AssetProxyGatewayValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAssetProxyGatewaySelectReply_AssetProxyGatewayValidationError) ErrorName() string {
	return "GetAssetProxyGatewaySelectReply_AssetProxyGatewayValidationError"
}

// Error satisfies the builtin error interface
func (e GetAssetProxyGatewaySelectReply_AssetProxyGatewayValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAssetProxyGatewaySelectReply_AssetProxyGateway.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAssetProxyGatewaySelectReply_AssetProxyGatewayValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAssetProxyGatewaySelectReply_AssetProxyGatewayValidationError{}

// Validate checks the field values on ListUserWorkSpaceReply_WorkSpace with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ListUserWorkSpaceReply_WorkSpace) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListUserWorkSpaceReply_WorkSpace with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ListUserWorkSpaceReply_WorkSpaceMultiError, or nil if none found.
func (m *ListUserWorkSpaceReply_WorkSpace) ValidateAll() error {
	return m.validate(true)
}

func (m *ListUserWorkSpaceReply_WorkSpace) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Key

	// no validation rules for Label

	if len(errors) > 0 {
		return ListUserWorkSpaceReply_WorkSpaceMultiError(errors)
	}

	return nil
}

// ListUserWorkSpaceReply_WorkSpaceMultiError is an error wrapping multiple
// validation errors returned by
// ListUserWorkSpaceReply_WorkSpace.ValidateAll() if the designated
// constraints aren't met.
type ListUserWorkSpaceReply_WorkSpaceMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListUserWorkSpaceReply_WorkSpaceMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListUserWorkSpaceReply_WorkSpaceMultiError) AllErrors() []error { return m }

// ListUserWorkSpaceReply_WorkSpaceValidationError is the validation error
// returned by ListUserWorkSpaceReply_WorkSpace.Validate if the designated
// constraints aren't met.
type ListUserWorkSpaceReply_WorkSpaceValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListUserWorkSpaceReply_WorkSpaceValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListUserWorkSpaceReply_WorkSpaceValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListUserWorkSpaceReply_WorkSpaceValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListUserWorkSpaceReply_WorkSpaceValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListUserWorkSpaceReply_WorkSpaceValidationError) ErrorName() string {
	return "ListUserWorkSpaceReply_WorkSpaceValidationError"
}

// Error satisfies the builtin error interface
func (e ListUserWorkSpaceReply_WorkSpaceValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListUserWorkSpaceReply_WorkSpace.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListUserWorkSpaceReply_WorkSpaceValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListUserWorkSpaceReply_WorkSpaceValidationError{}

// Validate checks the field values on ListAssetRouteReply_Children with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListAssetRouteReply_Children) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListAssetRouteReply_Children with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListAssetRouteReply_ChildrenMultiError, or nil if none found.
func (m *ListAssetRouteReply_Children) ValidateAll() error {
	return m.validate(true)
}

func (m *ListAssetRouteReply_Children) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Key

	// no validation rules for Title

	// no validation rules for Favorite

	// no validation rules for Ip

	// no validation rules for OrganizationId

	if len(errors) > 0 {
		return ListAssetRouteReply_ChildrenMultiError(errors)
	}

	return nil
}

// ListAssetRouteReply_ChildrenMultiError is an error wrapping multiple
// validation errors returned by ListAssetRouteReply_Children.ValidateAll() if
// the designated constraints aren't met.
type ListAssetRouteReply_ChildrenMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListAssetRouteReply_ChildrenMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListAssetRouteReply_ChildrenMultiError) AllErrors() []error { return m }

// ListAssetRouteReply_ChildrenValidationError is the validation error returned
// by ListAssetRouteReply_Children.Validate if the designated constraints
// aren't met.
type ListAssetRouteReply_ChildrenValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListAssetRouteReply_ChildrenValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListAssetRouteReply_ChildrenValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListAssetRouteReply_ChildrenValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListAssetRouteReply_ChildrenValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListAssetRouteReply_ChildrenValidationError) ErrorName() string {
	return "ListAssetRouteReply_ChildrenValidationError"
}

// Error satisfies the builtin error interface
func (e ListAssetRouteReply_ChildrenValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListAssetRouteReply_Children.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListAssetRouteReply_ChildrenValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListAssetRouteReply_ChildrenValidationError{}

// Validate checks the field values on ListAssetRouteReply_Route with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListAssetRouteReply_Route) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListAssetRouteReply_Route with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListAssetRouteReply_RouteMultiError, or nil if none found.
func (m *ListAssetRouteReply_Route) ValidateAll() error {
	return m.validate(true)
}

func (m *ListAssetRouteReply_Route) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Key

	// no validation rules for Title

	for idx, item := range m.GetChildren() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListAssetRouteReply_RouteValidationError{
						field:  fmt.Sprintf("Children[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListAssetRouteReply_RouteValidationError{
						field:  fmt.Sprintf("Children[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListAssetRouteReply_RouteValidationError{
					field:  fmt.Sprintf("Children[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListAssetRouteReply_RouteMultiError(errors)
	}

	return nil
}

// ListAssetRouteReply_RouteMultiError is an error wrapping multiple validation
// errors returned by ListAssetRouteReply_Route.ValidateAll() if the
// designated constraints aren't met.
type ListAssetRouteReply_RouteMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListAssetRouteReply_RouteMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListAssetRouteReply_RouteMultiError) AllErrors() []error { return m }

// ListAssetRouteReply_RouteValidationError is the validation error returned by
// ListAssetRouteReply_Route.Validate if the designated constraints aren't met.
type ListAssetRouteReply_RouteValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListAssetRouteReply_RouteValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListAssetRouteReply_RouteValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListAssetRouteReply_RouteValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListAssetRouteReply_RouteValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListAssetRouteReply_RouteValidationError) ErrorName() string {
	return "ListAssetRouteReply_RouteValidationError"
}

// Error satisfies the builtin error interface
func (e ListAssetRouteReply_RouteValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListAssetRouteReply_Route.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListAssetRouteReply_RouteValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListAssetRouteReply_RouteValidationError{}

// Validate checks the field values on GetAssetListReply_Asset with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAssetListReply_Asset) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAssetListReply_Asset with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAssetListReply_AssetMultiError, or nil if none found.
func (m *GetAssetListReply_Asset) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAssetListReply_Asset) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Uuid

	// no validation rules for Name

	// no validation rules for AssetIp

	// no validation rules for Active

	// no validation rules for Platform

	// no validation rules for ProxyGatewayId

	// no validation rules for Protocol

	// no validation rules for Os

	// no validation rules for OrganizationId

	// no validation rules for Comment

	// no validation rules for CreateUser

	// no validation rules for Idc

	// no validation rules for CreatedAt

	// no validation rules for UpdatedAt

	if len(errors) > 0 {
		return GetAssetListReply_AssetMultiError(errors)
	}

	return nil
}

// GetAssetListReply_AssetMultiError is an error wrapping multiple validation
// errors returned by GetAssetListReply_Asset.ValidateAll() if the designated
// constraints aren't met.
type GetAssetListReply_AssetMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAssetListReply_AssetMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAssetListReply_AssetMultiError) AllErrors() []error { return m }

// GetAssetListReply_AssetValidationError is the validation error returned by
// GetAssetListReply_Asset.Validate if the designated constraints aren't met.
type GetAssetListReply_AssetValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAssetListReply_AssetValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAssetListReply_AssetValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAssetListReply_AssetValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAssetListReply_AssetValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAssetListReply_AssetValidationError) ErrorName() string {
	return "GetAssetListReply_AssetValidationError"
}

// Error satisfies the builtin error interface
func (e GetAssetListReply_AssetValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAssetListReply_Asset.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAssetListReply_AssetValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAssetListReply_AssetValidationError{}

// Validate checks the field values on GetAssetListByIpReply_Asset with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAssetListByIpReply_Asset) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAssetListByIpReply_Asset with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAssetListByIpReply_AssetMultiError, or nil if none found.
func (m *GetAssetListByIpReply_Asset) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAssetListByIpReply_Asset) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Name

	// no validation rules for AssetIp

	// no validation rules for OrganizationId

	// no validation rules for Uuid

	if len(errors) > 0 {
		return GetAssetListByIpReply_AssetMultiError(errors)
	}

	return nil
}

// GetAssetListByIpReply_AssetMultiError is an error wrapping multiple
// validation errors returned by GetAssetListByIpReply_Asset.ValidateAll() if
// the designated constraints aren't met.
type GetAssetListByIpReply_AssetMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAssetListByIpReply_AssetMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAssetListByIpReply_AssetMultiError) AllErrors() []error { return m }

// GetAssetListByIpReply_AssetValidationError is the validation error returned
// by GetAssetListByIpReply_Asset.Validate if the designated constraints
// aren't met.
type GetAssetListByIpReply_AssetValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAssetListByIpReply_AssetValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAssetListByIpReply_AssetValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAssetListByIpReply_AssetValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAssetListByIpReply_AssetValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAssetListByIpReply_AssetValidationError) ErrorName() string {
	return "GetAssetListByIpReply_AssetValidationError"
}

// Error satisfies the builtin error interface
func (e GetAssetListByIpReply_AssetValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAssetListByIpReply_Asset.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAssetListByIpReply_AssetValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAssetListByIpReply_AssetValidationError{}

// Validate checks the field values on
// GetAssetProxyGatewayListReply_AssetProxyGateway with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetAssetProxyGatewayListReply_AssetProxyGateway) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetAssetProxyGatewayListReply_AssetProxyGateway with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// GetAssetProxyGatewayListReply_AssetProxyGatewayMultiError, or nil if none found.
func (m *GetAssetProxyGatewayListReply_AssetProxyGateway) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAssetProxyGatewayListReply_AssetProxyGateway) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for OrganizationId

	// no validation rules for Name

	// no validation rules for Ip

	// no validation rules for Port

	// no validation rules for Username

	// no validation rules for Comment

	// no validation rules for Idc

	if len(errors) > 0 {
		return GetAssetProxyGatewayListReply_AssetProxyGatewayMultiError(errors)
	}

	return nil
}

// GetAssetProxyGatewayListReply_AssetProxyGatewayMultiError is an error
// wrapping multiple validation errors returned by
// GetAssetProxyGatewayListReply_AssetProxyGateway.ValidateAll() if the
// designated constraints aren't met.
type GetAssetProxyGatewayListReply_AssetProxyGatewayMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAssetProxyGatewayListReply_AssetProxyGatewayMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAssetProxyGatewayListReply_AssetProxyGatewayMultiError) AllErrors() []error { return m }

// GetAssetProxyGatewayListReply_AssetProxyGatewayValidationError is the
// validation error returned by
// GetAssetProxyGatewayListReply_AssetProxyGateway.Validate if the designated
// constraints aren't met.
type GetAssetProxyGatewayListReply_AssetProxyGatewayValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAssetProxyGatewayListReply_AssetProxyGatewayValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e GetAssetProxyGatewayListReply_AssetProxyGatewayValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetAssetProxyGatewayListReply_AssetProxyGatewayValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAssetProxyGatewayListReply_AssetProxyGatewayValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAssetProxyGatewayListReply_AssetProxyGatewayValidationError) ErrorName() string {
	return "GetAssetProxyGatewayListReply_AssetProxyGatewayValidationError"
}

// Error satisfies the builtin error interface
func (e GetAssetProxyGatewayListReply_AssetProxyGatewayValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAssetProxyGatewayListReply_AssetProxyGateway.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAssetProxyGatewayListReply_AssetProxyGatewayValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAssetProxyGatewayListReply_AssetProxyGatewayValidationError{}

// Validate checks the field values on
// GetAssetAdminUserListReply_AssetAdminUser with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetAssetAdminUserListReply_AssetAdminUser) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetAssetAdminUserListReply_AssetAdminUser with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetAssetAdminUserListReply_AssetAdminUserMultiError, or nil if none found.
func (m *GetAssetAdminUserListReply_AssetAdminUser) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAssetAdminUserListReply_AssetAdminUser) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for OrganizationId

	// no validation rules for Username

	if len(errors) > 0 {
		return GetAssetAdminUserListReply_AssetAdminUserMultiError(errors)
	}

	return nil
}

// GetAssetAdminUserListReply_AssetAdminUserMultiError is an error wrapping
// multiple validation errors returned by
// GetAssetAdminUserListReply_AssetAdminUser.ValidateAll() if the designated
// constraints aren't met.
type GetAssetAdminUserListReply_AssetAdminUserMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAssetAdminUserListReply_AssetAdminUserMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAssetAdminUserListReply_AssetAdminUserMultiError) AllErrors() []error { return m }

// GetAssetAdminUserListReply_AssetAdminUserValidationError is the validation
// error returned by GetAssetAdminUserListReply_AssetAdminUser.Validate if the
// designated constraints aren't met.
type GetAssetAdminUserListReply_AssetAdminUserValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAssetAdminUserListReply_AssetAdminUserValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAssetAdminUserListReply_AssetAdminUserValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAssetAdminUserListReply_AssetAdminUserValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAssetAdminUserListReply_AssetAdminUserValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAssetAdminUserListReply_AssetAdminUserValidationError) ErrorName() string {
	return "GetAssetAdminUserListReply_AssetAdminUserValidationError"
}

// Error satisfies the builtin error interface
func (e GetAssetAdminUserListReply_AssetAdminUserValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAssetAdminUserListReply_AssetAdminUser.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAssetAdminUserListReply_AssetAdminUserValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAssetAdminUserListReply_AssetAdminUserValidationError{}
