// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/asset/v1/asset.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Asset_GetAssetList_FullMethodName               = "/asset.v1.Asset/GetAssetList"
	Asset_CreateAsset_FullMethodName                = "/asset.v1.Asset/CreateAsset"
	Asset_UpdateAsset_FullMethodName                = "/asset.v1.Asset/UpdateAsset"
	Asset_DeleteAsset_FullMethodName                = "/asset.v1.Asset/DeleteAsset"
	Asset_UserAssetFavorite_FullMethodName          = "/asset.v1.Asset/UserAssetFavorite"
	Asset_ListAssetRoute_FullMethodName             = "/asset.v1.Asset/ListAssetRoute"
	Asset_ListUserWorkSpace_FullMethodName          = "/asset.v1.Asset/ListUserWorkSpace"
	Asset_UserAssetAlias_FullMethodName             = "/asset.v1.Asset/userAssetAlias"
	Asset_GetAssetListByIp_FullMethodName           = "/asset.v1.Asset/GetAssetListByIp"
	Asset_GetAssetProxyGatewaySelect_FullMethodName = "/asset.v1.Asset/GetAssetProxyGatewaySelect"
	Asset_GetAssetProxyGatewayList_FullMethodName   = "/asset.v1.Asset/GetAssetProxyGatewayList"
	Asset_CreateAssetProxyGateway_FullMethodName    = "/asset.v1.Asset/CreateAssetProxyGateway"
	Asset_UpdateAssetProxyGateway_FullMethodName    = "/asset.v1.Asset/UpdateAssetProxyGateway"
	Asset_DeleteAssetProxyGateway_FullMethodName    = "/asset.v1.Asset/DeleteAssetProxyGateway"
	Asset_GetAssetAdminUserList_FullMethodName      = "/asset.v1.Asset/GetAssetAdminUserList"
	Asset_CreateAssetAdminUser_FullMethodName       = "/asset.v1.Asset/CreateAssetAdminUser"
	Asset_UpdateAssetAdminUser_FullMethodName       = "/asset.v1.Asset/UpdateAssetAdminUser"
	Asset_DeleteAssetAdminUser_FullMethodName       = "/asset.v1.Asset/DeleteAssetAdminUser"
)

// AssetClient is the client API for Asset service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AssetClient interface {
	GetAssetList(ctx context.Context, in *GetAssetListRequest, opts ...grpc.CallOption) (*GetAssetListReply, error)
	CreateAsset(ctx context.Context, in *CreateAssetRequest, opts ...grpc.CallOption) (*Reply, error)
	UpdateAsset(ctx context.Context, in *UpdateAseetRequest, opts ...grpc.CallOption) (*Reply, error)
	DeleteAsset(ctx context.Context, in *DeleteAssetRequest, opts ...grpc.CallOption) (*Reply, error)
	UserAssetFavorite(ctx context.Context, in *UserAssetFavoriteRequest, opts ...grpc.CallOption) (*Reply, error)
	ListAssetRoute(ctx context.Context, in *ListAssetRouteRequest, opts ...grpc.CallOption) (*ListAssetRouteReply, error)
	ListUserWorkSpace(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*ListUserWorkSpaceReply, error)
	UserAssetAlias(ctx context.Context, in *UserAssetAliasRequest, opts ...grpc.CallOption) (*Reply, error)
	GetAssetListByIp(ctx context.Context, in *GetAssetListByIpRequest, opts ...grpc.CallOption) (*GetAssetListByIpReply, error)
	GetAssetProxyGatewaySelect(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*GetAssetProxyGatewaySelectReply, error)
	GetAssetProxyGatewayList(ctx context.Context, in *GetAssetProxyGatewayListRequest, opts ...grpc.CallOption) (*GetAssetProxyGatewayListReply, error)
	CreateAssetProxyGateway(ctx context.Context, in *CreateAssetProxyGatewayRequest, opts ...grpc.CallOption) (*Reply, error)
	UpdateAssetProxyGateway(ctx context.Context, in *UpdateAssetProxyGatewayRequest, opts ...grpc.CallOption) (*Reply, error)
	DeleteAssetProxyGateway(ctx context.Context, in *DeleteAssetProxyGatewayRequest, opts ...grpc.CallOption) (*Reply, error)
	GetAssetAdminUserList(ctx context.Context, in *GetAssetAdminUserListRequest, opts ...grpc.CallOption) (*GetAssetAdminUserListReply, error)
	CreateAssetAdminUser(ctx context.Context, in *CreateAssetAdminUserRequest, opts ...grpc.CallOption) (*Reply, error)
	UpdateAssetAdminUser(ctx context.Context, in *UpdateAssetAdminUserRequest, opts ...grpc.CallOption) (*Reply, error)
	DeleteAssetAdminUser(ctx context.Context, in *DeleteAssetAdminUserRequest, opts ...grpc.CallOption) (*Reply, error)
}

type assetClient struct {
	cc grpc.ClientConnInterface
}

func NewAssetClient(cc grpc.ClientConnInterface) AssetClient {
	return &assetClient{cc}
}

func (c *assetClient) GetAssetList(ctx context.Context, in *GetAssetListRequest, opts ...grpc.CallOption) (*GetAssetListReply, error) {
	out := new(GetAssetListReply)
	err := c.cc.Invoke(ctx, Asset_GetAssetList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *assetClient) CreateAsset(ctx context.Context, in *CreateAssetRequest, opts ...grpc.CallOption) (*Reply, error) {
	out := new(Reply)
	err := c.cc.Invoke(ctx, Asset_CreateAsset_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *assetClient) UpdateAsset(ctx context.Context, in *UpdateAseetRequest, opts ...grpc.CallOption) (*Reply, error) {
	out := new(Reply)
	err := c.cc.Invoke(ctx, Asset_UpdateAsset_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *assetClient) DeleteAsset(ctx context.Context, in *DeleteAssetRequest, opts ...grpc.CallOption) (*Reply, error) {
	out := new(Reply)
	err := c.cc.Invoke(ctx, Asset_DeleteAsset_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *assetClient) UserAssetFavorite(ctx context.Context, in *UserAssetFavoriteRequest, opts ...grpc.CallOption) (*Reply, error) {
	out := new(Reply)
	err := c.cc.Invoke(ctx, Asset_UserAssetFavorite_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *assetClient) ListAssetRoute(ctx context.Context, in *ListAssetRouteRequest, opts ...grpc.CallOption) (*ListAssetRouteReply, error) {
	out := new(ListAssetRouteReply)
	err := c.cc.Invoke(ctx, Asset_ListAssetRoute_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *assetClient) ListUserWorkSpace(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*ListUserWorkSpaceReply, error) {
	out := new(ListUserWorkSpaceReply)
	err := c.cc.Invoke(ctx, Asset_ListUserWorkSpace_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *assetClient) UserAssetAlias(ctx context.Context, in *UserAssetAliasRequest, opts ...grpc.CallOption) (*Reply, error) {
	out := new(Reply)
	err := c.cc.Invoke(ctx, Asset_UserAssetAlias_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *assetClient) GetAssetListByIp(ctx context.Context, in *GetAssetListByIpRequest, opts ...grpc.CallOption) (*GetAssetListByIpReply, error) {
	out := new(GetAssetListByIpReply)
	err := c.cc.Invoke(ctx, Asset_GetAssetListByIp_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *assetClient) GetAssetProxyGatewaySelect(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*GetAssetProxyGatewaySelectReply, error) {
	out := new(GetAssetProxyGatewaySelectReply)
	err := c.cc.Invoke(ctx, Asset_GetAssetProxyGatewaySelect_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *assetClient) GetAssetProxyGatewayList(ctx context.Context, in *GetAssetProxyGatewayListRequest, opts ...grpc.CallOption) (*GetAssetProxyGatewayListReply, error) {
	out := new(GetAssetProxyGatewayListReply)
	err := c.cc.Invoke(ctx, Asset_GetAssetProxyGatewayList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *assetClient) CreateAssetProxyGateway(ctx context.Context, in *CreateAssetProxyGatewayRequest, opts ...grpc.CallOption) (*Reply, error) {
	out := new(Reply)
	err := c.cc.Invoke(ctx, Asset_CreateAssetProxyGateway_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *assetClient) UpdateAssetProxyGateway(ctx context.Context, in *UpdateAssetProxyGatewayRequest, opts ...grpc.CallOption) (*Reply, error) {
	out := new(Reply)
	err := c.cc.Invoke(ctx, Asset_UpdateAssetProxyGateway_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *assetClient) DeleteAssetProxyGateway(ctx context.Context, in *DeleteAssetProxyGatewayRequest, opts ...grpc.CallOption) (*Reply, error) {
	out := new(Reply)
	err := c.cc.Invoke(ctx, Asset_DeleteAssetProxyGateway_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *assetClient) GetAssetAdminUserList(ctx context.Context, in *GetAssetAdminUserListRequest, opts ...grpc.CallOption) (*GetAssetAdminUserListReply, error) {
	out := new(GetAssetAdminUserListReply)
	err := c.cc.Invoke(ctx, Asset_GetAssetAdminUserList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *assetClient) CreateAssetAdminUser(ctx context.Context, in *CreateAssetAdminUserRequest, opts ...grpc.CallOption) (*Reply, error) {
	out := new(Reply)
	err := c.cc.Invoke(ctx, Asset_CreateAssetAdminUser_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *assetClient) UpdateAssetAdminUser(ctx context.Context, in *UpdateAssetAdminUserRequest, opts ...grpc.CallOption) (*Reply, error) {
	out := new(Reply)
	err := c.cc.Invoke(ctx, Asset_UpdateAssetAdminUser_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *assetClient) DeleteAssetAdminUser(ctx context.Context, in *DeleteAssetAdminUserRequest, opts ...grpc.CallOption) (*Reply, error) {
	out := new(Reply)
	err := c.cc.Invoke(ctx, Asset_DeleteAssetAdminUser_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AssetServer is the server API for Asset service.
// All implementations must embed UnimplementedAssetServer
// for forward compatibility
type AssetServer interface {
	GetAssetList(context.Context, *GetAssetListRequest) (*GetAssetListReply, error)
	CreateAsset(context.Context, *CreateAssetRequest) (*Reply, error)
	UpdateAsset(context.Context, *UpdateAseetRequest) (*Reply, error)
	DeleteAsset(context.Context, *DeleteAssetRequest) (*Reply, error)
	UserAssetFavorite(context.Context, *UserAssetFavoriteRequest) (*Reply, error)
	ListAssetRoute(context.Context, *ListAssetRouteRequest) (*ListAssetRouteReply, error)
	ListUserWorkSpace(context.Context, *emptypb.Empty) (*ListUserWorkSpaceReply, error)
	UserAssetAlias(context.Context, *UserAssetAliasRequest) (*Reply, error)
	GetAssetListByIp(context.Context, *GetAssetListByIpRequest) (*GetAssetListByIpReply, error)
	GetAssetProxyGatewaySelect(context.Context, *emptypb.Empty) (*GetAssetProxyGatewaySelectReply, error)
	GetAssetProxyGatewayList(context.Context, *GetAssetProxyGatewayListRequest) (*GetAssetProxyGatewayListReply, error)
	CreateAssetProxyGateway(context.Context, *CreateAssetProxyGatewayRequest) (*Reply, error)
	UpdateAssetProxyGateway(context.Context, *UpdateAssetProxyGatewayRequest) (*Reply, error)
	DeleteAssetProxyGateway(context.Context, *DeleteAssetProxyGatewayRequest) (*Reply, error)
	GetAssetAdminUserList(context.Context, *GetAssetAdminUserListRequest) (*GetAssetAdminUserListReply, error)
	CreateAssetAdminUser(context.Context, *CreateAssetAdminUserRequest) (*Reply, error)
	UpdateAssetAdminUser(context.Context, *UpdateAssetAdminUserRequest) (*Reply, error)
	DeleteAssetAdminUser(context.Context, *DeleteAssetAdminUserRequest) (*Reply, error)
	mustEmbedUnimplementedAssetServer()
}

// UnimplementedAssetServer must be embedded to have forward compatible implementations.
type UnimplementedAssetServer struct {
}

func (UnimplementedAssetServer) GetAssetList(context.Context, *GetAssetListRequest) (*GetAssetListReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAssetList not implemented")
}
func (UnimplementedAssetServer) CreateAsset(context.Context, *CreateAssetRequest) (*Reply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateAsset not implemented")
}
func (UnimplementedAssetServer) UpdateAsset(context.Context, *UpdateAseetRequest) (*Reply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateAsset not implemented")
}
func (UnimplementedAssetServer) DeleteAsset(context.Context, *DeleteAssetRequest) (*Reply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteAsset not implemented")
}
func (UnimplementedAssetServer) UserAssetFavorite(context.Context, *UserAssetFavoriteRequest) (*Reply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserAssetFavorite not implemented")
}
func (UnimplementedAssetServer) ListAssetRoute(context.Context, *ListAssetRouteRequest) (*ListAssetRouteReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAssetRoute not implemented")
}
func (UnimplementedAssetServer) ListUserWorkSpace(context.Context, *emptypb.Empty) (*ListUserWorkSpaceReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListUserWorkSpace not implemented")
}
func (UnimplementedAssetServer) UserAssetAlias(context.Context, *UserAssetAliasRequest) (*Reply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserAssetAlias not implemented")
}
func (UnimplementedAssetServer) GetAssetListByIp(context.Context, *GetAssetListByIpRequest) (*GetAssetListByIpReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAssetListByIp not implemented")
}
func (UnimplementedAssetServer) GetAssetProxyGatewaySelect(context.Context, *emptypb.Empty) (*GetAssetProxyGatewaySelectReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAssetProxyGatewaySelect not implemented")
}
func (UnimplementedAssetServer) GetAssetProxyGatewayList(context.Context, *GetAssetProxyGatewayListRequest) (*GetAssetProxyGatewayListReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAssetProxyGatewayList not implemented")
}
func (UnimplementedAssetServer) CreateAssetProxyGateway(context.Context, *CreateAssetProxyGatewayRequest) (*Reply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateAssetProxyGateway not implemented")
}
func (UnimplementedAssetServer) UpdateAssetProxyGateway(context.Context, *UpdateAssetProxyGatewayRequest) (*Reply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateAssetProxyGateway not implemented")
}
func (UnimplementedAssetServer) DeleteAssetProxyGateway(context.Context, *DeleteAssetProxyGatewayRequest) (*Reply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteAssetProxyGateway not implemented")
}
func (UnimplementedAssetServer) GetAssetAdminUserList(context.Context, *GetAssetAdminUserListRequest) (*GetAssetAdminUserListReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAssetAdminUserList not implemented")
}
func (UnimplementedAssetServer) CreateAssetAdminUser(context.Context, *CreateAssetAdminUserRequest) (*Reply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateAssetAdminUser not implemented")
}
func (UnimplementedAssetServer) UpdateAssetAdminUser(context.Context, *UpdateAssetAdminUserRequest) (*Reply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateAssetAdminUser not implemented")
}
func (UnimplementedAssetServer) DeleteAssetAdminUser(context.Context, *DeleteAssetAdminUserRequest) (*Reply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteAssetAdminUser not implemented")
}
func (UnimplementedAssetServer) mustEmbedUnimplementedAssetServer() {}

// UnsafeAssetServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AssetServer will
// result in compilation errors.
type UnsafeAssetServer interface {
	mustEmbedUnimplementedAssetServer()
}

func RegisterAssetServer(s grpc.ServiceRegistrar, srv AssetServer) {
	s.RegisterService(&Asset_ServiceDesc, srv)
}

func _Asset_GetAssetList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAssetListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AssetServer).GetAssetList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Asset_GetAssetList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AssetServer).GetAssetList(ctx, req.(*GetAssetListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Asset_CreateAsset_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateAssetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AssetServer).CreateAsset(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Asset_CreateAsset_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AssetServer).CreateAsset(ctx, req.(*CreateAssetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Asset_UpdateAsset_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAseetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AssetServer).UpdateAsset(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Asset_UpdateAsset_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AssetServer).UpdateAsset(ctx, req.(*UpdateAseetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Asset_DeleteAsset_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteAssetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AssetServer).DeleteAsset(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Asset_DeleteAsset_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AssetServer).DeleteAsset(ctx, req.(*DeleteAssetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Asset_UserAssetFavorite_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserAssetFavoriteRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AssetServer).UserAssetFavorite(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Asset_UserAssetFavorite_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AssetServer).UserAssetFavorite(ctx, req.(*UserAssetFavoriteRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Asset_ListAssetRoute_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListAssetRouteRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AssetServer).ListAssetRoute(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Asset_ListAssetRoute_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AssetServer).ListAssetRoute(ctx, req.(*ListAssetRouteRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Asset_ListUserWorkSpace_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AssetServer).ListUserWorkSpace(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Asset_ListUserWorkSpace_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AssetServer).ListUserWorkSpace(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _Asset_UserAssetAlias_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserAssetAliasRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AssetServer).UserAssetAlias(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Asset_UserAssetAlias_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AssetServer).UserAssetAlias(ctx, req.(*UserAssetAliasRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Asset_GetAssetListByIp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAssetListByIpRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AssetServer).GetAssetListByIp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Asset_GetAssetListByIp_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AssetServer).GetAssetListByIp(ctx, req.(*GetAssetListByIpRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Asset_GetAssetProxyGatewaySelect_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AssetServer).GetAssetProxyGatewaySelect(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Asset_GetAssetProxyGatewaySelect_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AssetServer).GetAssetProxyGatewaySelect(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _Asset_GetAssetProxyGatewayList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAssetProxyGatewayListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AssetServer).GetAssetProxyGatewayList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Asset_GetAssetProxyGatewayList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AssetServer).GetAssetProxyGatewayList(ctx, req.(*GetAssetProxyGatewayListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Asset_CreateAssetProxyGateway_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateAssetProxyGatewayRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AssetServer).CreateAssetProxyGateway(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Asset_CreateAssetProxyGateway_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AssetServer).CreateAssetProxyGateway(ctx, req.(*CreateAssetProxyGatewayRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Asset_UpdateAssetProxyGateway_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAssetProxyGatewayRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AssetServer).UpdateAssetProxyGateway(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Asset_UpdateAssetProxyGateway_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AssetServer).UpdateAssetProxyGateway(ctx, req.(*UpdateAssetProxyGatewayRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Asset_DeleteAssetProxyGateway_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteAssetProxyGatewayRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AssetServer).DeleteAssetProxyGateway(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Asset_DeleteAssetProxyGateway_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AssetServer).DeleteAssetProxyGateway(ctx, req.(*DeleteAssetProxyGatewayRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Asset_GetAssetAdminUserList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAssetAdminUserListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AssetServer).GetAssetAdminUserList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Asset_GetAssetAdminUserList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AssetServer).GetAssetAdminUserList(ctx, req.(*GetAssetAdminUserListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Asset_CreateAssetAdminUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateAssetAdminUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AssetServer).CreateAssetAdminUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Asset_CreateAssetAdminUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AssetServer).CreateAssetAdminUser(ctx, req.(*CreateAssetAdminUserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Asset_UpdateAssetAdminUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAssetAdminUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AssetServer).UpdateAssetAdminUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Asset_UpdateAssetAdminUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AssetServer).UpdateAssetAdminUser(ctx, req.(*UpdateAssetAdminUserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Asset_DeleteAssetAdminUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteAssetAdminUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AssetServer).DeleteAssetAdminUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Asset_DeleteAssetAdminUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AssetServer).DeleteAssetAdminUser(ctx, req.(*DeleteAssetAdminUserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Asset_ServiceDesc is the grpc.ServiceDesc for Asset service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Asset_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "asset.v1.Asset",
	HandlerType: (*AssetServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetAssetList",
			Handler:    _Asset_GetAssetList_Handler,
		},
		{
			MethodName: "CreateAsset",
			Handler:    _Asset_CreateAsset_Handler,
		},
		{
			MethodName: "UpdateAsset",
			Handler:    _Asset_UpdateAsset_Handler,
		},
		{
			MethodName: "DeleteAsset",
			Handler:    _Asset_DeleteAsset_Handler,
		},
		{
			MethodName: "UserAssetFavorite",
			Handler:    _Asset_UserAssetFavorite_Handler,
		},
		{
			MethodName: "ListAssetRoute",
			Handler:    _Asset_ListAssetRoute_Handler,
		},
		{
			MethodName: "ListUserWorkSpace",
			Handler:    _Asset_ListUserWorkSpace_Handler,
		},
		{
			MethodName: "userAssetAlias",
			Handler:    _Asset_UserAssetAlias_Handler,
		},
		{
			MethodName: "GetAssetListByIp",
			Handler:    _Asset_GetAssetListByIp_Handler,
		},
		{
			MethodName: "GetAssetProxyGatewaySelect",
			Handler:    _Asset_GetAssetProxyGatewaySelect_Handler,
		},
		{
			MethodName: "GetAssetProxyGatewayList",
			Handler:    _Asset_GetAssetProxyGatewayList_Handler,
		},
		{
			MethodName: "CreateAssetProxyGateway",
			Handler:    _Asset_CreateAssetProxyGateway_Handler,
		},
		{
			MethodName: "UpdateAssetProxyGateway",
			Handler:    _Asset_UpdateAssetProxyGateway_Handler,
		},
		{
			MethodName: "DeleteAssetProxyGateway",
			Handler:    _Asset_DeleteAssetProxyGateway_Handler,
		},
		{
			MethodName: "GetAssetAdminUserList",
			Handler:    _Asset_GetAssetAdminUserList_Handler,
		},
		{
			MethodName: "CreateAssetAdminUser",
			Handler:    _Asset_CreateAssetAdminUser_Handler,
		},
		{
			MethodName: "UpdateAssetAdminUser",
			Handler:    _Asset_UpdateAssetAdminUser_Handler,
		},
		{
			MethodName: "DeleteAssetAdminUser",
			Handler:    _Asset_DeleteAssetAdminUser_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/asset/v1/asset.proto",
}
