syntax = "proto3";

package asset.v1;

import "google/api/annotations.proto";
import "validate/validate.proto";

option go_package = "gitlab.intsig.net/NOC/ctm/chaterm/chaterm_backend/api/asset/v1;v1";
import "google/protobuf/empty.proto";

service Asset {
  rpc GetAssetList (GetAssetListRequest) returns (GetAssetListReply) {
    option (google.api.http) = {
      get: "/v1/asset/assets"
    };
  }
  rpc CreateAsset (CreateAssetRequest) returns (Reply) {
    option (google.api.http) = {
      post: "/v1/asset/assets"
      body: "*"
    };
  }
  rpc UpdateAsset (UpdateAseetRequest) returns (Reply) {
    option (google.api.http) = {
      put: "/v1/asset/assets"
      body: "*"
    };
  }

  rpc DeleteAsset (DeleteAssetRequest) returns (Reply) {
    option (google.api.http) = {
      delete: "/v1/asset/assets/{uuid}"
    };
  }

  rpc UserAssetFavorite (UserAssetFavoriteRequest) returns (Reply) {
    option (google.api.http) = {
      post: "/v1/asset/favorite"
      body: "*"
    };
  }
  rpc ListAssetRoute (ListAssetRouteRequest) returns (ListAssetRouteReply) {
    option (google.api.http) = {
      get: "/v1/asset/routes"
    };
  }
  rpc ListUserWorkSpace (google.protobuf.Empty) returns (ListUserWorkSpaceReply) {
    option (google.api.http) = {
      get: "/v1/asset/user-work-space"
    };
  }
  rpc userAssetAlias(UserAssetAliasRequest) returns (Reply) {
    option (google.api.http) = {
      post: "/v1/asset/alias"
      body: "*"
    };
  }
  rpc GetAssetListByIp (GetAssetListByIpRequest) returns (GetAssetListByIpReply) {
    option (google.api.http) = {
      get: "/v1/asset/ips"
    };
  }

  rpc GetAssetProxyGatewaySelect (google.protobuf.Empty) returns (GetAssetProxyGatewaySelectReply) {
    option (google.api.http) = {
      get: "/v1/asset/proxy-gateways/select"
    };
  }

  rpc GetAssetProxyGatewayList (GetAssetProxyGatewayListRequest) returns (GetAssetProxyGatewayListReply) {
    option (google.api.http) = {
      get: "/v1/asset/proxy-gateways"
    };
  }
  rpc CreateAssetProxyGateway (CreateAssetProxyGatewayRequest) returns (Reply) {
    option (google.api.http) = {
      post: "/v1/asset/proxy-gateways"
      body: "*"
    };
  }
  rpc UpdateAssetProxyGateway (UpdateAssetProxyGatewayRequest) returns (Reply) {
    option (google.api.http) = {
      put: "/v1/asset/proxy-gateways"
      body: "*"
    };
  }

  rpc DeleteAssetProxyGateway (DeleteAssetProxyGatewayRequest) returns (Reply) {
    option (google.api.http) = {
      delete: "/v1/asset/proxy-gateways/{id}"
    };
  }


  rpc GetAssetAdminUserList (GetAssetAdminUserListRequest) returns (GetAssetAdminUserListReply) {
    option (google.api.http) = {
      get: "/v1/asset/admin-users"
    };
  }
  rpc CreateAssetAdminUser (CreateAssetAdminUserRequest) returns (Reply) {
    option (google.api.http) = {
      post: "/v1/asset/admin-users"
      body: "*"
    };
  }
  rpc UpdateAssetAdminUser (UpdateAssetAdminUserRequest) returns (Reply) {
    option (google.api.http) = {
      put: "/v1/asset/admin-users"
      body: "*"
    };
  }

  rpc DeleteAssetAdminUser (DeleteAssetAdminUserRequest) returns (Reply) {
    option (google.api.http) = {
      delete: "/v1/asset/admin-users/{id}"
    };
  }
}

message GetAssetProxyGatewaySelectReply {
  message AssetProxyGateway {
    int32 id = 1;
    string organizationId = 2;
    string name = 3;
    string ip = 4;
    int64 port = 5;
    string username = 6;
    string comment = 7;
    string idc = 8;
  }
  repeated AssetProxyGateway data = 1;
}

message UserAssetAliasRequest {
  string key = 1 [(validate.rules).string.min_len = 1];
  string alias = 2 [(validate.rules).string.min_len = 1];
}

message ListUserWorkSpaceReply {
  message WorkSpace {
    string key = 1;
    string label = 2;
  }
  repeated WorkSpace data = 1;
}

message CreateAssetRequest {
  string name = 1 [(validate.rules).string.min_len = 1];
  string idc = 2 [(validate.rules).string.min_len = 1];
  string assetIp = 3 [(validate.rules).string.min_len = 1];
  string active = 4 [(validate.rules).string.min_len = 1];
  string platform = 5 [(validate.rules).string.min_len = 1];
  int64 proxyGatewayId = 6 [(validate.rules).int64.gt = 0];
  string protocol = 7 [(validate.rules).string.min_len = 1];
  string assetSource = 8 [(validate.rules).string.min_len = 1];
  string os = 9 [(validate.rules).string.min_len = 1];
  string organizationId = 11 [(validate.rules).string.min_len = 1];
  string comment = 12;
}

message UpdateAseetRequest {
  string uuid = 1 [(validate.rules).string.min_len = 1];
  string name = 2 [(validate.rules).string.min_len = 1];
  string assetIp = 3 [(validate.rules).string.min_len = 1];
  string active = 4 [(validate.rules).string.min_len = 1];
  string platform = 5 [(validate.rules).string.min_len = 1];
  int64 proxyGatewayId = 6 [(validate.rules).int64.gt = 0];
  string protocol = 7 [(validate.rules).string.min_len = 1];
  string os = 8 [(validate.rules).string.min_len = 1];
  string organizationId = 10 [(validate.rules).string.min_len = 1];
  string comment = 11 [(validate.rules).string.min_len = 1];
  string idc = 12 [(validate.rules).string.min_len = 1];
}

message DeleteAssetRequest {
  string uuid = 1;
}


message ListAssetRouteRequest{
  string organizationId = 1;
}

message ListAssetRouteReply {
  message Children {
    string key = 1;
    string title = 2;
    bool favorite = 3;
    string ip = 4;
    string organizationId = 5;
  }
  message Route {
    string key = 1;
    string title = 2;
    repeated Children  children = 3;
  }
  repeated Route routers = 1;
}

message Reply {
  string message = 1;
}

message UserAssetFavoriteRequest {
  string assetIp = 1 [(validate.rules).string.min_len = 1];
  string action = 2 [(validate.rules).string.min_len = 1];
}

message GetAssetListRequest {
  int64 pageSize = 1 [(validate.rules).int64.gt = 0];
  int64 pageNo = 2 [(validate.rules).int64.gt = -1];
  string searchText = 3;
  string idc = 4;
  string organizationId = 5;
  int64  proxyGatewayId = 6;
}

message GetAssetListReply {
  message Asset {
    string uuid = 1;
    string name = 2;
    string assetIp = 3;
    string active = 4;
    string platform = 5;
    int64 proxyGatewayId = 6;
    string protocol = 7;
    string os = 8;
    string organizationId = 10;
    string comment = 11;
    string createUser = 12;
    string idc = 13;
    string createdAt = 14;
    string updatedAt = 15;
  }
  repeated Asset data = 1;
  int64 pageNo = 2;
  int64 pageSize = 3;
  int64 totalCount = 4;
  int64 totalPage = 5;
}

message GetAssetListByIpRequest {
  int64 pageSize = 1 [(validate.rules).int64.gt = 0];
  int64 pageNo = 2 [(validate.rules).int64.gt = -1];
  string ip = 3;
  string organizationId = 4;
}
message GetAssetListByIpReply {
  message Asset {
    int32 id = 1;
    string name = 2;
    string assetIp = 3;
    string organizationId = 4;
    string uuid = 5;
  }
  repeated Asset data = 1;
  int64 pageNo = 2;
  int64 pageSize = 3;
  int64 totalCount = 4;
  int64 totalPage = 5;
}

message GetAssetProxyGatewayListRequest {
  int64 pageSize = 1 [(validate.rules).int64.gt = 0];
  int64 pageNo = 2 [(validate.rules).int64.gt = -1];
  string searchText = 3;
}

message CreateAssetProxyGatewayRequest {
  string name = 2 [(validate.rules).string.min_len = 1];
  string organizationId = 3 [(validate.rules).string.min_len = 1];
  string ip = 4 [(validate.rules).string.min_len = 1];
  int64 port = 1 [(validate.rules).int64.gt = 0];
  string username = 6 [(validate.rules).string.min_len = 1];
  string password = 7;
  string privateKey = 8;
  string comment = 9;
  string idc = 10 [(validate.rules).string.min_len = 1];
}

message UpdateAssetProxyGatewayRequest {
  int32 id = 1;
  string name = 2 [(validate.rules).string.min_len = 1];
  string organizationId = 3 [(validate.rules).string.min_len = 1];
  string ip = 4 [(validate.rules).string.min_len = 1];
  int64 port = 5 [(validate.rules).int64.gt = 0];
  string username = 6 [(validate.rules).string.min_len = 1];
  string password = 7;
  string privateKey = 8;
  string comment = 9;
  string idc = 10 [(validate.rules).string.min_len = 1];
}

message DeleteAssetProxyGatewayRequest {
  int32 id = 1;
}


message GetAssetProxyGatewayListReply {
  message AssetProxyGateway {
    int32 id = 1;
    string organizationId = 2;
    string name = 3;
    string ip = 4;
    int64 port = 5;
    string username = 6;
    string comment = 7;
    string idc = 8;
  }
  repeated AssetProxyGateway data = 1;
  int64 pageNo = 2;
  int64 pageSize = 3;
  int64 totalCount = 4;
  int64 totalPage = 5;
}




message GetAssetAdminUserListRequest {
  int64 pageSize = 1 [(validate.rules).int64.gt = 0];
  int64 pageNo = 2 [(validate.rules).int64.gt = -1];
  string searchText = 3;
}

message CreateAssetAdminUserRequest {
  string organizationId = 1 [(validate.rules).string.min_len = 1];
  string username = 2 [(validate.rules).string.min_len = 1];
  string password = 3;
  string privateKey = 4 [(validate.rules).string.min_len = 1];
  string comment = 5;
}

message UpdateAssetAdminUserRequest {
  int32 id = 1;
  string organizationId = 2 [(validate.rules).string.min_len = 1];
  string username = 3 [(validate.rules).string.min_len = 1];
  string password = 4;
  string privateKey = 5 [(validate.rules).string.min_len = 1];
  string comment = 6;
}

message DeleteAssetAdminUserRequest {
  int32 id = 1;
}


message GetAssetAdminUserListReply {
  message AssetAdminUser {
    int32 id = 1;
    string organizationId = 2;
    string username = 3;
  }
  repeated AssetAdminUser data = 1;
  int64 pageNo = 2;
  int64 pageSize = 3;
  int64 totalCount = 4;
  int64 totalPage = 5;
}