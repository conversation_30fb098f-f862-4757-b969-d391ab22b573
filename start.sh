export GIN_MODE=release
export ENV=test
# 检查进程是否存在
if pgrep -x "ctm-backend" >/dev/null; then
    echo "restart ctm-backend";
    # 获取进程ID
    pid=$(pgrep -x "ctm-backend")
    
    kill -15 $pid
    echo "优雅杀死进程 $pid"

    sleep 2
    nohup /opt/ctm-backend/ctm-backend > /opt/ctm-backend/logs/request.log 2>&1 &
    # 获取新进程ID
    nid=$(pgrep -x "ctm-backend")
    echo "新进程ID $nid"
else
    echo "Start ctm-backend";
    nohup /opt/ctm-backend/ctm-backend > /opt/ctm-backend/logs/request.log 2>&1 &
fi
